{"mt5_path": "C:\\Program Files\\Tickmill MT5 Terminal\\terminal64.exe", "mt5_login": "55666086", "mt5_password": "668899asd!A", "mt5_server": "Tickmill-Live", "server_port": 9999, "log_level": "DEBUG", "enable_trading": true, "default_volume": 0.1, "default_sl_points": 400, "default_tp_points": 400, "sl_tp_calculation_method": "points", "symbol_volumes": {"BTCUSD": 0.5, "ETHUSD": 8.0, "XAUUSD": 0.1, "GBPJPY": 0.2, "BRENT": 0.1, "XTIUSD": 0.1, "GBPUSD": 0.2, "DXY": 0.1}, "symbol_sl_points": {"BTCUSD": 9000, "ETHUSD": 500, "XAUUSD": 700, "GBPJPY": 150, "BRENT": 200, "XTIUSD": 200, "GBPUSD": 100, "DXY": 50}, "symbol_tp_points": {"BTCUSD": 12000, "ETHUSD": 800, "XAUUSD": 800, "GBPJPY": 300, "BRENT": 300, "XTIUSD": 300, "GBPUSD": 150, "DXY": 100}, "symbol_max_active_orders": {"BTCUSD": 2, "ETHUSD": 8, "XAUUSD": 2, "GBPJPY": 2, "BRENT": 2, "XTIUSD": 2, "GBPUSD": 2, "DXY": 2}, "symbol_map": {"BTCUSD": "BTCUSD", "ETHUSD": "ETHUSD", "XAUUSD": "XAUUSD", "GBPJPY": "GBPJPY", "BRENT": "BRENT", "XTIUSD": "XTIUSD", "GBPUSD": "GBPUSD", "DXY": "DXY"}, "bark_url": "https://api.day.app", "bark_device_key": "RRHtNcrzxBBsuUoag2j4oY", "bark_device_key_2": "MrdAcDtjvBBp3kCVJTixV5", "bark_title": "MT5交易执行通知", "bark_level": "active", "bark_volume": 1.0, "bark_badge": 1, "call": "1", "bark_sound": "bell.caf", "web_port": 8080, "admin_username": "admin", "admin_password": "668899asd", "enable_time_filter": false, "trading_times": {"0": {"enabled": true, "period1": {"start": "22:00", "end": "23:59"}, "period2": {"start": "00:00", "end": "02:00"}}, "1": {"enabled": true, "period1": {"start": "09:00", "end": "12:00"}, "period2": {"start": "13:30", "end": "16:30"}}, "2": {"enabled": true, "period1": {"start": "09:00", "end": "12:00"}, "period2": {"start": "13:30", "end": "16:30"}}, "3": {"enabled": true, "period1": {"start": "09:00", "end": "12:00"}, "period2": {"start": "13:30", "end": "16:30"}}, "4": {"enabled": true, "period1": {"start": "04:00", "end": "13:00"}, "period2": {"start": "15:30", "end": "23:30"}}, "5": {"enabled": true, "period1": {"start": "09:00", "end": "12:00"}, "period2": {"start": "13:30", "end": "16:30"}}, "6": {"enabled": false, "period1": {"start": "00:00", "end": "00:00"}, "period2": {"start": "00:00", "end": "00:00"}}}, "enabled_symbols": {"XAUUSD": false, "GBPJPY": false, "GBPUSD": false, "ETHUSD": true, "BTCUSD": false, "BRENT": false, "XTIUSD": false, "DXY": false}, "auto_close": {"total": {"enabled": false, "profit_usd": null, "loss_usd": null}}, "order_auto_close": {}, "bark_notifications": {"signal_received": true, "signal_processing": true, "trade_execution": true, "trade_closed": true, "error": true}, "balance_monitoring": {"enabled": true, "balance_change_notification": {"enabled": true, "min_change_amount": 1.0}, "profit_loss_notification": {"enabled": true, "profit_threshold": 50.0, "loss_threshold": 50.0}, "periodic_balance_notification": {"enabled": true, "mode": "custom_times", "custom_times": ["00:00", "01:00", "08:00", "10:00", "12:00", "16:00", "18:00", "20:00"], "timezone": "Asia/Shanghai", "interval_hours": 8, "start_time": "00:00", "end_time": "23:59"}}}
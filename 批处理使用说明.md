# TradingView自动交易系统 - 统一管理器

## 📁 文件说明

### 🚀 统一管理器

#### `TradingSystem.bat` - 一站式管理工具
- **用途**: 集成所有系统管理功能的统一界面
- **特点**:
  - 美观的图形界面
  - 12个核心功能
  - 智能检测和诊断
  - 一键操作

### 🎮 功能菜单

#### 🚀 系统操作
1. **快速启动系统** - 一键启动Web和Webhook服务
2. **重启系统** - 停止并重新启动所有服务
3. **停止系统** - 安全停止所有相关进程

#### 🔧 系统管理
4. **安装/更新依赖** - 管理Python包依赖
5. **检查系统状态** - 全面诊断系统运行状态
6. **实时监控** - 5秒刷新的实时状态监控

#### 📁 文件管理
7. **查看日志文件** - 浏览和查看系统日志
8. **备份配置** - 自动备份重要配置文件
9. **恢复配置** - 从备份恢复配置文件

#### 🛠️ 维护工具
10. **清理临时文件** - 清理缓存和临时文件
11. **创建桌面快捷方式** - 一键创建桌面快捷方式
12. **打开Web界面** - 直接打开管理界面

## 🚀 快速开始

### 1. 首次使用
```batch
# 双击运行统一管理器
TradingSystem.bat

# 选择功能菜单:
# 4. 安装/更新依赖 (首次运行)
# 11. 创建桌面快捷方式 (可选)
# 1. 快速启动系统
```

### 2. 日常使用
```batch
# 方式1: 双击桌面快捷方式 "TradingView交易系统"
# 方式2: 双击项目目录中的 TradingSystem.bat

# 然后选择: 1. 快速启动系统
```

### 3. 系统管理
```batch
# 运行 TradingSystem.bat
# 使用菜单选项 4-12 进行各种管理操作
```

## 📋 使用流程

### 首次部署流程
1. **运行统一管理器**: 双击 `TradingSystem.bat`
2. **安装依赖**: 选择 `4. 安装/更新依赖`
3. **创建快捷方式**: 选择 `11. 创建桌面快捷方式` (可选)
4. **配置MT5连接**: 确保MT5已安装并允许DLL导入
5. **启动系统**: 选择 `1. 快速启动系统`
6. **配置交易参数**: 通过Web界面配置交易对和风险参数
7. **配置TradingView**: 设置Webhook地址为 `http://localhost:7000/webhook/tradingview`

### 日常使用流程
1. **启动管理器**: 双击桌面快捷方式或 `TradingSystem.bat`
2. **启动系统**: 选择 `1. 快速启动系统`
3. **访问管理界面**: 选择 `12. 打开Web界面` 或访问 http://localhost:5000
4. **监控系统**: 选择 `6. 实时监控` (可选)
5. **停止系统**: 选择 `3. 停止系统` 或 Ctrl+C

### 维护流程
1. **检查状态**: 选择 `5. 检查系统状态`
2. **备份配置**: 选择 `8. 备份配置`
3. **查看日志**: 选择 `7. 查看日志文件`
4. **更新依赖**: 选择 `4. 安装/更新依赖`
5. **清理系统**: 选择 `10. 清理临时文件`

## 🔧 系统要求

### 软件要求
- **操作系统**: Windows 10/11
- **Python**: 3.8 或更高版本
- **MetaTrader 5**: 最新版本
- **网络**: 稳定的互联网连接

### 硬件要求
- **内存**: 最少4GB RAM
- **存储**: 最少1GB可用空间
- **处理器**: 支持多线程的现代处理器

## 🌐 访问地址

### Web管理界面
- **地址**: http://localhost:5000
- **功能**: 系统配置、交易管理、状态监控

### Webhook接口
- **地址**: http://localhost:7000/webhook/tradingview
- **用途**: 接收TradingView信号

## 📊 端口说明

| 端口 | 服务 | 说明 |
|------|------|------|
| 5000 | Web服务 | 管理界面和API |
| 7000 | Webhook服务 | 接收TradingView信号 |

## 🛠️ 故障排除

### 常见问题

#### Python未找到
- **解决**: 安装Python 3.8+，确保添加到PATH
- **下载**: https://www.python.org/downloads/

#### 端口被占用
- **解决**: 使用管理器的停止功能，或重启计算机

#### MT5连接失败
- **解决**: 
  1. 确保MT5已安装
  2. 在MT5中启用"允许DLL导入"
  3. 检查MT5账户登录状态

#### 依赖包安装失败
- **解决**: 
  1. 以管理员身份运行安装脚本
  2. 检查网络连接
  3. 手动安装: `pip install -r requirements.txt`

### 日志文件位置
- **路径**: `logs/` 目录
- **查看**: 使用管理器的日志查看功能

### 配置文件位置
- **路径**: `config/` 目录
- **备份**: 使用管理器的备份功能

## 📞 技术支持

### 自助诊断
1. 运行 `TradingSystem.bat`
2. 选择 `5. 检查系统状态`
3. 查看详细诊断结果

### 日志分析
1. 运行 `TradingSystem.bat`
2. 选择 `7. 查看日志文件`
3. 查看错误信息和系统日志

## 🔄 更新说明

### 系统更新
1. 运行 `TradingSystem.bat`
2. 选择 `8. 备份配置` 备份当前配置
3. 下载新版本文件覆盖旧文件
4. 选择 `4. 安装/更新依赖` 更新依赖
5. 选择 `9. 恢复配置` 恢复配置文件

### 依赖更新
1. 运行 `TradingSystem.bat`
2. 选择 `4. 安装/更新依赖`
3. 等待更新完成并验证

## ⚠️ 重要提醒

1. **备份重要**: 定期备份配置文件
2. **安全第一**: 不要在生产环境中使用默认配置
3. **监控必要**: 定期检查系统运行状态
4. **日志重要**: 保留足够的日志用于故障排除
5. **网络稳定**: 确保网络连接稳定可靠

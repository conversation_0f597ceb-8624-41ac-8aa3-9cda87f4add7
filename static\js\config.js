/**
 * 配置管理模块
 */

class ConfigManager {
    constructor() {
        this.symbolConfigs = {};
        this.systemConfig = {};
        this.barkConfig = {};
        this.riskConfig = {};
    }

    /**
     * 加载所有配置
     */
    async loadAllConfigs() {
        try {
            await Promise.all([
                this.loadSymbolConfigs(),
                this.loadSystemConfig(),
                this.loadBarkConfig(),
                this.loadRiskConfig()
            ]);
        } catch (error) {
            console.error('加载配置失败:', error);
            ui.showAlert('danger', '加载配置失败: ' + error.message);
        }
    }

    /**
     * 加载交易对配置
     */
    async loadSymbolConfigs() {
        try {
            const response = await ui.apiCall('/api/config/symbols');
            this.symbolConfigs = response.data;
            this.renderSymbolConfigs();
        } catch (error) {
            console.error('加载交易对配置失败:', error);
            throw error;
        }
    }

    /**
     * 加载系统配置
     */
    async loadSystemConfig() {
        try {
            const response = await ui.apiCall('/api/config/system');
            this.systemConfig = response.data;
        } catch (error) {
            console.error('加载系统配置失败:', error);
            throw error;
        }
    }

    /**
     * 加载Bark配置
     */
    async loadBarkConfig() {
        try {
            const response = await ui.apiCall('/api/config/bark');
            this.barkConfig = response.data;
        } catch (error) {
            console.error('加载Bark配置失败:', error);
            throw error;
        }
    }

    /**
     * 加载风险控制配置
     */
    async loadRiskConfig() {
        try {
            const response = await ui.apiCall('/api/config/risk');
            this.riskConfig = response.data;
        } catch (error) {
            console.error('加载风险控制配置失败:', error);
            throw error;
        }
    }

    /**
     * 渲染交易对配置表格
     */
    renderSymbolConfigs() {
        const tbody = document.getElementById('symbolsConfigBody');
        if (!tbody) return;

        const symbols = [
            { symbol: 'XAUUSD', name: '黄金/美元', category: 'precious_metals' },
            { symbol: 'BTCUSD', name: '比特币/美元', category: 'crypto' },
            { symbol: 'ETHUSD', name: '以太坊/美元', category: 'crypto' },
            { symbol: 'SOLUSD', name: 'Solana/美元', category: 'crypto' },
            { symbol: 'EURUSD', name: '欧元/美元', category: 'forex' },
            { symbol: 'GBPUSD', name: '英镑/美元', category: 'forex' },
            { symbol: 'USDJPY', name: '美元/日元', category: 'forex' },
            { symbol: 'AUDUSD', name: '澳元/美元', category: 'forex' },
            { symbol: 'USDCAD', name: '美元/加元', category: 'forex' },
            { symbol: 'USDCHF', name: '美元/瑞郎', category: 'forex' },
            { symbol: 'NZDUSD', name: '纽元/美元', category: 'forex' },
            { symbol: 'XAGUSD', name: '白银/美元', category: 'precious_metals' },
            { symbol: 'USOIL', name: '美原油', category: 'commodities' }
        ];

        tbody.innerHTML = '';

        symbols.forEach(symbolInfo => {
            const config = this.symbolConfigs[symbolInfo.symbol] || this.getDefaultConfig(symbolInfo.symbol);
            const row = this.createSymbolConfigRow(symbolInfo, config);
            tbody.appendChild(row);
        });
    }

    /**
     * 创建交易对配置行
     */
    createSymbolConfigRow(symbolInfo, config) {
        const row = document.createElement('tr');
        
        const categoryBadge = this.getCategoryBadge(symbolInfo.category);
        const enabledChecked = config.enabled ? 'checked' : '';

        row.innerHTML = `
            <td class="d-none d-md-table-cell">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" ${enabledChecked} 
                           onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'enabled', this.checked)">
                </div>
            </td>
            <td>
                <div class="d-flex flex-column">
                    <strong>${symbolInfo.symbol}</strong>
                    <small class="text-muted">${symbolInfo.name}</small>
                    <div class="d-md-none mt-1">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" ${enabledChecked} 
                                   onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'enabled', this.checked)">
                            <label class="form-check-label">启用</label>
                        </div>
                    </div>
                </div>
            </td>
            <td class="d-none d-lg-table-cell">${categoryBadge}</td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${config.lot_size || 0.01}" step="0.01" min="0.01"
                       onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'lot_size', parseFloat(this.value))">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${config.stop_loss_usd || 50}" step="1" min="1"
                       onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'stop_loss_usd', parseInt(this.value))">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       value="${config.take_profit_usd || 100}" step="1" min="1"
                       onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'take_profit_usd', parseInt(this.value))">
            </td>
            <td class="d-none d-md-table-cell">
                <input type="number" class="form-control form-control-sm" 
                       value="${config.signal_timeout_seconds || 180}" step="30" min="30"
                       onchange="configManager.updateSymbolConfig('${symbolInfo.symbol}', 'signal_timeout_seconds', parseInt(this.value))">
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary btn-sm" 
                            onclick="configManager.showSymbolDetails('${symbolInfo.symbol}')" 
                            title="详细设置">
                        <i class="bi bi-gear"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    /**
     * 获取分类徽章
     */
    getCategoryBadge(category) {
        const badges = {
            'crypto': '<span class="badge bg-warning">加密货币</span>',
            'forex': '<span class="badge bg-primary">外汇</span>',
            'precious_metals': '<span class="badge bg-success">贵金属</span>',
            'commodities': '<span class="badge bg-info">商品</span>'
        };
        return badges[category] || '<span class="badge bg-secondary">其他</span>';
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig(symbol) {
        const defaults = {
            'XAUUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 50, take_profit_usd: 100, signal_timeout_seconds: 180 },
            'BTCUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 100, take_profit_usd: 200, signal_timeout_seconds: 180 },
            'ETHUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 80, take_profit_usd: 160, signal_timeout_seconds: 180 },
            'SOLUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 60, take_profit_usd: 120, signal_timeout_seconds: 180 },
            'EURUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 30, take_profit_usd: 60, signal_timeout_seconds: 180 },
            'GBPUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 35, take_profit_usd: 70, signal_timeout_seconds: 180 },
            'USDJPY': { enabled: true, lot_size: 0.01, stop_loss_usd: 30, take_profit_usd: 60, signal_timeout_seconds: 180 },
            'AUDUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 25, take_profit_usd: 50, signal_timeout_seconds: 180 },
            'USDCAD': { enabled: true, lot_size: 0.01, stop_loss_usd: 25, take_profit_usd: 50, signal_timeout_seconds: 180 },
            'USDCHF': { enabled: true, lot_size: 0.01, stop_loss_usd: 25, take_profit_usd: 50, signal_timeout_seconds: 180 },
            'NZDUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 25, take_profit_usd: 50, signal_timeout_seconds: 180 },
            'XAGUSD': { enabled: true, lot_size: 0.01, stop_loss_usd: 40, take_profit_usd: 80, signal_timeout_seconds: 180 },
            'USOIL': { enabled: true, lot_size: 0.01, stop_loss_usd: 45, take_profit_usd: 90, signal_timeout_seconds: 180 }
        };
        return defaults[symbol] || { enabled: false, lot_size: 0.01, stop_loss_usd: 50, take_profit_usd: 100, signal_timeout_seconds: 180 };
    }

    /**
     * 更新交易对配置
     */
    async updateSymbolConfig(symbol, key, value) {
        try {
            if (!this.symbolConfigs[symbol]) {
                this.symbolConfigs[symbol] = this.getDefaultConfig(symbol);
            }
            
            this.symbolConfigs[symbol][key] = value;
            
            const response = await ui.apiCall(`/api/config/symbols/${symbol}`, 'PUT', {
                [key]: value
            });
            
            if (response.success) {
                ui.showAlert('success', `${symbol} 配置已更新`, 2000);
            } else {
                throw new Error(response.error || '更新失败');
            }
        } catch (error) {
            console.error('更新交易对配置失败:', error);
            ui.showAlert('danger', '更新配置失败: ' + error.message);
            // 重新加载配置以恢复原值
            this.loadSymbolConfigs();
        }
    }

    /**
     * 显示交易对详细设置
     */
    showSymbolDetails(symbol) {
        const config = this.symbolConfigs[symbol] || this.getDefaultConfig(symbol);
        
        // 这里可以实现详细配置模态框
        ui.showAlert('info', `${symbol} 详细配置功能开发中...`);
    }

    /**
     * 批量操作
     */
    async enableAllSymbols() {
        try {
            const symbols = Object.keys(this.symbolConfigs);
            for (const symbol of symbols) {
                await this.updateSymbolConfig(symbol, 'enabled', true);
            }
            this.renderSymbolConfigs();
            ui.showAlert('success', '已启用所有交易对');
        } catch (error) {
            ui.showAlert('danger', '批量启用失败: ' + error.message);
        }
    }

    async disableAllSymbols() {
        try {
            const symbols = Object.keys(this.symbolConfigs);
            for (const symbol of symbols) {
                await this.updateSymbolConfig(symbol, 'enabled', false);
            }
            this.renderSymbolConfigs();
            ui.showAlert('success', '已禁用所有交易对');
        } catch (error) {
            ui.showAlert('danger', '批量禁用失败: ' + error.message);
        }
    }

    async resetToDefaults() {
        if (confirm('确定要恢复所有交易对的默认配置吗？')) {
            try {
                // 实现恢复默认配置的逻辑
                ui.showAlert('info', '恢复默认配置功能开发中...');
            } catch (error) {
                ui.showAlert('danger', '恢复默认配置失败: ' + error.message);
            }
        }
    }
}

// 全局配置管理器实例
let configManager;

// 初始化配置管理器
document.addEventListener('DOMContentLoaded', function() {
    configManager = new ConfigManager();
});

// 全局函数供HTML调用
function enableAllSymbols() {
    if (configManager) configManager.enableAllSymbols();
}

function disableAllSymbols() {
    if (configManager) configManager.disableAllSymbols();
}

function resetToDefaults() {
    if (configManager) configManager.resetToDefaults();
}

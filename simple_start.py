#!/usr/bin/env python3
"""
简化的系统启动脚本 - 用于调试
"""
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🚀 启动TradingView自动交易系统...")
        print("📍 当前工作目录:", os.getcwd())
        print("🐍 Python版本:", sys.version)
        
        # 测试导入
        print("📦 测试模块导入...")
        from flask import Flask
        print("✅ Flask导入成功")
        
        from app import create_app
        print("✅ App模块导入成功")
        
        # 创建应用
        print("🏗️ 创建Flask应用...")
        app = create_app('development')
        print("✅ Flask应用创建成功")
        
        # 启动应用
        print("🌐 启动Web服务器...")
        print("📍 访问地址: http://127.0.0.1:7000")
        print("🔑 默认登录: admin / admin123")
        print("⚠️ 按 Ctrl+C 停止服务")
        
        app.run(
            host='0.0.0.0',
            port=7000,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
调试启动脚本
"""
import sys
import os
import traceback
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🚀 开始启动系统...")
        print(f"📍 工作目录: {os.getcwd()}")
        
        # 测试基础导入
        print("📦 测试基础模块导入...")
        from flask import Flask
        print("✅ Flask导入成功")
        
        # 测试应用创建
        print("🏗️ 测试应用创建...")
        from app import create_app
        print("✅ App模块导入成功")
        
        app = create_app('development')
        print("✅ Flask应用创建成功")
        
        # 测试路由注册
        print("🛣️ 检查路由注册...")
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"📋 注册的路由数量: {len(routes)}")
        for route in routes[:10]:  # 显示前10个路由
            print(f"   {route}")
        if len(routes) > 10:
            print(f"   ... 还有 {len(routes) - 10} 个路由")
        
        # 启动应用
        print("🌐 启动Web服务器...")
        print("📍 访问地址: http://127.0.0.1:7000")
        print("🔑 默认登录: admin / admin123")
        print("⚠️ 按 Ctrl+C 停止服务")
        print("=" * 50)
        
        app.run(
            host='0.0.0.0',
            port=7000,
            debug=False,  # 关闭调试模式
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 收到停止信号，正在关闭系统...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()

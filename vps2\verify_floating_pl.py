#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证浮动盈亏功能的简单测试
"""

import sqlite3
import sys
import os

def check_database():
    """检查数据库表结构"""
    print("=== 检查数据库结构 ===")
    
    db_path = "trading_data.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='profit_loss_rules'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ profit_loss_rules 表存在")
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(profit_loss_rules)")
            columns = cursor.fetchall()
            print("✅ 表结构:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
            
            # 检查默认数据
            cursor.execute("SELECT * FROM profit_loss_rules WHERE rule_type='total'")
            total_rule = cursor.fetchone()
            if total_rule:
                print("✅ 总规则记录存在:")
                print(f"   - ID: {total_rule[0]}")
                print(f"   - 启用状态: {'开启' if total_rule[2] else '关闭'}")
                print(f"   - 盈利阈值: {total_rule[3]} USD")
                print(f"   - 亏损阈值: {total_rule[4]} USD")
            else:
                print("❌ 总规则记录不存在")
            
            return True
        else:
            print("❌ profit_loss_rules 表不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False
    finally:
        conn.close()

def check_floating_pl_module():
    """检查浮动盈亏监控模块"""
    print("\n=== 检查浮动盈亏监控模块 ===")
    
    try:
        import floating_pl_monitor
        print("✅ floating_pl_monitor 模块导入成功")
        
        # 检查类是否存在
        if hasattr(floating_pl_monitor, 'FloatingPLMonitor'):
            print("✅ FloatingPLMonitor 类存在")
            
            # 创建实例测试
            monitor = floating_pl_monitor.FloatingPLMonitor()
            print("✅ FloatingPLMonitor 实例创建成功")
            
            # 检查方法是否存在
            methods = ['start', 'stop', 'is_running', 'get_status']
            for method in methods:
                if hasattr(monitor, method):
                    print(f"✅ 方法 {method} 存在")
                else:
                    print(f"❌ 方法 {method} 不存在")
            
            return True
        else:
            print("❌ FloatingPLMonitor 类不存在")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 模块检查失败: {e}")
        return False

def check_web_interface_integration():
    """检查web界面集成"""
    print("\n=== 检查Web界面集成 ===")
    
    try:
        with open('web_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键API端点
        api_endpoints = [
            '/api/total_floating_pl',
            '/api/profit_loss_rules/total',
            '/api/profit_loss_rules/individual',
            '/api/floating_pl_monitor/status',
            '/api/floating_pl_monitor/control'
        ]
        
        for endpoint in api_endpoints:
            if endpoint in content:
                print(f"✅ API端点 {endpoint} 已集成")
            else:
                print(f"❌ API端点 {endpoint} 未找到")
        
        # 检查浮动盈亏监控导入
        if 'floating_pl_monitor' in content:
            print("✅ floating_pl_monitor 模块已导入到web_interface")
        else:
            print("❌ floating_pl_monitor 模块未导入到web_interface")
        
        return True
        
    except Exception as e:
        print(f"❌ Web界面检查失败: {e}")
        return False

def check_frontend_template():
    """检查前端模板"""
    print("\n=== 检查前端模板 ===")
    
    try:
        template_path = os.path.join('templates', 'orders.html')
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键元素
        key_elements = [
            '浮动盈亏控制',
            'floating-pl-panel',
            'total-floating-pl',
            'profit-threshold',
            'loss-threshold',
            'individual-pl-modal'
        ]
        
        for element in key_elements:
            if element in content:
                print(f"✅ 前端元素 {element} 存在")
            else:
                print(f"❌ 前端元素 {element} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端模板检查失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证浮动盈亏功能")
    
    success_count = 0
    total_checks = 4
    
    if check_database():
        success_count += 1
    
    if check_floating_pl_module():
        success_count += 1
    
    if check_web_interface_integration():
        success_count += 1
    
    if check_frontend_template():
        success_count += 1
    
    print(f"\n📊 验证结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("✅ 所有功能验证通过！浮动盈亏功能已正确集成。")
    else:
        print(f"❌ 还有 {total_checks - success_count} 项需要修复。")

if __name__ == '__main__':
    main()

"""
MetaTrader5 交易管理系统主应用
"""
import os
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入核心模块
from config import config
from database.manager import db_manager
from auth.manager import auth_manager, require_auth, require_login
from webhook.handler import webhook_handler
from trading.strategy import trading_strategy
from trading.engine import trading_strategy_engine
from trading.config_manager import config_manager
from notifications.bark import bark_manager
from mt5.connection import mt5_connection, auto_connect
from risk.manager import risk_manager
from trading.sl_tp_monitor import sl_tp_monitor
from api.routes import api_bp
from utils.logger import logger

def create_app(config_name='default'):
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 启用CORS
    CORS(app)

    # 初始化数据库
    with app.app_context():
        db_manager.init_database()

    # 注册API蓝图
    app.register_blueprint(api_bp)

    # 注册路由
    register_routes(app)
    
    return app

def register_routes(app):
    """注册所有路由"""
    
    # 静态文件路由
    @app.route('/')
    def index():
        """主页 - 认证检查"""
        return render_template('auth_check.html')

    @app.route('/dashboard')
    def dashboard():
        """仪表板页面"""
        return render_template('simple_dashboard.html')

    @app.route('/debug')
    def debug():
        """调试页面"""
        return render_template('dashboard_debug.html')
    
    @app.route('/login')
    def login_page():
        """登录页面"""
        return render_template('login.html')
    
    # API路由 - 认证相关
    @app.route('/api/auth/login', methods=['POST'])
    def login():
        """用户登录"""
        data = request.json
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({"success": False, "message": "用户名和密码不能为空"}), 400
        
        # 获取客户端信息
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        
        # 认证用户
        result = auth_manager.authenticate_user(username, password, ip_address, user_agent)
        
        if result["success"]:
            return jsonify(result), 200
        else:
            return jsonify(result), 401
    
    @app.route('/api/auth/logout', methods=['POST'])
    @require_auth
    def logout():
        """用户登出"""
        session_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        result = auth_manager.logout_user(session_token)
        return jsonify(result), 200
    
    @app.route('/api/auth/validate', methods=['GET'])
    @require_auth
    def validate_session():
        """验证会话有效性"""
        return jsonify({
            "valid": True,
            "user": request.current_user
        }), 200

    # API路由 - 警报历史
    @app.route('/api/alerts', methods=['GET'])
    @require_auth
    def get_alerts():
        """获取警报历史"""
        try:
            # 获取查询参数
            page = int(request.args.get('page', 1))
            limit = int(request.args.get('limit', 30))
            symbol = request.args.get('symbol')
            signal_type = request.args.get('signal_type')
            time_range = request.args.get('time_range')

            # 计算偏移量
            offset = (page - 1) * limit

            # 获取警报历史
            alerts = webhook_handler.get_alert_history(
                limit=limit,
                offset=offset,
                symbol=symbol,
                signal_type=signal_type,
                time_range=time_range
            )

            # 获取总数
            total = webhook_handler.get_alert_count(
                symbol=symbol,
                signal_type=signal_type,
                time_range=time_range
            )

            return jsonify({
                "success": True,
                "data": {
                    "alerts": alerts,
                    "total": total,
                    "page": page,
                    "limit": limit
                }
            }), 200

        except Exception as e:
            logger.error(f"获取警报历史失败: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500

    @app.route('/api/alerts/<alert_id>/raw', methods=['GET'])
    @require_auth
    def get_alert_raw_data(alert_id):
        """获取警报原始JSON数据"""
        try:
            raw_data = webhook_handler.get_alert_raw_data(alert_id)
            if raw_data:
                return jsonify({
                    "success": True,
                    "data": {
                        "alert_id": alert_id,
                        "raw_data": raw_data
                    }
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": "警报不存在"
                }), 404

        except Exception as e:
            logger.error(f"获取警报原始数据失败: {e}")
            return jsonify({
                "success": False,
                "error": str(e)
            }), 500



    # API路由 - Webhook
    @app.route('/webhook/tradingview', methods=['POST'])
    def receive_tradingview_alert():
        """接收TradingView警报"""
        try:
            alert_data = request.json
            logger.info(f"收到TradingView警报: {alert_data}")

            # 执行交易策略（这会处理警报、保存数据、执行交易）
            result = trading_strategy.execute_strategy(alert_data)

            return jsonify(result), 200 if result["success"] else 400

        except Exception as e:
            logger.error(f"Webhook处理失败: {e}")
            return jsonify({"success": False, "error": str(e)}), 500



    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({"error": "页面未找到"}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({"error": "服务器内部错误"}), 500

def init_system():
    """初始化系统"""
    try:
        logger.info("正在初始化交易系统...")
        
        # 创建默认管理员用户
        result = auth_manager.create_admin_user("admin", "admin123", "<EMAIL>")
        if result["success"]:
            logger.info("默认管理员用户创建成功")
        
        # 连接MT5
        if auto_connect():
            logger.info("MT5连接成功")
        else:
            logger.warning("MT5连接失败，请检查配置")
        
        # 启动交易策略监控
        trading_strategy.start_strategy()

        # 启动交易引擎监控（超时平仓等）
        trading_strategy_engine.start_monitoring()
        logger.info("交易引擎监控启动成功")

        # 启动风险管理监控
        risk_manager.start_monitoring()

        # 启动订单同步
        from trading.order_sync import order_sync_manager
        if order_sync_manager.start_sync():
            logger.info("MT5订单同步启动成功")
        else:
            logger.warning("MT5订单同步启动失败")

        # 启动止损止盈监控（延迟启动，确保其他组件已初始化）
        import time
        import threading

        def delayed_start_sl_tp_monitor():
            """延迟启动止损止盈监控"""
            time.sleep(3)  # 等待3秒确保其他组件初始化完成
            try:
                if sl_tp_monitor.start_monitoring():
                    logger.info("止损止盈监控启动成功")
                else:
                    logger.warning("止损止盈监控启动失败")
            except Exception as e:
                logger.error(f"启动止损止盈监控失败: {e}")

        # 在后台线程中延迟启动
        threading.Thread(target=delayed_start_sl_tp_monitor, daemon=True).start()

        logger.info("系统初始化完成")
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")

if __name__ == '__main__':
    # 初始化系统
    init_system()
    
    # 创建应用
    app = create_app()
    
    # 启动应用（禁用调试模式以避免Python 3.13兼容性问题）
    app.run(
        host=app.config.get('FLASK_HOST', '0.0.0.0'),
        port=app.config.get('FLASK_PORT', 5000),
        debug=False,  # 强制禁用调试模式
        use_reloader=False  # 禁用自动重载
    )

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 订单详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 56px);
        }
        .sidebar a {
            color: #adb5bd;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
        }
        .sidebar a:hover, .sidebar a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar i {
            margin-right: 8px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }
        .stats-card {
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-red {
            background-color: #dc3545;
        }
        .status-yellow {
            background-color: #ffc107;
        }
        .detail-row {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .profit-chart {
            height: 300px;
            width: 100%;
        }
        /* Auto-refresh related styles */
        .refresh-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
            background-color: #28a745;
            animation: blink 1s infinite;
            vertical-align: middle;
        }
        @keyframes blink {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }
        #refreshControlBar {
            border: 1px solid #dee2e6;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        #refreshControlBar:hover {
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
        .position-trend-info {
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #6c757d;
        }
        .data-change {
            animation: highlight 1.5s ease-out;
            position: relative;
            border-radius: 3px;
        }
        @keyframes highlight {
            0% { background-color: rgba(255, 243, 205, 1); }
            50% { background-color: rgba(255, 243, 205, 0.7); }
            100% { background-color: transparent; }
        }
        .value-increase {
            color: #28a745 !important;
            font-weight: bold;
        }
        .value-decrease {
            color: #dc3545 !important;
            font-weight: bold;
        }
        .change-indicator {
            margin-left: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        .change-indicator.up {
            color: #28a745;
        }
        .change-indicator.down {
            color: #dc3545;
        }
        .last-updated {
            font-size: 12px;
            color: #6c757d;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">订单详情</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="{{ url_for('orders') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回订单列表
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <!-- 订单基本信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">基本信息</h5>
                            </div>
                            <div class="card-body">
                                {% if order['id'] is not none %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">订单ID</div>
                                    <div class="col-md-8">{{ order['id'] }}</div>
                                </div>
                                {% endif %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">订单号</div>
                                    <div class="col-md-8">{{ order['ticket'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">交易对</div>
                                    <div class="col-md-8">{{ order['trading_pair'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">交易方向</div>
                                    <div class="col-md-8">
                                        {% if order['operation'] == 'buy' %}
                                            <span class="badge bg-success">买入</span>
                                        {% else %}
                                            <span class="badge bg-danger">卖出</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">交易量</div>
                                    <div class="col-md-8">{{ order['volume'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">开仓时间</div>
                                    <div class="col-md-8">
                                        {% if order.get('time_formatted') %}
                                            {{ order['time_formatted'] }}
                                        {% elif order.get('mt5_time') %}
                                            {{ order['mt5_time'] }}
                                        {% else %}
                                            {{ order['timestamp'].replace('T', ' ').split('.')[0] if order['timestamp'] else '-' }}
                                        {% endif %}
                                    </div>
                                </div>
                                {% if order.get('magic') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">Magic 编号</div>
                                    <div class="col-md-8">{{ order['magic'] }}</div>
                                </div>
                                {% endif %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">开仓价格</div>
                                    <div class="col-md-8">{{ order['price'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">订单状态</div>
                                    <div class="col-md-8">
                                        {% if order['status'] == 'open' %}
                                            <span class="badge bg-success">持仓中</span>
                                        {% elif order['status'] == 'closed' %}
                                            <span class="badge bg-secondary">已平仓</span>
                                        {% elif order['status'] == 'partially_closed' %}
                                            <span class="badge bg-warning">部分平仓</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 止损止盈和平仓信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">止损止盈和平仓信息</h5>
                                {% if order['status'] == 'open' %}
                                <div class="mt-2">
                                    <div class="position-trend-info small" id="positionTrendInfo"></div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">止损价格</div>
                                    <div class="col-md-8">{{ order['sl'] if order['sl'] else '未设置' }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">止盈价格</div>
                                    <div class="col-md-8">{{ order['tp'] if order['tp'] else '未设置' }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">当前盈亏</div>
                                    <div id="currentProfitValue" class="col-md-8 {% if order['profit'] > 0 %}text-success fw-bold{% elif order['profit'] < 0 %}text-danger fw-bold{% endif %}">
                                        {{ "%.2f"|format(order['profit']|float) }}
                                    </div>
                                </div>
                                {% if order.get('from_mt5', False) or order.get('current_price') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">当前价格</div>
                                    <div class="col-md-8" id="currentPriceValue">{{ order['price_current'] if order.get('price_current') else order.get('current_price', '-') }}</div>
                                </div>
                                {% if order['status'] == 'open' %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">价格变化</div>
                                    <div class="col-md-8">
                                        <span id="priceDiffValue" class="{% if (order['price_current'] - order['price']) > 0 and order['operation'] == 'buy' or (order['price_current'] - order['price']) < 0 and order['operation'] == 'sell' %}text-success{% elif (order['price_current'] - order['price']) != 0 %}text-danger{% endif %}">
                                            {{ "%.5f"|format((order['price_current'] - order['price'])|float) if order.get('price_current') else "0.00000" }}
                                        </span>
                                    </div>
                                </div>
                                {% endif %}
                                {% endif %}
                                {% if order.get('swap') is not none %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">隔夜利息</div>
                                    <div class="col-md-8 {% if order['swap'] > 0 %}text-success{% elif order['swap'] < 0 %}text-danger{% endif %}">
                                        {{ "%.2f"|format(order['swap']|float) }}
                                    </div>
                                </div>
                                {% endif %}
                                {% if order.get('time_formatted') or order.get('mt5_time') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">持仓时长</div>
                                    <div class="col-md-8">{{ order.get('duration', '-') }}</div>
                                </div>
                                {% endif %}
                                {% if order.get('comment') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">注释</div>
                                    <div class="col-md-8">{{ order['comment'] }}</div>
                                </div>
                                {% endif %}
                                {% if order['status'] == 'closed' or order['status'] == 'partially_closed' %}
                                    <div class="row detail-row">
                                        <div class="col-md-4 detail-label">平仓价格</div>
                                        <div class="col-md-8">{{ order['close_price'] if order['close_price'] else '-' }}</div>
                                    </div>
                                    <div class="row detail-row">
                                        <div class="col-md-4 detail-label">平仓时间</div>
                                        <div class="col-md-8">{{ order['close_time'].replace('T', ' ').split('.')[0] if order['close_time'] else '-' }}</div>
                                    </div>
                                    <div class="row detail-row">
                                        <div class="col-md-4 detail-label">平仓原因</div>
                                        <div class="col-md-8">{{ order.get('close_reason', '手动平仓') }}</div>
                                    </div>
                                {% endif %}
                                
                                {% if order['status'] == 'open' or order['status'] == 'partially_closed' %}
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button class="btn btn-outline-danger close-position-btn" 
                                                    data-ticket="{{ order['ticket'] }}" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#closePositionModal">
                                                <i class="bi bi-x-circle"></i> 平仓
                                            </button>
                                            <button class="btn btn-outline-warning modify-sl-tp-btn" 
                                                    data-ticket="{{ order['ticket'] }}"
                                                    data-sl="{{ order['sl'] }}"
                                                    data-tp="{{ order['tp'] }}"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modifySLTPModal">
                                                <i class="bi bi-pencil"></i> 修改止损止盈
                                            </button>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                {% if order.get('from_mt5', False) or order['status'] == 'open' %}
                <!-- MT5实时价格信息 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">MT5 实时交易数据</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm mb-3">
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">价格变动</h6>
                                                <div class="row detail-row">
                                                    <div class="col-6 detail-label">开仓价格</div>
                                                    <div class="col-6">{{ order['price'] }}</div>
                                                </div>
                                                <div class="row detail-row">
                                                    <div class="col-6 detail-label">当前价格</div>
                                                    <div class="col-6">{{ order.get('price_current', order.get('current_price', '-')) }}</div>
                                                </div>
                                                <div class="row detail-row">
                                                    <div class="col-6 detail-label">价格变化</div>
                                                    <div class="col-6">
                                                        {% if order.get('price_current') and order['price'] %}
                                                            {% set price_diff = order.get('price_current') - order['price'] %}
                                                            <span class="{% if (price_diff > 0 and order['operation'] == 'buy') or (price_diff < 0 and order['operation'] == 'sell') %}text-success{% elif price_diff != 0 %}text-danger{% endif %}">
                                                                {{ "%.5f"|format(price_diff) }}
                                                            </span>
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-0 shadow-sm mb-3">
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">盈亏状况</h6>
                                                <div class="row detail-row">
                                                    <div class="col-6 detail-label">当前盈亏</div>
                                                    <div class="col-6 {% if order['profit'] > 0 %}text-success fw-bold{% elif order['profit'] < 0 %}text-danger fw-bold{% endif %}">
                                                        {{ "%.2f"|format(order['profit']|float) }}
                                                    </div>
                                                </div>
                                                <div class="row detail-row">
                                                    <div class="col-6 detail-label">隔夜利息</div>
                                                    <div class="col-6 {% if order.get('swap', 0) > 0 %}text-success{% elif order.get('swap', 0) < 0 %}text-danger{% endif %}">
                                                        {{ "%.2f"|format(order.get('swap', 0)|float) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- 关联信号信息 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">关联信号信息</h5>
                            </div>
                            <div class="card-body">
                                {% if signal %}
                                    <div class="row detail-row">
                                        <div class="col-md-2 detail-label">信号ID</div>
                                        <div class="col-md-4">{{ signal['id'] }}</div>
                                        <div class="col-md-2 detail-label">信号时间</div>
                                        <div class="col-md-4">{{ signal['timestamp'].replace('T', ' ').split('.')[0] }}</div>
                                    </div>
                                    <div class="row detail-row">
                                        <div class="col-md-2 detail-label">信号类型</div>
                                        <div class="col-md-4">
                                            {% if signal['signal_type'] == 'buy' %}
                                                <span class="badge bg-success">买入</span>
                                            {% else %}
                                                <span class="badge bg-danger">卖出</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-2 detail-label">时间周期</div>
                                        <div class="col-md-4">{{ signal['interval'] }}</div>
                                    </div>
                                    <div class="row detail-row">
                                        <div class="col-md-2 detail-label">MRC事件</div>
                                        <div class="col-md-10">{{ signal['mrc_event'] }}</div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <a href="{{ url_for('signal_detail', signal_id=signal['id']) }}" class="btn btn-outline-primary">
                                                <i class="bi bi-info-circle"></i> 查看信号详情
                                            </a>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="alert alert-warning mb-0">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>此订单没有关联的信号信息或是手动创建的
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单历史记录 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">订单历史记录</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">时间</th>
                                                <th scope="col">操作类型</th>
                                                <th scope="col">详情</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    {% if order.get('time_formatted') %}
                                                        {{ order['time_formatted'] }}
                                                    {% elif order.get('mt5_time') %}
                                                        {{ order['mt5_time'] }}
                                                    {% else %}
                                                        {{ order['timestamp'].replace('T', ' ').split('.')[0] if order['timestamp'] else '-' }}
                                                    {% endif %}
                                                </td>
                                                <td><span class="badge bg-primary">开仓</span></td>
                                                <td>以 {{ order['price'] }} 价格开仓 {{ order['volume'] }} 手 {{ order['trading_pair'] }}
                                                    {% if order.get('comment') %}({{ order['comment'] }}){% endif %}
                                                </td>
                                            </tr>
                                            
                                            {% if order['sl'] or order['tp'] %}
                                            <tr>
                                                <td>
                                                    {% if order.get('time_formatted') %}
                                                        {{ order['time_formatted'] }}
                                                    {% elif order.get('mt5_time') %}
                                                        {{ order['mt5_time'] }}
                                                    {% else %}
                                                        {{ order['timestamp'].replace('T', ' ').split('.')[0] if order['timestamp'] else '-' }}
                                                    {% endif %}
                                                </td>
                                                <td><span class="badge bg-info">设置止损止盈</span></td>
                                                <td>设置止损: {{ order['sl'] if order['sl'] else '无' }}, 止盈: {{ order['tp'] if order['tp'] else '无' }}</td>
                                            </tr>
                                            {% endif %}
                                            
                                            {% if order['status'] == 'closed' or order['status'] == 'partially_closed' %}
                                            <tr>
                                                <td>{{ order['close_time'].replace('T', ' ').split('.')[0] if order['close_time'] else '-' }}</td>
                                                <td>
                                                    {% if order['status'] == 'closed' %}
                                                    <span class="badge bg-secondary">完全平仓</span>
                                                    {% else %}
                                                    <span class="badge bg-warning">部分平仓</span>
                                                    {% endif %}
                                                </td>
                                                <td>以 {{ order['close_price'] }} 价格平仓，盈亏: <span class="{% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% endif %}">{{ "%.2f"|format(order['profit']|float) }}</span></td>
                                            </tr>
                                            {% endif %}
                                            
                                            <!-- 这里可以添加其他历史记录，如修改止损止盈等 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 平仓确认弹窗 -->
    <div class="modal fade" id="closePositionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认平仓</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要平仓订单 <span id="closePositionTicket" class="fw-bold"></span> 吗？</p>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="partialCloseCheck">
                        <label class="form-check-label" for="partialCloseCheck">
                            部分平仓
                        </label>
                    </div>
                    <div id="partialVolumeGroup" class="mb-3" style="display: none;">
                        <label for="partialVolume" class="form-label">平仓量</label>
                        <input type="number" class="form-control" id="partialVolume" step="0.01" min="0.01">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmCloseBtn">确认平仓</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改止损止盈弹窗 -->
    <div class="modal fade" id="modifySLTPModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">修改止损止盈</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>修改订单 <span id="modifyTicket" class="fw-bold"></span> 的止损止盈：</p>
                    <div class="mb-3">
                        <label for="newSL" class="form-label">止损价格</label>
                        <input type="number" class="form-control" id="newSL" step="0.00001">
                        <small class="form-text text-muted">留空表示不修改</small>
                    </div>
                    <div class="mb-3">
                        <label for="newTP" class="form-label">止盈价格</label>
                        <input type="number" class="form-control" id="newTP" step="0.00001">
                        <small class="form-text text-muted">留空表示不修改</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmModifyBtn">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 存储当前数据，用于比较变化
        let currentData = {
            price_current: {{ order.get('price_current', 0)|float }},
            profit: {{ order.get('profit', 0)|float }},
            swap: {{ order.get('swap', 0)|float }}
        };
        
        // 格式化日期时间
        function formatDateTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN');
        }
        
        // 格式化时长
        function formatDuration(seconds) {
            const days = Math.floor(seconds / (3600 * 24));
            const hours = Math.floor((seconds % (3600 * 24)) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${days}天 ${hours}小时 ${minutes}分钟`;
        }
        
        // 获取持仓数据
        function fetchPositionData() {
            const ticket = {{ order['ticket'] }};
            
            $('#refreshIndicator').css('opacity', '1'); // 显示刷新进行中指示器
            
            $.ajax({
                url: `/api/position/${ticket}`,
                type: 'GET',
                success: function(response) {
                    if(response.success) {
                        updatePositionData(response.position);
                        
                        // 更新最后刷新时间
                        const now = new Date();
                        const timeStr = now.toLocaleTimeString();
                        $('#lastUpdated').html(`<span class="text-success">最后更新: ${timeStr}</span>`);
                        
                        // 更新价格趋势显示
                        updatePriceTrend();
                        
                        // 更新页面标题添加实时盈亏信息
                        const profit = response.position.profit;
                        const profitPrefix = profit > 0 ? '▲' : profit < 0 ? '▼' : '';
                        document.title = `${profitPrefix} ${response.position.symbol} | ${profit.toFixed(2)} | MT5交易系统`;
                    } else {
                        console.error('获取持仓数据失败:', response.message);
                        $('#lastUpdated').html(`<span class="text-danger">刷新失败: ${response.message}</span>`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求失败:', error);
                    $('#lastUpdated').html(`<span class="text-danger">请求失败: ${error}</span>`);
                },
                complete: function() {
                    // 添加淡入淡出效果，表示刷新完成
                    $('#refreshIndicator').css('opacity', '0.3');
                }
            });
        }
        
        // 更新持仓数据UI
        function updatePositionData(position) {
            // 更新价格信息
            const priceCurrentElement = $(".detail-label:contains('当前价格')").next();
            if(position.price_current !== currentData.price_current) {
                // 添加变化指示器
                const increasing = position.price_current > currentData.price_current;
                priceCurrentElement.addClass('data-change');
                if (increasing) {
                    priceCurrentElement.addClass('value-increase');
                } else {
                    priceCurrentElement.addClass('value-decrease');
                }
                priceCurrentElement.html(`${position.price_current} <span class="change-indicator ${increasing ? 'up' : 'down'}">${increasing ? '▲' : '▼'}</span>`);
                
                // 5秒后移除指示效果，但保留颜色
                setTimeout(() => {
                    priceCurrentElement.removeClass('data-change');
                }, 1500);
            } else {
                priceCurrentElement.text(position.price_current);
                priceCurrentElement.removeClass('value-increase value-decrease');
            }
            
            // 更新价格变化
            const priceDiff = position.price_current - position.price_open;
            const priceDiffElement = $(".detail-label:contains('价格变化')").next().find('span');
            if(priceDiff !== parseFloat(priceDiffElement.text())) {
                priceDiffElement.addClass('data-change');
                setTimeout(() => priceDiffElement.removeClass('data-change'), 1500);
            }
            
            const isPricePositive = (priceDiff > 0 && position.type_str === 'buy') || 
                                   (priceDiff < 0 && position.type_str === 'sell');
            priceDiffElement.removeClass('text-success text-danger');
            if(priceDiff !== 0) {
                priceDiffElement.addClass(isPricePositive ? 'text-success' : 'text-danger');
            }
            priceDiffElement.text(priceDiff.toFixed(5));
            
            // 更新盈亏信息
            const profitElement = $(".detail-label:contains('当前盈亏')").next();
            if(position.profit !== currentData.profit) {
                // 添加变化指示器
                const increasing = position.profit > currentData.profit;
                profitElement.addClass('data-change');
                
                // 清除之前的样式
                profitElement.removeClass('text-success text-danger fw-bold value-increase value-decrease');
                
                // 根据盈亏值设置样式
                if(position.profit !== 0) {
                    profitElement.addClass(position.profit > 0 ? 'text-success fw-bold' : 'text-danger fw-bold');
                }
                
                // 根据变化方向设置指示器
                profitElement.html(`${position.profit.toFixed(2)} <span class="change-indicator ${increasing ? 'up' : 'down'}">${increasing ? '▲' : '▼'}</span>`);
                
                // 只去掉高亮动画，保留颜色和变化指示
                setTimeout(() => {
                    profitElement.removeClass('data-change');
                }, 1500);
            } else {
                // 无变化时只更新值，保持颜色样式
                profitElement.removeClass('text-success text-danger value-increase value-decrease');
                if(position.profit !== 0) {
                    profitElement.addClass(position.profit > 0 ? 'text-success fw-bold' : 'text-danger fw-bold');
                }
                profitElement.text(position.profit.toFixed(2));
            }
            
            // 更新隔夜利息
            const swapElement = $(".detail-label:contains('隔夜利息')").next();
            if(position.swap !== currentData.swap) {
                swapElement.addClass('data-change');
                setTimeout(() => swapElement.removeClass('data-change'), 1500);
            }
            swapElement.removeClass('text-success text-danger');
            if(position.swap !== 0) {
                swapElement.addClass(position.swap > 0 ? 'text-success' : 'text-danger');
            }
            swapElement.text(position.swap.toFixed(2));
            
            // 更新持仓时长
            const now = Math.floor(Date.now() / 1000);
            const durationSeconds = now - position.time;
            $(".detail-label:contains('持仓时长')").next().text(formatDuration(durationSeconds));
            
            // 保存当前数据用于下次比较
            currentData = {
                price_current: position.price_current,
                profit: position.profit,
                swap: position.swap
            };
        }
        
        // 保存用户设置到localStorage
        function saveUserPreferences() {
            try {
                const preferences = {
                    autoRefresh: $('#autoRefreshSwitch').is(':checked'),
                    refreshInterval: $('#refreshIntervalSelect').val(),
                    lastSaved: new Date().toISOString()
                };
                localStorage.setItem('mt5PositionDetailPrefs', JSON.stringify(preferences));
                console.log('用户首选项已保存', preferences);
            } catch (e) {
                console.error('保存用户首选项失败', e);
            }
        }
        
        // 从localStorage加载用户设置
        function loadUserPreferences() {
            try {
                const prefsString = localStorage.getItem('mt5PositionDetailPrefs');
                if (prefsString) {
                    const preferences = JSON.parse(prefsString);
                    console.log('加载用户首选项', preferences);
                    
                    // 设置刷新间隔
                    if (preferences.refreshInterval && $('#refreshIntervalSelect option[value="'+preferences.refreshInterval+'"]').length > 0) {
                        $('#refreshIntervalSelect').val(preferences.refreshInterval);
                    } else {
                        // 默认5秒
                        $('#refreshIntervalSelect').val('5000');
                    }
                    
                    // 设置自动刷新状态
                    if (preferences.autoRefresh) {
                        $('#autoRefreshSwitch').prop('checked', true).trigger('change');
                    } else {
                        $('#autoRefreshSwitch').prop('checked', false);
                    }
                    
                    return true;
                }
            } catch (e) {
                console.error('加载用户首选项失败', e);
            }
            return false;
        }
        
        // 更新价格变动的可视化指示器和持仓趋势信息
        function updatePriceTrend() {
            const ticket = {{ order['ticket'] }};
            const historyKey = `price_history_${ticket}`;
            const operationType = "{{ order['operation'] }}";
            
            // 检查是否有历史价格记录
            let priceHistory = localStorage.getItem(historyKey);
            if (priceHistory) {
                try {
                    priceHistory = JSON.parse(priceHistory);
                    // 超过10分钟的历史记录不使用
                    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
                    if (priceHistory.timestamp < tenMinutesAgo) {
                        localStorage.removeItem(historyKey);
                        return;
                    }
                    
                    // 计算价格趋势
                    const currentPrice = currentData.price_current;
                    const openingPrice = {{ order['price'] }};
                    const lastPrice = priceHistory.last_price;
                    
                    // 计算价格趋势
                    const trend = currentPrice > lastPrice ? "上涨" : currentPrice < lastPrice ? "下跌" : "平稳";
                    const trendClass = currentPrice > lastPrice ? "text-success" : currentPrice < lastPrice ? "text-danger" : "text-muted";
                    
                    // 计算从开盘到现在的变化
                    const overallChange = currentPrice - openingPrice;
                    const overallChangePercent = ((overallChange / openingPrice) * 100).toFixed(4);
                    
                    // 检查对于当前操作类型价格变化是否有利
                    const isPriceMovementFavorable = 
                        (operationType === 'buy' && overallChange > 0) || 
                        (operationType === 'sell' && overallChange < 0);
                    
                    const overallTrendClass = isPriceMovementFavorable ? "text-success" : (overallChange !== 0 ? "text-danger" : "text-muted");
                    
                    // 计算盈亏趋势
                    const profit = currentData.profit;
                    const profitClass = profit > 0 ? "text-success" : profit < 0 ? "text-danger" : "text-muted";
                    
                    // 更新趋势信息区域
                    const trendHTML = `
                        <div class="mb-1">
                            <strong>价格:</strong> 
                            <span class="${trendClass}">${trend}</span> | 
                            <span class="${overallTrendClass}">从开仓: ${overallChange > 0 ? "+" : ""}${overallChangePercent}%</span>
                        </div>
                        <div>
                            <strong>盈亏:</strong> 
                            <span class="${profitClass}">${profit.toFixed(2)}</span>
                            <span class="ms-1 small">(${isPriceMovementFavorable ? '✓ 良好' : profit > 0 ? '✓ 良好' : '⚠️ 不利'})</span>
                        </div>
                    `;
                    
                    // 更新趋势信息显示
                    $("#positionTrendInfo").html(trendHTML);
                    
                    // 更新历史记录
                    priceHistory.last_price = currentPrice;
                    priceHistory.profit = profit;
                    priceHistory.timestamp = Date.now();
                    localStorage.setItem(historyKey, JSON.stringify(priceHistory));
                } catch (e) {
                    console.error('处理价格历史记录失败', e);
                }
            } else {
                // 创建新的历史记录
                const newHistory = {
                    opening_price: {{ order['price'] }},
                    last_price: currentData.price_current,
                    profit: currentData.profit,
                    timestamp: Date.now()
                };
                localStorage.setItem(historyKey, JSON.stringify(newHistory));
            }
        }
        
        $(document).ready(function() {
            // 只为实时更新添加控制栏，其他情况隐藏
            {% if order['status'] == 'open' %}
            
            // 添加控制栏容器用于更好的布局
            const refreshControlBar = $('<div class="d-flex align-items-center bg-light p-2 rounded mb-3" id="refreshControlBar" style="margin-top: 10px;"></div>')
                .insertAfter('.btn-toolbar');
            
            // 添加刷新按钮和自动刷新开关
            $('<button class="btn btn-sm btn-outline-primary" id="refreshDataBtn"><i class="bi bi-arrow-clockwise"></i> 刷新数据</button>')
                .appendTo('#refreshControlBar');
                
            const switchContainer = $('<div class="form-check form-switch ms-3 d-flex align-items-center"></div>').appendTo('#refreshControlBar');
            $('<input class="form-check-input" type="checkbox" id="autoRefreshSwitch">')
                .appendTo(switchContainer);
            $('<label class="form-check-label ms-1" for="autoRefreshSwitch">自动刷新</label>')
                .appendTo(switchContainer);
            
            // 添加刷新间隔选择，并美化UI
            const intervalContainer = $('<div class="d-flex align-items-center ms-3" id="refreshIntervalContainer" style="display: none;"></div>')
                .appendTo('#refreshControlBar');
            $('<label class="me-2 text-nowrap small" for="refreshIntervalSelect">刷新间隔:</label>')
                .appendTo(intervalContainer);
            const intervalSelect = $('<select class="form-select form-select-sm" id="refreshIntervalSelect" style="width: auto;"></select>')
                .appendTo(intervalContainer);
            
            // 添加刷新间隔选项
            intervalSelect.append('<option value="1000">1秒</option>');
            intervalSelect.append('<option value="3000">3秒</option>');
            intervalSelect.append('<option value="5000" selected>5秒</option>');
            intervalSelect.append('<option value="10000">10秒</option>');
            intervalSelect.append('<option value="30000">30秒</option>');
                
            // 添加刷新状态指示器，更友好的UI
            const statusContainer = $('<div class="ms-auto d-flex align-items-center" id="lastUpdatedContainer" style="display: none;"></div>')
                .appendTo('#refreshControlBar');
            $('<span class="refresh-indicator me-1" id="refreshIndicator"></span>')
                .appendTo(statusContainer);
            $('<span id="lastUpdated" class="last-updated"></span>')
                .appendTo(statusContainer);
            
            {% endif %}
            
            // 刷新数据按钮点击事件
            $('#refreshDataBtn').click(function() {
                fetchPositionData();
            });
            
            // 自动刷新开关事件
            let refreshInterval;
            $('#autoRefreshSwitch').change(function() {
                if($(this).is(':checked')) {
                    $('#refreshIntervalContainer').fadeIn(200);
                    $('#lastUpdatedContainer').fadeIn(200);
                    
                    // 根据选择的间隔进行刷新
                    const interval = parseInt($('#refreshIntervalSelect').val());
                    refreshInterval = setInterval(fetchPositionData, interval);
                    
                    // 立即刷新一次
                    fetchPositionData();
                    
                    // 添加视觉反馈
                    $('#refreshDataBtn').addClass('disabled').attr('disabled', true);
                } else {
                    $('#refreshIntervalContainer').fadeOut(200);
                    $('#lastUpdatedContainer').fadeOut(200);
                    clearInterval(refreshInterval);
                    
                    // 恢复手动刷新按钮
                    $('#refreshDataBtn').removeClass('disabled').attr('disabled', false);
                }
                // 保存用户设置
                saveUserPreferences();
            });
            
            // 刷新间隔变更事件
            $('#refreshIntervalSelect').change(function() {
                if($('#autoRefreshSwitch').is(':checked')) {
                    clearInterval(refreshInterval);
                    const interval = parseInt($(this).val());
                    refreshInterval = setInterval(fetchPositionData, interval);
                }
                // 保存用户设置
                saveUserPreferences();
            });
            
            // 加载用户首选项
            loadUserPreferences();
            
            // 如果自动刷新开关已开启，启动定时刷新
            if($('#autoRefreshSwitch').is(':checked')) {
                $('#refreshIntervalSelect').show();
                $('#lastUpdatedContainer').show();
                $('#refreshDataBtn').addClass('disabled').attr('disabled', true);
                const interval = parseInt($('#refreshIntervalSelect').val());
                refreshInterval = setInterval(fetchPositionData, interval);
                // 立即刷新一次
                fetchPositionData();
            }
            
            // 平仓按钮点击事件
            $('.close-position-btn').click(function() {
                const ticket = $(this).data('ticket');
                $('#closePositionTicket').text(ticket);
            });

            // 部分平仓选项
            $('#partialCloseCheck').change(function() {
                if($(this).is(':checked')) {
                    $('#partialVolumeGroup').show();
                } else {
                    $('#partialVolumeGroup').hide();
                }
            });

            // 确认平仓事件
            $('#confirmCloseBtn').click(function() {
                const ticket = $('#closePositionTicket').text();
                let data = {
                    ticket: ticket
                };
                
                if($('#partialCloseCheck').is(':checked')) {
                    const volume = $('#partialVolume').val();
                    if(volume && volume > 0) {
                        data.volume = volume;
                    } else {
                        alert('请输入有效的平仓量');
                        return;
                    }
                }
                
                $.ajax({
                    url: '/close_position',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if(response.success) {
                            alert('平仓成功');
                            location.reload();
                        } else {
                            alert('平仓失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#closePositionModal').modal('hide');
                    }
                });
            });

            // 修改止损止盈按钮点击事件
            $('.modify-sl-tp-btn').click(function() {
                const ticket = $(this).data('ticket');
                const sl = $(this).data('sl');
                const tp = $(this).data('tp');
                
                $('#modifyTicket').text(ticket);
                $('#newSL').val(sl);
                $('#newTP').val(tp);
            });

            // 确认修改止损止盈事件
            $('#confirmModifyBtn').click(function() {
                const ticket = $('#modifyTicket').text();
                const sl = $('#newSL').val();
                const tp = $('#newTP').val();
                
                $.ajax({
                    url: '/modify_sl_tp',
                    type: 'POST',
                    data: {
                        ticket: ticket,
                        sl: sl,
                        tp: tp
                    },
                    success: function(response) {
                        if(response.success) {
                            alert('修改成功');
                            location.reload();
                        } else {
                            alert('修改失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#modifySLTPModal').modal('hide');
                    }
                });
            });
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持仓管理页面改进功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import requests
import json

def test_account_api():
    """测试账户信息API"""
    print("=== 测试账户信息API ===")
    
    base_url = "http://localhost:7000"
    
    try:
        # 测试账户信息API
        response = requests.get(f"{base_url}/api/account/info")
        print(f"GET /api/account/info 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"账户信息API响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if data.get('success'):
                account_data = data.get('data', {})
                print(f"账户余额: ${account_data.get('balance', 0):.2f}")
                print(f"账户净值: ${account_data.get('equity', 0):.2f}")
                print(f"已用保证金: ${account_data.get('margin', 0):.2f}")
                print("✅ 账户信息API正常")
            else:
                print(f"❌ API返回失败: {data}")
        else:
            print(f"❌ API请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_refresh_timing():
    """测试刷新时间间隔"""
    print("\n=== 测试刷新时间间隔 ===")
    
    # 检查模板文件中的刷新间隔设置
    template_file = 'templates/simple_dashboard.html'
    
    if os.path.exists(template_file):
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找刷新间隔设置
            if '}, 2000);' in content and '// 定时刷新账户信息和持仓数据（每2秒）' in content:
                print("✅ 自动刷新间隔已设置为2秒")
            elif '}, 10000);' in content:
                print("⚠️  自动刷新间隔仍为10秒，需要修改")
            else:
                print("❓ 未找到刷新间隔设置")
                
            # 查找手动刷新按钮
            if 'refreshAccountInfo()' in content and 'refreshAccountBtn' in content:
                print("✅ 手动刷新按钮已添加")
            else:
                print("❌ 手动刷新按钮未找到")
                
        except Exception as e:
            print(f"❌ 读取模板文件失败: {e}")
    else:
        print("❌ 模板文件不存在")

def check_css_animations():
    """检查CSS动画"""
    print("\n=== 检查CSS动画 ===")
    
    css_file = 'static/css/style.css'
    
    if os.path.exists(css_file):
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # 检查旋转动画
            animation_indicators = [
                '.spin {',
                'animation: spin 1s linear infinite;',
                '@keyframes spin {'
            ]
            
            found_indicators = []
            for indicator in animation_indicators:
                if indicator in css_content:
                    found_indicators.append(indicator)
            
            print(f"找到的动画CSS: {len(found_indicators)}/{len(animation_indicators)}")
            
            if len(found_indicators) == len(animation_indicators):
                print("✅ CSS旋转动画已添加")
            else:
                print("⚠️  CSS旋转动画不完整")
                missing = set(animation_indicators) - set(found_indicators)
                print(f"缺失的CSS: {missing}")
                
        except Exception as e:
            print(f"❌ 读取CSS文件失败: {e}")
    else:
        print("❌ CSS文件不存在")

def simulate_refresh_test():
    """模拟刷新测试"""
    print("\n=== 模拟刷新测试 ===")
    
    base_url = "http://localhost:7000"
    
    try:
        print("模拟连续刷新账户信息...")
        
        for i in range(3):
            print(f"第 {i+1} 次刷新...")
            
            start_time = time.time()
            response = requests.get(f"{base_url}/api/account/info", timeout=5)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                print(f"  ✅ 响应时间: {response_time:.0f}ms")
            else:
                print(f"  ❌ 请求失败: {response.status_code}")
            
            if i < 2:  # 不在最后一次等待
                time.sleep(2)  # 等待2秒，模拟新的刷新间隔
        
        print("✅ 刷新测试完成")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
    except Exception as e:
        print(f"❌ 刷新测试失败: {e}")

def check_ui_improvements():
    """检查UI改进"""
    print("\n=== 检查UI改进 ===")
    
    template_file = 'templates/simple_dashboard.html'
    
    if os.path.exists(template_file):
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            improvements = {
                "账户信息标题": '<h6 class="mb-0"><i class="bi bi-person-circle"></i> 账户信息</h6>',
                "刷新按钮": 'onclick="refreshAccountInfo()"',
                "刷新函数": 'async function refreshAccountInfo()',
                "加载状态": 'refreshBtn.innerHTML = \'<i class="bi bi-arrow-clockwise spin"></i> 刷新中...\';',
                "成功提示": 'ui.showAlert(\'success\', \'账户信息已刷新\', 1500);'
            }
            
            found_improvements = 0
            for name, indicator in improvements.items():
                if indicator in content:
                    print(f"✅ {name}: 已实现")
                    found_improvements += 1
                else:
                    print(f"❌ {name}: 未找到")
            
            print(f"\n总体进度: {found_improvements}/{len(improvements)} 项改进已实现")
            
            if found_improvements == len(improvements):
                print("🎉 所有UI改进都已成功实现！")
            
        except Exception as e:
            print(f"❌ 检查UI改进失败: {e}")
    else:
        print("❌ 模板文件不存在")

if __name__ == "__main__":
    print("开始测试持仓管理页面改进功能...\n")
    
    check_ui_improvements()
    test_refresh_timing()
    check_css_animations()
    test_account_api()
    simulate_refresh_test()
    
    print("\n测试完成！")
    print("\n使用说明:")
    print("1. 打开浏览器访问系统界面")
    print("2. 进入持仓管理页面")
    print("3. 观察账户信息区域的刷新按钮")
    print("4. 点击'刷新账户'按钮测试手动刷新")
    print("5. 观察数据每2秒自动刷新")

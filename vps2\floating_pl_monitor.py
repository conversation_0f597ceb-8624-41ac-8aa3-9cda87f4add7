#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
浮动盈亏监控模块 - 实时监控浮动盈亏并自动执行平仓操作
"""

import time
import threading
import logging
import sqlite3
from datetime import datetime
import mt5_trader

# 配置日志
logger = logging.getLogger(__name__)

class FloatingPLMonitor:
    """浮动盈亏监控类"""
    
    def __init__(self, check_interval=5):
        """
        初始化监控器
        :param check_interval: 检查间隔(秒)
        """
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_thread = None
        
    def get_db_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_total_rule(self):
        """获取总浮动盈亏规则"""
        try:
            conn = self.get_db_connection()
            c = conn.cursor()
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'total' LIMIT 1")
            rule = c.fetchone()
            conn.close()
            return dict(rule) if rule else None
        except Exception as e:
            logger.error(f"获取总规则失败: {e}")
            return None
    
    def get_individual_rules(self):
        """获取所有单个订单规则"""
        try:
            conn = self.get_db_connection()
            c = conn.cursor()
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'individual' AND enabled = 1")
            rules = c.fetchall()
            conn.close()
            return [dict(rule) for rule in rules]
        except Exception as e:
            logger.error(f"获取单个订单规则失败: {e}")
            return []

    def get_global_sl_tp_rule(self):
        """获取全局止盈止损规则"""
        try:
            conn = self.get_db_connection()
            c = conn.cursor()
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'global_sl_tp' LIMIT 1")
            rule = c.fetchone()
            conn.close()
            return dict(rule) if rule else None
        except Exception as e:
            logger.error(f"获取全局止盈止损规则失败: {e}")
            return None
    
    def check_total_floating_pl(self, total_rule, positions):
        """检查总浮动盈亏是否触发条件"""
        try:
            if not total_rule or not total_rule.get('enabled'):
                return False
            
            total_profit = sum(position.get('profit', 0) for position in positions)
            profit_threshold = total_rule.get('profit_threshold')
            loss_threshold = total_rule.get('loss_threshold')
            
            logger.debug(f"总浮动盈亏检查: 当前盈亏={total_profit:.2f}, 盈利阈值={profit_threshold}, 亏损阈值={loss_threshold}")
            
            # 检查是否触发盈利平仓条件
            if profit_threshold and total_profit >= profit_threshold:
                logger.info(f"总浮动盈亏达到盈利阈值 {profit_threshold}，当前盈亏: {total_profit:.2f}，执行平仓所有订单")
                return self.close_all_positions("total_profit_threshold")
            
            # 检查是否触发亏损平仓条件
            if loss_threshold and total_profit <= loss_threshold:
                logger.info(f"总浮动盈亏达到亏损阈值 {loss_threshold}，当前盈亏: {total_profit:.2f}，执行平仓所有订单")
                return self.close_all_positions("total_loss_threshold")
            
            return False
            
        except Exception as e:
            logger.error(f"检查总浮动盈亏失败: {e}")
            return False
    
    def check_individual_floating_pl(self, individual_rules, positions):
        """检查单个订单浮动盈亏是否触发条件"""
        try:
            if not individual_rules:
                return False
            
            closed_count = 0
            
            # 为每个规则检查对应的持仓
            for rule in individual_rules:
                ticket = rule.get('ticket')
                profit_threshold = rule.get('profit_threshold')
                loss_threshold = rule.get('loss_threshold')
                
                # 找到对应的持仓
                position = next((pos for pos in positions if pos.get('ticket') == ticket), None)
                if not position:
                    continue
                
                current_profit = position.get('profit', 0)
                
                logger.debug(f"订单{ticket}浮动盈亏检查: 当前盈亏={current_profit:.2f}, 盈利阈值={profit_threshold}, 亏损阈值={loss_threshold}")
                
                # 检查是否触发盈利平仓条件
                if profit_threshold and current_profit >= profit_threshold:
                    logger.info(f"订单{ticket}浮动盈亏达到盈利阈值 {profit_threshold}，当前盈亏: {current_profit:.2f}，执行平仓")
                    if self.close_position(ticket, "individual_profit_threshold"):
                        closed_count += 1
                
                # 检查是否触发亏损平仓条件
                elif loss_threshold and current_profit <= loss_threshold:
                    logger.info(f"订单{ticket}浮动盈亏达到亏损阈值 {loss_threshold}，当前盈亏: {current_profit:.2f}，执行平仓")
                    if self.close_position(ticket, "individual_loss_threshold"):
                        closed_count += 1
            
            return closed_count > 0

        except Exception as e:
            logger.error(f"检查单个订单浮动盈亏失败: {e}")
            return False

    def check_global_sl_tp(self, global_sl_tp_rule, positions):
        """检查全局止盈止损是否触发条件"""
        try:
            if not global_sl_tp_rule or not global_sl_tp_rule.get('enabled'):
                return False

            profit_threshold = global_sl_tp_rule.get('profit_threshold')
            loss_threshold = global_sl_tp_rule.get('loss_threshold')
            closed_count = 0

            # 为每个持仓检查全局止盈止损条件
            for position in positions:
                ticket = position.get('ticket')
                current_profit = position.get('profit', 0)

                logger.debug(f"订单{ticket}全局止盈止损检查: 当前盈亏={current_profit:.2f}, 止盈阈值={profit_threshold}, 止损阈值={loss_threshold}")

                # 检查是否触发止盈条件
                if profit_threshold and current_profit >= profit_threshold:
                    logger.info(f"订单{ticket}达到全局止盈阈值 {profit_threshold}，当前盈亏: {current_profit:.2f}，执行平仓")
                    if self.close_position(ticket, "global_take_profit"):
                        closed_count += 1

                # 检查是否触发止损条件
                elif loss_threshold and current_profit <= loss_threshold:
                    logger.info(f"订单{ticket}达到全局止损阈值 {loss_threshold}，当前盈亏: {current_profit:.2f}，执行平仓")
                    if self.close_position(ticket, "global_stop_loss"):
                        closed_count += 1

            return closed_count > 0

        except Exception as e:
            logger.error(f"检查全局止盈止损失败: {e}")
            return False
    
    def close_position(self, ticket, reason):
        """平仓单个订单"""
        try:
            result = mt5_trader.close_position(ticket)
            if result:
                logger.info(f"订单{ticket}平仓成功，原因: {reason}")
                return True
            else:
                logger.error(f"订单{ticket}平仓失败，原因: {reason}")
                return False
        except Exception as e:
            logger.error(f"平仓订单{ticket}时出错: {e}")
            return False
    
    def close_all_positions(self, reason):
        """平仓所有订单"""
        try:
            result = mt5_trader.close_all_positions()
            if result.get('success'):
                logger.info(f"平仓所有订单成功，原因: {reason}, 平仓数量: {result.get('closed_count', 0)}")
                return True
            else:
                logger.error(f"平仓所有订单失败，原因: {reason}, 错误: {result.get('message', '未知错误')}")
                return False
        except Exception as e:
            logger.error(f"平仓所有订单时出错: {e}")
            return False
    
    def monitor_loop(self):
        """监控循环"""
        logger.info("浮动盈亏监控线程启动")
        
        while self.monitoring:
            try:
                # 初始化MT5连接
                mt5_connected = mt5_trader.init_mt5()
                if not mt5_connected:
                    logger.warning("MT5未连接，跳过本次监控")
                    time.sleep(self.check_interval)
                    continue
                
                # 获取所有持仓
                positions = mt5_trader.get_all_positions()
                if not positions:
                    logger.debug("当前无持仓，跳过监控")
                    time.sleep(self.check_interval)
                    continue
                
                # 获取规则
                total_rule = self.get_total_rule()
                individual_rules = self.get_individual_rules()
                global_sl_tp_rule = self.get_global_sl_tp_rule()

                # 检查总浮动盈亏
                total_triggered = self.check_total_floating_pl(total_rule, positions)

                # 如果总规则触发，则不需要检查其他规则
                if not total_triggered:
                    # 检查全局止盈止损
                    global_sl_tp_triggered = self.check_global_sl_tp(global_sl_tp_rule, positions)

                    # 如果全局止盈止损没有触发，再检查单个订单规则
                    if not global_sl_tp_triggered:
                        # 检查单个订单浮动盈亏
                        self.check_individual_floating_pl(individual_rules, positions)
                
            except Exception as e:
                logger.error(f"监控循环中出现错误: {e}")
            
            # 等待下次检查
            time.sleep(self.check_interval)
        
        logger.info("浮动盈亏监控线程结束")
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring:
            logger.warning("监控已在运行中")
            return False
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("浮动盈亏监控已启动")
        return True
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            logger.warning("监控未在运行")
            return False
        
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        logger.info("浮动盈亏监控已停止")
        return True
    
    def is_monitoring(self):
        """检查是否正在监控"""
        return self.monitoring and self.monitor_thread and self.monitor_thread.is_alive()

# 全局监控器实例
floating_pl_monitor = FloatingPLMonitor()

def start_floating_pl_monitor():
    """启动浮动盈亏监控"""
    return floating_pl_monitor.start_monitoring()

def stop_floating_pl_monitor():
    """停止浮动盈亏监控"""
    return floating_pl_monitor.stop_monitoring()

def is_floating_pl_monitor_running():
    """检查浮动盈亏监控是否运行"""
    return floating_pl_monitor.is_monitoring()

if __name__ == '__main__':
    # 测试监控器
    import logging
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    # 启动监控
    if start_floating_pl_monitor():
        print("监控启动成功")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("停止监控...")
            stop_floating_pl_monitor()
    else:
        print("监控启动失败")

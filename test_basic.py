#!/usr/bin/env python3
"""
基础功能测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from utils.helpers import normalize_symbol, normalize_signal_direction, validate_alert_data
        print("✅ utils.helpers 导入成功")
        
        from database.manager import DatabaseManager
        print("✅ database.manager 导入成功")
        
        from trading.config_manager import TradingConfigManager
        print("✅ trading.config_manager 导入成功")
        
        from webhook.handler import WebhookHandler
        print("✅ webhook.handler 导入成功")
        
        from auth.manager import AuthManager
        print("✅ auth.manager 导入成功")
        
        from notifications.bark import BarkNotificationManager
        print("✅ notifications.bark 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_symbol_normalization():
    """测试交易对标准化"""
    print("\n🔍 测试交易对标准化...")
    
    from utils.helpers import normalize_symbol
    
    test_cases = [
        ("XAUUSD", "XAUUSD"),
        ("ETHUSD", "ETHUSD"),
        ("ETHUSDT", "ETHUSD"),
        ("ETH-USD", "ETHUSD"),
        ("eth/usd", "ETHUSD"),
        ("BTC_USD", "BTCUSD"),
    ]
    
    all_passed = True
    for input_symbol, expected in test_cases:
        result = normalize_symbol(input_symbol)
        if result == expected:
            print(f"✅ {input_symbol} -> {result}")
        else:
            print(f"❌ {input_symbol} -> {result} (期望: {expected})")
            all_passed = False
    
    return all_passed

def test_signal_normalization():
    """测试信号标准化"""
    print("\n🔍 测试信号标准化...")
    
    from utils.helpers import normalize_signal_direction
    
    buy_signals = ["BUY", "buy", "LONG", "long", "UP", "买入", "做多", "综合做多信号"]
    sell_signals = ["SELL", "sell", "SHORT", "short", "DOWN", "卖出", "做空", "综合做空信号"]
    
    all_passed = True
    
    print("测试做多信号:")
    for signal in buy_signals:
        result = normalize_signal_direction(signal)
        if result == "BUY":
            print(f"✅ {signal} -> {result}")
        else:
            print(f"❌ {signal} -> {result} (期望: BUY)")
            all_passed = False
    
    print("测试做空信号:")
    for signal in sell_signals:
        result = normalize_signal_direction(signal)
        if result == "SELL":
            print(f"✅ {signal} -> {result}")
        else:
            print(f"❌ {signal} -> {result} (期望: SELL)")
            all_passed = False
    
    return all_passed

def test_alert_validation():
    """测试警报验证"""
    print("\n🔍 测试警报验证...")
    
    from utils.helpers import validate_alert_data
    
    # 有效警报
    valid_alert = {
        "symbol": "XAUUSD",
        "signal_direction": "BUY",
        "timeframe": "1m"
    }
    
    is_valid, error = validate_alert_data(valid_alert)
    if is_valid and error is None:
        print("✅ 有效警报验证通过")
    else:
        print(f"❌ 有效警报验证失败: {error}")
        return False
    
    # 无效警报 - 缺少字段
    invalid_alert = {
        "symbol": "XAUUSD"
        # 缺少 signal_direction
    }
    
    is_valid, error = validate_alert_data(invalid_alert)
    if not is_valid and error is not None:
        print("✅ 无效警报验证通过")
    else:
        print("❌ 无效警报验证失败")
        return False
    
    return True

def test_database_init():
    """测试数据库初始化"""
    print("\n🔍 测试数据库初始化...")
    
    try:
        from database.manager import DatabaseManager
        import tempfile
        import os
        
        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False)
        temp_db.close()
        
        # 初始化数据库
        db_manager = DatabaseManager(temp_db.name)
        db_manager.init_database()
        
        # 测试查询
        result = db_manager.execute_query("SELECT COUNT(*) FROM admin_users")
        
        # 清理
        os.unlink(temp_db.name)
        
        print("✅ 数据库初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n🔍 测试配置管理器...")
    
    try:
        from trading.config_manager import TradingConfigManager
        import tempfile
        import os
        
        # 创建临时数据库
        temp_db = tempfile.NamedTemporaryFile(delete=False)
        temp_db.close()
        
        # 初始化配置管理器
        config_manager = TradingConfigManager()
        
        # 测试获取交易对配置
        config = config_manager.get_symbol_config("XAUUSD")
        if config and "enabled" in config:
            print("✅ 获取交易对配置成功")
        else:
            print("❌ 获取交易对配置失败")
            return False
        
        # 清理
        try:
            os.unlink(temp_db.name)
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_webhook_handler():
    """测试Webhook处理器"""
    print("\n🔍 测试Webhook处理器...")
    
    try:
        from webhook.handler import WebhookHandler
        
        handler = WebhookHandler()
        
        # 测试标准化警报数据
        raw_alert = {
            "symbol": "ETHUSDT",
            "signal_direction": "买入",
            "timeframe": "5m",
            "price": 3000.0,
            "signal_strength": "强"
        }
        
        standardized = handler.standardize_alert_data(raw_alert)
        
        if (standardized["standard_symbol"] == "ETHUSD" and 
            standardized["standard_signal"] == "BUY"):
            print("✅ Webhook处理器测试成功")
            return True
        else:
            print("❌ Webhook处理器测试失败")
            return False
        
    except Exception as e:
        print(f"❌ Webhook处理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 TradingView自动交易系统 - 基础功能测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("交易对标准化", test_symbol_normalization),
        ("信号标准化", test_signal_normalization),
        ("警报验证", test_alert_validation),
        ("数据库初始化", test_database_init),
        ("配置管理器", test_config_manager),
        ("Webhook处理器", test_webhook_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

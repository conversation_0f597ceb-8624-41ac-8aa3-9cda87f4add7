"""
用户认证和会话管理模块
"""
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify
from database.manager import db_manager
from config import Config
from utils.logger import logger

class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.jwt_secret = secrets.token_hex(32)
        self.session_timeout = Config.SESSION_TIMEOUT.total_seconds()
        self.max_login_attempts = Config.MAX_LOGIN_ATTEMPTS
        self.lockout_duration = Config.LOCKOUT_DURATION.total_seconds()
    
    def hash_password(self, password, salt=None):
        """密码哈希加密"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        return password_hash.hex(), salt
    
    def verify_password(self, password, stored_hash, salt):
        """验证密码"""
        password_hash, _ = self.hash_password(password, salt)
        return password_hash == stored_hash
    
    def create_admin_user(self, username, password, email=None):
        """创建管理员用户"""
        try:
            # 检查用户名是否已存在
            existing_user = db_manager.execute_query(
                "SELECT id FROM admin_users WHERE username = ?",
                (username,)
            )
            if existing_user:
                return {"success": False, "message": "用户名已存在"}
            
            # 加密密码
            password_hash, salt = self.hash_password(password)
            
            # 插入用户
            db_manager.execute_query(
                """INSERT INTO admin_users (username, password_hash, salt, email)
                   VALUES (?, ?, ?, ?)""",
                (username, password_hash, salt, email)
            )
            
            logger.info(f"管理员用户创建成功: {username}")
            return {"success": True, "message": "管理员用户创建成功"}
        
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return {"success": False, "message": f"创建用户失败: {str(e)}"}
    
    def authenticate_user(self, username, password, ip_address, user_agent):
        """用户认证"""
        try:
            # 检查用户是否被锁定
            user = db_manager.execute_query(
                """SELECT id, username, password_hash, salt, login_attempts, locked_until
                   FROM admin_users WHERE username = ?""",
                (username,)
            )
            
            if not user:
                self.log_login_attempt(username, ip_address, user_agent, "FAILED", "用户不存在")
                return {"success": False, "message": "用户名或密码错误"}
            
            user = user[0]
            
            # 检查账户锁定状态
            if user[5] and datetime.now() < datetime.fromisoformat(user[5]):
                self.log_login_attempt(username, ip_address, user_agent, "LOCKED", "账户被锁定")
                return {"success": False, "message": "账户已被锁定，请稍后再试"}
            
            # 验证密码
            if not self.verify_password(password, user[2], user[3]):
                # 增加失败次数
                new_attempts = user[4] + 1
                locked_until = None
                
                if new_attempts >= self.max_login_attempts:
                    locked_until = datetime.now() + timedelta(seconds=self.lockout_duration)
                
                db_manager.execute_query(
                    "UPDATE admin_users SET login_attempts = ?, locked_until = ? WHERE id = ?",
                    (new_attempts, locked_until, user[0])
                )
                
                self.log_login_attempt(username, ip_address, user_agent, "FAILED", "密码错误")
                return {"success": False, "message": "用户名或密码错误"}
            
            # 登录成功，重置失败次数
            db_manager.execute_query(
                "UPDATE admin_users SET login_attempts = 0, locked_until = NULL, last_login = ? WHERE id = ?",
                (datetime.now(), user[0])
            )
            
            # 创建会话
            session_token = self.create_session(user[0], ip_address, user_agent)
            
            self.log_login_attempt(username, ip_address, user_agent, "SUCCESS", None)
            
            logger.info(f"用户登录成功: {username}")
            
            return {
                "success": True,
                "message": "登录成功",
                "session_token": session_token,
                "user_id": user[0],
                "username": user[1]
            }
        
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            self.log_login_attempt(username, ip_address, user_agent, "FAILED", f"系统错误: {str(e)}")
            return {"success": False, "message": "登录失败，请稍后再试"}
    
    def create_session(self, user_id, ip_address, user_agent):
        """创建用户会话"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(seconds=self.session_timeout)
        
        db_manager.execute_query(
            """INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at)
               VALUES (?, ?, ?, ?, ?)""",
            (user_id, session_token, ip_address, user_agent, expires_at)
        )
        
        return session_token
    
    def validate_session(self, session_token):
        """验证会话有效性"""
        try:
            session = db_manager.execute_query(
                """SELECT s.user_id, s.expires_at, u.username
                   FROM user_sessions s
                   JOIN admin_users u ON s.user_id = u.id
                   WHERE s.session_token = ?""",
                (session_token,)
            )
            
            if not session:
                return {"valid": False, "message": "会话不存在"}
            
            session = session[0]
            
            if datetime.now() > datetime.fromisoformat(session[1]):
                # 会话过期，删除
                db_manager.execute_query(
                    "DELETE FROM user_sessions WHERE session_token = ?",
                    (session_token,)
                )
                return {"valid": False, "message": "会话已过期"}
            
            return {
                "valid": True,
                "user_id": session[0],
                "username": session[2]
            }
        
        except Exception as e:
            logger.error(f"会话验证失败: {e}")
            return {"valid": False, "message": f"会话验证失败: {str(e)}"}
    
    def logout_user(self, session_token):
        """用户登出"""
        try:
            db_manager.execute_query(
                "DELETE FROM user_sessions WHERE session_token = ?",
                (session_token,)
            )
            return {"success": True, "message": "登出成功"}
        except Exception as e:
            logger.error(f"登出失败: {e}")
            return {"success": False, "message": f"登出失败: {str(e)}"}
    
    def log_login_attempt(self, username, ip_address, user_agent, result, failure_reason):
        """记录登录尝试"""
        try:
            db_manager.execute_query(
                """INSERT INTO login_logs (username, ip_address, user_agent, login_result, failure_reason)
                   VALUES (?, ?, ?, ?, ?)""",
                (username, ip_address, user_agent, result, failure_reason)
            )
        except Exception as e:
            logger.error(f"记录登录日志失败: {e}")
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            deleted_count = db_manager.execute_query(
                "DELETE FROM user_sessions WHERE expires_at < ?",
                (datetime.now(),)
            )
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 个过期会话")
        except Exception as e:
            logger.error(f"清理过期会话失败: {e}")
    
    def change_password(self, user_id, current_password, new_password):
        """修改密码"""
        try:
            # 验证当前密码
            user = db_manager.execute_query(
                "SELECT password_hash, salt FROM admin_users WHERE id = ?",
                (user_id,)
            )
            
            if not user:
                return {"success": False, "message": "用户不存在"}
            
            user = user[0]
            
            if not self.verify_password(current_password, user[0], user[1]):
                return {"success": False, "message": "当前密码错误"}
            
            # 更新密码
            new_hash, new_salt = self.hash_password(new_password)
            db_manager.execute_query(
                "UPDATE admin_users SET password_hash = ?, salt = ? WHERE id = ?",
                (new_hash, new_salt, user_id)
            )
            
            logger.info(f"用户 {user_id} 密码修改成功")
            return {"success": True, "message": "密码修改成功"}
        
        except Exception as e:
            logger.error(f"密码修改失败: {e}")
            return {"success": False, "message": f"密码修改失败: {str(e)}"}

# 创建全局认证管理器实例
auth_manager = AuthManager()

# 认证装饰器
def require_auth(f):
    """API认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        session_token = request.headers.get('Authorization')
        if not session_token:
            session_token = request.cookies.get('session_token')

        if not session_token:
            return jsonify({"error": "未提供认证信息"}), 401

        # 移除 Bearer 前缀（如果存在）
        if session_token.startswith('Bearer '):
            session_token = session_token[7:]

        auth_result = auth_manager.validate_session(session_token)
        if not auth_result["valid"]:
            return jsonify({"error": auth_result["message"]}), 401

        # 将用户信息添加到请求上下文
        request.current_user = {
            "user_id": auth_result["user_id"],
            "username": auth_result["username"]
        }

        return f(*args, **kwargs)
    return decorated_function

def require_login(f):
    """页面认证装饰器 - 未登录时重定向到登录页"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查多种方式的session token
        session_token = None

        # 1. 检查Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            session_token = auth_header[7:]

        # 2. 检查Cookie
        if not session_token:
            session_token = request.cookies.get('session_token')

        # 3. 检查localStorage/sessionStorage (通过JavaScript传递)
        if not session_token:
            # 这里可以通过JavaScript在页面加载时检查
            pass

        if not session_token:
            # 重定向到登录页面
            from flask import redirect, url_for
            return redirect(url_for('login_page'))

        # 验证session token
        auth_result = auth_manager.validate_session(session_token)
        if not auth_result["valid"]:
            # session无效，重定向到登录页面
            from flask import redirect, url_for
            return redirect(url_for('login_page'))

        # 将用户信息添加到请求上下文
        request.current_user = {
            "user_id": auth_result["user_id"],
            "username": auth_result["username"]
        }

        return f(*args, **kwargs)
    return decorated_function

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 600;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .btn-action {
            margin: 5px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .profit-positive {
            color: #28a745;
            font-weight: 600;
        }
        .profit-negative {
            color: #dc3545;
            font-weight: 600;
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i> TradingView自动交易系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3" id="userInfo">
                    <i class="bi bi-person-circle"></i> <span id="username">加载中...</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i> 登出
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 系统状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value" id="accountBalance">$0.00</div>
                        <div class="metric-label">账户余额</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value" id="totalPositions">0</div>
                        <div class="metric-label">当前持仓</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value" id="totalPnL">$0.00</div>
                        <div class="metric-label">总盈亏</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value">
                            <span class="status-indicator" id="systemStatus"></span>
                            <span id="systemStatusText">离线</span>
                        </div>
                        <div class="metric-label">系统状态</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-gear"></i> 系统控制</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success btn-action" onclick="startStrategy()">
                            <i class="bi bi-play-fill"></i> 启动策略
                        </button>
                        <button class="btn btn-warning btn-action" onclick="stopStrategy()">
                            <i class="bi bi-pause-fill"></i> 停止策略
                        </button>
                        <button class="btn btn-danger btn-action" onclick="emergencyStop()">
                            <i class="bi bi-stop-fill"></i> 紧急停止
                        </button>
                        <button class="btn btn-info btn-action" onclick="refreshData()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                        <button class="btn btn-secondary btn-action" onclick="showConfigModal()">
                            <i class="bi bi-sliders"></i> 系统配置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 标签页 -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="positions-tab" data-bs-toggle="tab" data-bs-target="#positions" type="button" role="tab">
                    <i class="bi bi-list-ul"></i> 当前持仓
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trades-tab" data-bs-toggle="tab" data-bs-target="#trades" type="button" role="tab">
                    <i class="bi bi-clock-history"></i> 交易历史
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                    <i class="bi bi-bell"></i> 警报记录
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
                    <i class="bi bi-gear"></i> 交易对配置
                </button>
            </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="mainTabsContent">
            <!-- 当前持仓 -->
            <div class="tab-pane fade show active" id="positions" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">当前持仓列表</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="positionsTable">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>当前价</th>
                                        <th>盈亏</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易历史 -->
            <div class="tab-pane fade" id="trades" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">交易历史记录</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="tradesTable">
                                <thead>
                                    <tr>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>平仓价</th>
                                        <th>盈亏</th>
                                        <th>开仓时间</th>
                                        <th>平仓时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 警报记录 -->
            <div class="tab-pane fade" id="alerts" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">TradingView警报记录</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="alertsTable">
                                <thead>
                                    <tr>
                                        <th>交易对</th>
                                        <th>信号</th>
                                        <th>时间框架</th>
                                        <th>价格</th>
                                        <th>信号强度</th>
                                        <th>接收时间</th>
                                        <th>处理状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="7" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易对配置 -->
            <div class="tab-pane fade" id="config" role="tabpanel">
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">交易对配置管理</h6>
                    </div>
                    <div class="card-body">
                        <div id="symbolConfigContainer">
                            <div class="text-center">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-spinner position-fixed top-50 start-50 translate-middle" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统 - 调试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>🔍 认证调试信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo">
                            <p>正在检查认证状态...</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="checkAuth()">重新检查认证</button>
                            <button class="btn btn-secondary" onclick="clearTokens()">清除Token</button>
                            <button class="btn btn-success" onclick="goToDashboard()">进入仪表板</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugInfo').innerHTML = '';
        }

        async function checkAuth() {
            clearLog();
            log('开始认证检查...');

            // 1. 检查Token存储
            const localToken = localStorage.getItem('session_token');
            const sessionToken = sessionStorage.getItem('session_token');
            
            log('LocalStorage Token: ' + (localToken ? localToken.substring(0, 20) + '...' : '无'));
            log('SessionStorage Token: ' + (sessionToken ? sessionToken.substring(0, 20) + '...' : '无'));

            const token = localToken || sessionToken;
            if (!token) {
                log('❌ 没有找到Token，需要登录');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 3000);
                return;
            }

            log('✅ 找到Token: ' + token.substring(0, 20) + '...');

            // 2. 验证Token
            try {
                log('正在验证Token...');
                const response = await fetch('/api/auth/validate', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('验证响应状态: ' + response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Token验证成功');
                    log('用户信息: ' + JSON.stringify(data));
                    log('🎉 认证通过，可以访问系统');
                } else {
                    const errorText = await response.text();
                    log('❌ Token验证失败: ' + errorText);
                    log('清除无效Token...');
                    localStorage.removeItem('session_token');
                    sessionStorage.removeItem('session_token');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 3000);
                }
            } catch (error) {
                log('❌ 验证请求失败: ' + error.message);
                setTimeout(() => {
                    window.location.href = '/login';
                }, 3000);
            }
        }

        function clearTokens() {
            localStorage.removeItem('session_token');
            sessionStorage.removeItem('session_token');
            log('🗑️ 已清除所有Token');
        }

        function goToDashboard() {
            window.location.href = '/dashboard';
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
</body>
</html>

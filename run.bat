@echo off
title TradingView Trading System

echo Starting TradingView Trading System...
echo.

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Please install Python first.
    pause
    exit
)

if not exist "app.py" (
    echo app.py not found. Please run in project directory.
    pause
    exit
)

echo Installing dependencies...
pip install flask flask-cors requests schedule MetaTrader5 >nul 2>&1

echo.
echo System starting...
echo Web: http://localhost:5000
echo Webhook: http://localhost:7000/webhook/tradingview
echo.

python app.py

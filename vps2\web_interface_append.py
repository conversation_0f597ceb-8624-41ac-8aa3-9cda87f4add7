# 路由: API - 获取浮动盈亏监控状态
@app.route('/api/floating_pl_monitor/status')
@login_required
def api_floating_pl_monitor_status():
    """获取浮动盈亏监控状态"""
    try:
        is_running = floating_pl_monitor.is_floating_pl_monitor_running()
        return jsonify({
            'success': True,
            'monitoring': is_running,
            'status': 'running' if is_running else 'stopped'
        })
    except Exception as e:
        logger.error(f"获取浮动盈亏监控状态失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取监控状态失败: {str(e)}'})

# 路由: API - 启动/停止浮动盈亏监控
@app.route('/api/floating_pl_monitor/control', methods=['POST'])
@login_required
def api_floating_pl_monitor_control():
    """启动或停止浮动盈亏监控"""
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})
        
        action = data.get('action')
        
        if action == 'start':
            result = floating_pl_monitor.start_floating_pl_monitor()
            if result:
                return jsonify({'success': True, 'message': '浮动盈亏监控已启动'})
            else:
                return jsonify({'success': False, 'message': '启动浮动盈亏监控失败'})
        
        elif action == 'stop':
            result = floating_pl_monitor.stop_floating_pl_monitor()
            if result:
                return jsonify({'success': True, 'message': '浮动盈亏监控已停止'})
            else:
                return jsonify({'success': False, 'message': '停止浮动盈亏监控失败'})
        
        else:
            return jsonify({'success': False, 'message': '无效的操作类型'})
            
    except Exception as e:
        logger.error(f"控制浮动盈亏监控失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'控制监控失败: {str(e)}'})

if __name__ == '__main__':
    try:
        # 启动浮动盈亏监控
        logger.info("启动浮动盈亏监控...")
        if floating_pl_monitor.start_floating_pl_monitor():
            logger.info("浮动盈亏监控启动成功")
        else:
            logger.warning("浮动盈亏监控启动失败")
        
        logger.info(f"Web界面启动，端口: {WEB_PORT}")
        app.run(host='0.0.0.0', port=WEB_PORT, debug=True)
    except Exception as e:
        logger.error(f"Web界面启动失败: {e}", exc_info=True)
    finally:
        # 确保在程序退出时停止监控
        try:
            floating_pl_monitor.stop_floating_pl_monitor()
            logger.info("浮动盈亏监控已停止")
        except Exception as e:
            logger.error(f"停止浮动盈亏监控失败: {e}", exc_info=True)

"""
Webhook处理模块测试
"""
import unittest
import json
from unittest.mock import patch, MagicMock
from webhook.handler import <PERSON>hookHandler
from utils.helpers import normalize_symbol, normalize_signal_direction, validate_alert_data

class TestWebhookHandler(unittest.TestCase):
    """Webhook处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.webhook_handler = WebhookHandler()
    
    def test_normalize_symbol(self):
        """测试交易对标准化"""
        # 测试标准格式
        self.assertEqual(normalize_symbol("XAUUSD"), "XAUUSD")
        self.assertEqual(normalize_symbol("ETHUSD"), "ETHUSD")
        
        # 测试变体格式
        self.assertEqual(normalize_symbol("ETHUSDT"), "ETHUSD")
        self.assertEqual(normalize_symbol("ETH-USD"), "ETHUSD")
        self.assertEqual(normalize_symbol("ETH/USD"), "ETHUSD")
        self.assertEqual(normalize_symbol("eth_usd"), "ETHUSD")
        
        # 测试未知格式
        self.assertEqual(normalize_symbol("UNKNOWN"), "UNKNOWN")
    
    def test_normalize_signal_direction(self):
        """测试信号方向标准化"""
        # 测试做多信号
        self.assertEqual(normalize_signal_direction("BUY"), "BUY")
        self.assertEqual(normalize_signal_direction("buy"), "BUY")
        self.assertEqual(normalize_signal_direction("LONG"), "BUY")
        self.assertEqual(normalize_signal_direction("long"), "BUY")
        self.assertEqual(normalize_signal_direction("UP"), "BUY")
        self.assertEqual(normalize_signal_direction("买入"), "BUY")
        self.assertEqual(normalize_signal_direction("做多"), "BUY")
        self.assertEqual(normalize_signal_direction("综合做多信号"), "BUY")
        
        # 测试做空信号
        self.assertEqual(normalize_signal_direction("SELL"), "SELL")
        self.assertEqual(normalize_signal_direction("sell"), "SELL")
        self.assertEqual(normalize_signal_direction("SHORT"), "SELL")
        self.assertEqual(normalize_signal_direction("short"), "SELL")
        self.assertEqual(normalize_signal_direction("DOWN"), "SELL")
        self.assertEqual(normalize_signal_direction("卖出"), "SELL")
        self.assertEqual(normalize_signal_direction("做空"), "SELL")
        self.assertEqual(normalize_signal_direction("综合做空信号"), "SELL")
        
        # 测试未知信号
        self.assertEqual(normalize_signal_direction("UNKNOWN"), "UNKNOWN")
    
    def test_validate_alert_data(self):
        """测试警报数据验证"""
        # 测试有效数据
        valid_alert = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m",
            "price": 2000.50
        }
        
        is_valid, error = validate_alert_data(valid_alert)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # 测试缺少必需字段
        invalid_alert = {
            "symbol": "XAUUSD",
            "timeframe": "1m"
            # 缺少 signal_direction
        }
        
        is_valid, error = validate_alert_data(invalid_alert)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # 测试不支持的交易对
        unsupported_alert = {
            "symbol": "UNSUPPORTED",
            "signal_direction": "BUY",
            "timeframe": "1m"
        }
        
        is_valid, error = validate_alert_data(unsupported_alert)
        self.assertFalse(is_valid)
        self.assertIn("不支持的交易对", error)
        
        # 测试无效信号方向
        invalid_signal_alert = {
            "symbol": "XAUUSD",
            "signal_direction": "INVALID",
            "timeframe": "1m"
        }
        
        is_valid, error = validate_alert_data(invalid_signal_alert)
        self.assertFalse(is_valid)
        self.assertIn("无效的信号方向", error)
    
    def test_standardize_alert_data(self):
        """测试警报数据标准化"""
        raw_alert = {
            "symbol": "ETHUSDT",
            "signal_direction": "买入",
            "timeframe": "5m",
            "price": 3000.0,
            "signal_strength": "强"
        }
        
        standardized = self.webhook_handler.standardize_alert_data(raw_alert)
        
        self.assertEqual(standardized["original_symbol"], "ETHUSDT")
        self.assertEqual(standardized["standard_symbol"], "ETHUSD")
        self.assertEqual(standardized["original_signal"], "买入")
        self.assertEqual(standardized["standard_signal"], "BUY")
        self.assertEqual(standardized["timeframe"], "5m")
        self.assertEqual(standardized["price"], 3000.0)
    
    @patch('webhook.handler.db_manager')
    def test_save_alert_record(self, mock_db):
        """测试保存警报记录"""
        mock_db.execute_query.return_value = None
        
        processed_alert = {
            "original_symbol": "ETHUSDT",
            "standard_symbol": "ETHUSD",
            "original_signal": "买入",
            "standard_signal": "BUY",
            "timeframe": "5m",
            "signal_strength": "强",
            "price": 3000.0,
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        raw_data = {"test": "data"}
        
        alert_id = self.webhook_handler.save_alert_record(processed_alert, raw_data)
        
        self.assertIsNotNone(alert_id)
        mock_db.execute_query.assert_called_once()
    
    @patch('webhook.handler.db_manager')
    def test_process_alert(self, mock_db):
        """测试处理警报"""
        mock_db.execute_query.return_value = None
        
        alert_data = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m",
            "price": 2000.0
        }
        
        result = self.webhook_handler.process_alert(alert_data)
        
        self.assertTrue(result["success"])
        self.assertIn("alert_id", result)
        self.assertIn("processed_data", result)

class TestAlertStatistics(unittest.TestCase):
    """警报统计测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.webhook_handler = WebhookHandler()
    
    @patch('webhook.handler.db_manager')
    def test_get_alert_statistics(self, mock_db):
        """测试获取警报统计"""
        # 模拟数据库返回
        mock_db.execute_query.side_effect = [
            [(10,)],  # 总警报数
            [("XAUUSD", 5), ("ETHUSD", 3), ("BTCUSD", 2)],  # 按交易对统计
            [("BUY", 6), ("SELL", 4)]  # 按信号方向统计
        ]
        
        stats = self.webhook_handler.get_alert_statistics(7)
        
        self.assertEqual(stats["total_alerts"], 10)
        self.assertEqual(len(stats["symbol_statistics"]), 3)
        self.assertEqual(len(stats["signal_statistics"]), 2)
        self.assertEqual(stats["period_days"], 7)
        
        # 验证统计数据
        symbol_stats = {item["symbol"]: item["count"] for item in stats["symbol_statistics"]}
        self.assertEqual(symbol_stats["XAUUSD"], 5)
        self.assertEqual(symbol_stats["ETHUSD"], 3)
        
        signal_stats = {item["signal"]: item["count"] for item in stats["signal_statistics"]}
        self.assertEqual(signal_stats["BUY"], 6)
        self.assertEqual(signal_stats["SELL"], 4)

if __name__ == '__main__':
    unittest.main()

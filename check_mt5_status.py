#!/usr/bin/env python3
"""
快速检查MT5状态
"""
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🔍 快速MT5状态检查...")
        
        # 导入MT5模块
        import MetaTrader5 as mt5
        
        # 检查初始化状态
        terminal_info = mt5.terminal_info()
        if not terminal_info:
            print("❌ MT5未初始化，尝试初始化...")
            if mt5.initialize():
                print("✅ MT5初始化成功")
                terminal_info = mt5.terminal_info()
            else:
                print(f"❌ MT5初始化失败: {mt5.last_error()}")
                return
        
        # 显示终端信息
        print(f"📍 终端路径: {terminal_info.path}")
        print(f"📊 终端版本: {terminal_info.build}")
        print(f"🌐 连接状态: {'已连接' if terminal_info.connected else '未连接'}")
        
        # 检查账户信息
        account_info = mt5.account_info()
        if account_info:
            print(f"💰 账户余额: ${account_info.balance:.2f}")
            print(f"📊 账户杠杆: 1:{account_info.leverage}")
            print(f"🏢 经纪商: {account_info.company}")
            print(f"🔢 账户号: {account_info.login}")
        else:
            print("❌ 无法获取账户信息")
        
        # 测试系统连接管理器
        print("\n🔍 测试系统连接管理器...")
        from mt5.connection import MT5Connection
        
        connection = MT5Connection()
        is_connected = connection.check_connection()
        print(f"🔗 系统连接状态: {'已连接' if is_connected else '未连接'}")
        
        if is_connected:
            account = connection.get_account_info()
            if account:
                print(f"💰 系统获取余额: ${account.get('balance', 0):.2f}")
        
        print("\n🎉 MT5状态检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

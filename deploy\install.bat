@echo off
REM TradingView自动交易系统 Windows安装脚本
echo ========================================
echo TradingView自动交易系统安装程序
echo ========================================

REM 检查Python是否已安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到pip，请确保Python安装正确
    pause
    exit /b 1
)

echo.
echo 正在创建虚拟环境...
python -m venv venv
if errorlevel 1 (
    echo 错误: 创建虚拟环境失败
    pause
    exit /b 1
)

echo.
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 正在升级pip...
python -m pip install --upgrade pip

echo.
echo 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 安装依赖包失败
    pause
    exit /b 1
)

echo.
echo 正在创建配置文件...
if not exist .env (
    copy .env.example .env
    echo 已创建配置文件 .env，请编辑此文件填入您的配置信息
) else (
    echo 配置文件 .env 已存在，跳过创建
)

echo.
echo 正在创建日志目录...
if not exist logs mkdir logs

echo.
echo 正在创建静态文件目录...
if not exist static\css mkdir static\css
if not exist static\js mkdir static\js
if not exist static\images mkdir static\images

echo.
echo 正在测试系统...
python -c "from app import create_app; print('系统测试通过')"
if errorlevel 1 (
    echo 错误: 系统测试失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 编辑 .env 文件，填入您的MT5账户信息和Bark密钥
echo 2. 确保MetaTrader5已安装并可以正常登录
echo 3. 运行 start.bat 启动系统
echo.
echo 配置文件位置: .env
echo 启动脚本: start.bat
echo 停止脚本: stop.bat
echo.
pause

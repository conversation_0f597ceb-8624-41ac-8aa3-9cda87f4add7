"""
配置管理模块测试
"""
import unittest
import tempfile
import os
from trading.config_manager import TradingConfigManager
from database.manager import DatabaseManager

class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.init_database()
        
        # 初始化配置管理器
        self.config_manager = TradingConfigManager()
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时数据库
        os.unlink(self.temp_db.name)
    
    def test_get_symbol_config(self):
        """测试获取交易对配置"""
        # 测试获取存在的交易对配置
        config = self.config_manager.get_symbol_config("XAUUSD")
        self.assertIsNotNone(config)
        self.assertIn("enabled", config)
        self.assertIn("lot_size", config)
        self.assertIn("stop_loss_usd", config)
        self.assertIn("take_profit_usd", config)
        
        # 测试获取不存在的交易对配置
        config = self.config_manager.get_symbol_config("NONEXISTENT")
        self.assertIsNotNone(config)
        self.assertFalse(config["enabled"])
    
    def test_update_symbol_config(self):
        """测试更新交易对配置"""
        new_config = {
            "enabled": True,
            "lot_size": 0.02,
            "stop_loss_usd": 60.0,
            "take_profit_usd": 120.0,
            "signal_timeout": 200,
            "category": "test"
        }
        
        result = self.config_manager.update_symbol_config("XAUUSD", new_config)
        self.assertTrue(result["success"])
        
        # 验证更新后的配置
        updated_config = self.config_manager.get_symbol_config("XAUUSD")
        self.assertEqual(updated_config["lot_size"], 0.02)
        self.assertEqual(updated_config["stop_loss_usd"], 60.0)
    
    def test_system_config(self):
        """测试系统配置"""
        # 测试更新系统配置
        result = self.config_manager.update_system_config("test_key", "test_value")
        self.assertTrue(result["success"])
        
        # 测试获取系统配置
        value = self.config_manager.get_system_config("test_key")
        self.assertEqual(value, "test_value")
        
        # 测试获取所有系统配置
        all_config = self.config_manager.get_system_config()
        self.assertIsInstance(all_config, dict)
        self.assertIn("test_key", all_config)
    
    def test_portfolio_config(self):
        """测试组合配置"""
        # 测试获取组合配置
        config = self.config_manager.get_portfolio_config()
        self.assertIsNotNone(config)
        self.assertIn("enabled", config)
        self.assertIn("global_stop_loss_usd", config)
        
        # 测试更新组合配置
        new_config = {
            "enabled": True,
            "global_stop_loss_usd": 600.0,
            "global_take_profit_usd": 1200.0,
            "check_interval_seconds": 45
        }
        
        result = self.config_manager.update_portfolio_config(new_config)
        self.assertTrue(result["success"])
        
        # 验证更新后的配置
        updated_config = self.config_manager.get_portfolio_config()
        self.assertEqual(updated_config["global_stop_loss_usd"], 600.0)

class TestConfigValidation(unittest.TestCase):
    """配置验证测试类"""
    
    def test_symbol_config_validation(self):
        """测试交易对配置验证"""
        # 测试有效配置
        valid_config = {
            "enabled": True,
            "lot_size": 0.01,
            "stop_loss_usd": 50.0,
            "take_profit_usd": 100.0,
            "signal_timeout": 180,
            "category": "crypto"
        }
        
        # 这里应该添加配置验证逻辑
        self.assertTrue(self._validate_symbol_config(valid_config))
        
        # 测试无效配置
        invalid_config = {
            "enabled": True,
            "lot_size": -0.01,  # 负数手数
            "stop_loss_usd": 50.0,
            "take_profit_usd": 100.0,
            "signal_timeout": 180,
            "category": "crypto"
        }
        
        self.assertFalse(self._validate_symbol_config(invalid_config))
    
    def _validate_symbol_config(self, config):
        """验证交易对配置"""
        try:
            # 检查必需字段
            required_fields = ["enabled", "lot_size", "stop_loss_usd", "take_profit_usd", "signal_timeout"]
            for field in required_fields:
                if field not in config:
                    return False
            
            # 检查数值范围
            if config["lot_size"] <= 0:
                return False
            if config["stop_loss_usd"] < 0:
                return False
            if config["take_profit_usd"] < 0:
                return False
            if config["signal_timeout"] <= 0:
                return False
            
            return True
        except Exception:
            return False

if __name__ == '__main__':
    unittest.main()

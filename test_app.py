#!/usr/bin/env python3
"""
Flask应用测试脚本
"""
import sys
import os
import tempfile
import threading
import time
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_flask_app():
    """测试Flask应用启动"""
    print("🔍 测试Flask应用启动...")
    
    try:
        # 设置测试环境
        os.environ['TESTING_MODE'] = 'True'
        os.environ['FLASK_DEBUG'] = 'False'
        
        from app import create_app
        
        # 创建测试应用
        app = create_app('testing')
        
        # 在后台线程中启动应用
        def run_app():
            app.run(host='127.0.0.1', port=7001, debug=False, use_reloader=False)
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # 等待应用启动
        time.sleep(3)
        
        # 测试健康检查端点
        try:
            response = requests.get('http://127.0.0.1:7001/api/system/health', timeout=5)
            if response.status_code == 200:
                print("✅ Flask应用启动成功，健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败，状态码: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到应用: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Flask应用启动失败: {e}")
        return False

def test_webhook_endpoint():
    """测试Webhook端点"""
    print("\n🔍 测试Webhook端点...")
    
    try:
        # 测试数据
        test_alert = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m",
            "price": 2000.0,
            "signal_strength": "强"
        }
        
        # 发送测试请求
        response = requests.post(
            'http://127.0.0.1:7001/webhook/tradingview',
            json=test_alert,
            timeout=10
        )
        
        if response.status_code in [200, 400]:  # 200成功或400业务逻辑错误都算正常
            print("✅ Webhook端点响应正常")
            return True
        else:
            print(f"❌ Webhook端点异常，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Webhook测试失败: {e}")
        return False

def test_login_endpoint():
    """测试登录端点"""
    print("\n🔍 测试登录端点...")
    
    try:
        # 测试登录
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            'http://127.0.0.1:7001/api/auth/login',
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'session_token' in data:
                print("✅ 登录端点测试成功")
                return True, data['session_token']
            else:
                print("❌ 登录响应格式错误")
                return False, None
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录测试失败: {e}")
        return False, None

def test_protected_endpoint(token):
    """测试受保护的端点"""
    print("\n🔍 测试受保护的端点...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        response = requests.get(
            'http://127.0.0.1:7001/api/config/symbols',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 受保护端点测试成功")
                return True
            else:
                print("❌ 受保护端点响应格式错误")
                return False
        else:
            print(f"❌ 受保护端点访问失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 受保护端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 TradingView自动交易系统 - Flask应用测试")
    print("=" * 60)
    
    # 测试Flask应用启动
    if not test_flask_app():
        print("❌ Flask应用启动失败，停止后续测试")
        return False
    
    # 测试各个端点
    tests_passed = 0
    total_tests = 0
    
    # 测试Webhook端点
    total_tests += 1
    if test_webhook_endpoint():
        tests_passed += 1
    
    # 测试登录端点
    total_tests += 1
    login_success, token = test_login_endpoint()
    if login_success:
        tests_passed += 1
        
        # 如果登录成功，测试受保护端点
        total_tests += 1
        if test_protected_endpoint(token):
            tests_passed += 1
    else:
        total_tests += 1  # 跳过受保护端点测试
    
    print("\n" + "=" * 60)
    print(f"📊 Flask应用测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 Flask应用所有测试通过！")
        return True
    else:
        print("⚠️ 部分Flask应用测试失败")
        return False

if __name__ == '__main__':
    success = main()
    print("\n💡 提示: 如果测试通过，您可以运行 'python run.py' 启动完整系统")
    sys.exit(0 if success else 1)

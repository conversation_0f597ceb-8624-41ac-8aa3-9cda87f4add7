#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
初始化浮动盈亏规则表
"""

import sqlite3
import logging

def init_profit_loss_rules_table():
    """初始化浮动盈亏规则表"""
    try:
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        
        # 创建浮动盈亏总规则表
        c.execute('''
            CREATE TABLE IF NOT EXISTS profit_loss_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_type TEXT NOT NULL, -- 'total' 总规则, 'individual' 单个订单规则
                enabled INTEGER DEFAULT 0, -- 是否启用
                profit_threshold REAL, -- 盈利阈值(美元)
                loss_threshold REAL, -- 亏损阈值(美元，负数)
                ticket INTEGER, -- 订单号(仅单个订单规则需要)
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 检查是否已存在总规则，如果没有则插入默认值
        c.execute("SELECT COUNT(*) FROM profit_loss_rules WHERE rule_type = 'total'")
        total_rules_count = c.fetchone()[0]

        if total_rules_count == 0:
            c.execute('''
                INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold)
                VALUES ('total', 0, 1000.0, -500.0)
            ''')
            print("已插入默认总浮动盈亏规则：盈利1000美元/亏损500美元时平仓所有订单（默认关闭）")

        # 检查是否已存在全局止盈止损规则，如果没有则插入默认值
        c.execute("SELECT COUNT(*) FROM profit_loss_rules WHERE rule_type = 'global_sl_tp'")
        global_sl_tp_count = c.fetchone()[0]

        if global_sl_tp_count == 0:
            c.execute('''
                INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold)
                VALUES ('global_sl_tp', 0, 100.0, -50.0)
            ''')
            print("已插入默认全局止盈止损规则：每个订单盈利100美元/亏损50美元时自动平仓（默认关闭）")
        
        conn.commit()
        conn.close()
        print("浮动盈亏规则表初始化完成")
        
    except Exception as e:
        print(f"初始化浮动盈亏规则表失败: {e}")

if __name__ == '__main__':
    init_profit_loss_rules_table()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
余额监测功能测试脚本
"""

import json
import time
import requests
from datetime import datetime

def test_config_structure():
    """测试配置文件结构"""
    print("=== 测试配置文件结构 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查余额监测配置
        balance_monitoring = config.get('balance_monitoring', {})
        
        print(f"✅ 余额监测总开关: {'启用' if balance_monitoring.get('enabled', True) else '禁用'}")
        
        # 检查余额变动通知配置
        balance_change = balance_monitoring.get('balance_change_notification', {})
        print(f"✅ 余额变动通知: {'启用' if balance_change.get('enabled', True) else '禁用'}")
        print(f"   最小变动金额: {balance_change.get('min_change_amount', 1.0)} USD")
        
        # 检查盈亏通知配置
        profit_loss = balance_monitoring.get('profit_loss_notification', {})
        print(f"✅ 盈亏通知: {'启用' if profit_loss.get('enabled', True) else '禁用'}")
        print(f"   盈利阈值: {profit_loss.get('profit_threshold', 50.0)} USD")
        print(f"   亏损阈值: {profit_loss.get('loss_threshold', 50.0)} USD")
        
        # 检查定时推送配置
        periodic = balance_monitoring.get('periodic_balance_notification', {})
        print(f"✅ 定时余额推送: {'启用' if periodic.get('enabled', True) else '禁用'}")
        print(f"   推送间隔: {periodic.get('interval_hours', 4)} 小时")
        print(f"   推送时间: {periodic.get('start_time', '08:00')} - {periodic.get('end_time', '22:00')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_balance_monitor_module():
    """测试余额监测模块"""
    print("\n=== 测试余额监测模块 ===")
    
    try:
        import balance_monitor
        
        # 测试模块导入
        print("✅ 余额监测模块导入成功")
        
        # 测试配置加载
        balance_monitor.balance_monitor.load_config()
        print("✅ 配置加载成功")
        
        # 测试数据库初始化
        balance_monitor.balance_monitor.init_database()
        print("✅ 数据库初始化成功")
        
        # 测试账户信息获取
        account_info = balance_monitor.balance_monitor.get_account_info()
        if account_info:
            print(f"✅ 账户信息获取成功:")
            print(f"   余额: ${account_info['balance']:.2f}")
            print(f"   净值: ${account_info['equity']:.2f}")
            print(f"   浮动盈亏: {account_info['profit']:+.2f} USD")
        else:
            print("⚠️ 无法获取账户信息，可能MT5未连接")
        
        # 测试监测状态
        status = balance_monitor.get_balance_monitoring_status()
        print(f"✅ 监测状态获取成功:")
        print(f"   功能启用: {status['enabled']}")
        print(f"   服务运行: {status['running']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 余额监测模块测试失败: {e}")
        return False

def test_notification_functions():
    """测试通知功能"""
    print("\n=== 测试通知功能 ===")
    
    try:
        import balance_monitor
        
        # 获取账户信息
        account_info = balance_monitor.balance_monitor.get_account_info()
        if not account_info:
            print("⚠️ 无法获取账户信息，跳过通知测试")
            return False
        
        print("开始测试各种通知功能...")
        
        # 测试余额变动通知
        print("📊 测试余额变动通知...")
        balance_monitor.balance_monitor.send_balance_change_notification(account_info, 15.75)
        time.sleep(2)
        
        # 测试盈利通知
        print("💰 测试盈利通知...")
        balance_monitor.balance_monitor.send_profit_notification(account_info, 85.50)
        time.sleep(2)
        
        # 测试亏损通知
        print("⚠️ 测试亏损通知...")
        balance_monitor.balance_monitor.send_loss_notification(account_info, 65.25)
        time.sleep(2)
        
        # 测试定时余额推送
        print("⏰ 测试定时余额推送...")
        balance_monitor.balance_monitor.send_periodic_balance_notification()
        
        print("✅ 所有通知功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 通知功能测试失败: {e}")
        return False

def test_web_api():
    """测试Web API"""
    print("\n=== 测试Web API ===")
    
    base_url = "http://localhost:8080"
    
    # 测试API列表
    test_apis = [
        {
            'name': '余额监测状态',
            'url': f'{base_url}/api/balance_monitor/status',
            'method': 'GET'
        },
        {
            'name': '余额历史记录',
            'url': f'{base_url}/api/balance_history?days=7',
            'method': 'GET'
        },
        {
            'name': '余额变动通知测试',
            'url': f'{base_url}/api/test_balance_notification',
            'method': 'POST',
            'data': {'type': 'balance_change'}
        },
        {
            'name': '盈利通知测试',
            'url': f'{base_url}/api/test_balance_notification',
            'method': 'POST',
            'data': {'type': 'profit'}
        },
        {
            'name': '亏损通知测试',
            'url': f'{base_url}/api/test_balance_notification',
            'method': 'POST',
            'data': {'type': 'loss'}
        },
        {
            'name': '定时推送测试',
            'url': f'{base_url}/api/test_balance_notification',
            'method': 'POST',
            'data': {'type': 'periodic'}
        }
    ]
    
    success_count = 0
    
    for api in test_apis:
        try:
            print(f"测试 {api['name']}...")
            
            if api['method'] == 'GET':
                response = requests.get(api['url'], timeout=10)
            else:
                response = requests.post(api['url'], json=api['data'], timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {api['name']}: 成功")
                    success_count += 1
                else:
                    print(f"❌ {api['name']}: {result.get('message', '未知错误')}")
            else:
                print(f"❌ {api['name']}: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"⚠️ {api['name']}: Web服务器未运行")
        except Exception as e:
            print(f"❌ {api['name']}: {e}")
    
    print(f"\nAPI测试结果: {success_count}/{len(test_apis)} 成功")
    return success_count > 0

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    try:
        import balance_monitor
        import sqlite3
        
        # 测试数据库连接
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        
        # 检查余额历史表是否存在
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='balance_history'")
        if c.fetchone():
            print("✅ balance_history表存在")
        else:
            print("❌ balance_history表不存在")
            return False
        
        # 检查表结构
        c.execute("PRAGMA table_info(balance_history)")
        columns = c.fetchall()
        expected_columns = ['id', 'timestamp', 'balance', 'equity', 'profit', 'margin', 'free_margin', 'margin_level', 'change_amount', 'change_type', 'notification_sent']
        
        actual_columns = [col[1] for col in columns]
        missing_columns = [col for col in expected_columns if col not in actual_columns]
        
        if missing_columns:
            print(f"❌ 缺少列: {missing_columns}")
            return False
        else:
            print("✅ 表结构正确")
        
        # 测试数据插入
        account_info = {
            'balance': 1000.0,
            'equity': 1050.0,
            'profit': 50.0,
            'margin': 100.0,
            'free_margin': 900.0,
            'margin_level': 1050.0
        }
        
        balance_monitor.balance_monitor.save_balance_record(account_info, 25.0, "测试记录", True)
        print("✅ 数据插入成功")
        
        # 测试数据查询
        history = balance_monitor.get_balance_history(1)
        if history:
            print(f"✅ 数据查询成功，找到 {len(history)} 条记录")
        else:
            print("⚠️ 未找到历史记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始余额监测功能全面测试...")
    print("=" * 60)
    
    tests = [
        ("配置文件结构", test_config_structure),
        ("余额监测模块", test_balance_monitor_module),
        ("数据库操作", test_database_operations),
        ("通知功能", test_notification_functions),
        ("Web API", test_web_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有测试通过！余额监测功能已准备就绪。")
        print("\n使用说明:")
        print("1. 在Web界面的系统设置中配置余额监测参数")
        print("2. 启用所需的通知类型")
        print("3. 使用测试按钮验证通知功能")
        print("4. 系统将自动监测余额变动并发送通知")
    else:
        print(f"\n⚠️ 有 {len(results) - success_count} 项测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sqlite3
from datetime import datetime

def test_api_vs_database():
    """直接对比API和数据库的结果"""
    print("=== API vs 数据库直接对比测试 ===")
    
    # 1. 直接查询数据库
    print("\n1. 直接查询数据库（4小时）:")
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    query = """
        SELECT standard_symbol, standard_signal, created_at,
               datetime(created_at, '+8 hours') as beijing_time
        FROM alerts
        WHERE datetime(created_at, '+8 hours') >= datetime('now', '+8 hours', '-4 hours')
        ORDER BY created_at DESC
    """
    
    cursor.execute(query)
    alerts = cursor.fetchall()
    conn.close()
    
    # 统计数据库结果
    db_stats = {}
    for alert in alerts:
        symbol, signal, created_at, beijing_time = alert
        if symbol not in db_stats:
            db_stats[symbol] = {'BUY': 0, 'SELL': 0}
        if signal == 'BUY':
            db_stats[symbol]['BUY'] += 1
        elif signal == 'SELL':
            db_stats[symbol]['SELL'] += 1
    
    print(f"数据库查询结果: {len(alerts)}条记录")
    for symbol, counts in db_stats.items():
        if counts['BUY'] > 0 or counts['SELL'] > 0:
            print(f"  {symbol}: BUY={counts['BUY']}, SELL={counts['SELL']}")
    
    # 2. 调用API
    print("\n2. 调用API（4小时）:")
    try:
        response = requests.get('http://localhost:7000/api/alerts/stats?hours=4')
        if response.status_code == 200:
            data = response.json()
            total_alerts = data.get('total_alerts', 0)
            print(f"API返回结果: {total_alerts}条记录")
            
            api_stats = {}
            for symbol, stats in data['data'].items():
                if stats['buy'] > 0 or stats['sell'] > 0:
                    api_stats[symbol] = {'BUY': stats['buy'], 'SELL': stats['sell']}
                    print(f"  {symbol}: BUY={stats['buy']}, SELL={stats['sell']}")
            
            # 3. 对比结果
            print("\n3. 对比结果:")
            print(f"数据库记录数: {len(alerts)}")
            print(f"API记录数: {total_alerts}")
            
            if len(alerts) != total_alerts:
                print("❌ 记录数不一致!")
            else:
                print("✅ 记录数一致")
            
            # 对比统计
            for symbol in set(list(db_stats.keys()) + list(api_stats.keys())):
                db_buy = db_stats.get(symbol, {}).get('BUY', 0)
                db_sell = db_stats.get(symbol, {}).get('SELL', 0)
                api_buy = api_stats.get(symbol, {}).get('BUY', 0)
                api_sell = api_stats.get(symbol, {}).get('SELL', 0)
                
                if db_buy != api_buy or db_sell != api_sell:
                    print(f"❌ {symbol}: 数据库({db_buy}/{db_sell}) vs API({api_buy}/{api_sell})")
                else:
                    if db_buy > 0 or db_sell > 0:
                        print(f"✅ {symbol}: 一致({db_buy}/{db_sell})")
        else:
            print(f"API错误: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"API调用失败: {e}")

if __name__ == "__main__":
    test_api_vs_database()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .loading-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 400px;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5>正在验证登录状态...</h5>
        <p class="text-muted">请稍候</p>
    </div>

    <script>
        class AuthChecker {
            constructor() {
                this.checkAuth();
            }

            async checkAuth() {
                try {
                    // 获取存储的token
                    const token = this.getStoredToken();
                    
                    if (!token) {
                        // 没有token，跳转到登录页
                        this.redirectToLogin();
                        return;
                    }

                    // 验证token有效性
                    const isValid = await this.validateToken(token);
                    
                    if (isValid) {
                        // token有效，跳转到主页
                        this.redirectToDashboard();
                    } else {
                        // token无效，清除并跳转到登录页
                        this.clearToken();
                        this.redirectToLogin();
                    }
                } catch (error) {
                    console.error('认证检查失败:', error);
                    this.redirectToLogin();
                }
            }

            getStoredToken() {
                // 优先检查localStorage，然后检查sessionStorage
                return localStorage.getItem('session_token') || 
                       sessionStorage.getItem('session_token');
            }

            async validateToken(token) {
                try {
                    const response = await fetch('/api/auth/validate', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    return response.ok;
                } catch (error) {
                    console.error('Token验证失败:', error);
                    return false;
                }
            }

            clearToken() {
                localStorage.removeItem('session_token');
                sessionStorage.removeItem('session_token');
            }

            redirectToLogin() {
                window.location.href = '/login';
            }

            redirectToDashboard() {
                window.location.href = '/dashboard';
            }
        }

        // 页面加载完成后开始认证检查
        document.addEventListener('DOMContentLoaded', () => {
            new AuthChecker();
        });
    </script>
</body>
</html>

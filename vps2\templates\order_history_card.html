{% extends "base_template.html" %}

{% block content %}
<!-- MT5历史交易记录卡片 -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">订单历史记录</h5>
        <div class="btn-group">
            <button id="showAllOrders" class="btn btn-sm btn-outline-primary active">完整订单历史</button>
            <button id="showMT5Deals" class="btn btn-sm btn-outline-primary">MT5交易记录</button>
            <button id="showMT5Orders" class="btn btn-sm btn-outline-primary">MT5订单记录</button>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- 完整订单历史表格 -->
        <div id="allOrdersTable" class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead>
                    <tr>
                        <th scope="col">订单号</th>
                        <th scope="col">开仓时间</th>
                        <th scope="col">平仓时间</th>
                        <th scope="col">交易对</th>
                        <th scope="col">方向</th>
                        <th scope="col">交易量</th>
                        <th scope="col">开仓价</th>
                        <th scope="col">平仓价</th>
                        <th scope="col">持仓时长</th>
                        <th scope="col">止损</th>
                        <th scope="col">止盈</th>
                        <th scope="col">盈亏</th>
                        <th scope="col">隔夜利息</th>
                        <th scope="col">详情</th>
                    </tr>
                </thead>
                <tbody>
                    {% if all_history_orders %}
                        {% for order in all_history_orders %}
                            <tr>
                                <td>{{ order['ticket'] }}</td>
                                <td>{{ order['open_time_formatted'] }}</td>
                                <td>{{ order['close_time_formatted'] }}</td>
                                <td>{{ order['trading_pair'] }}</td>
                                <td>
                                    {% if order['operation'] == 'buy' %}
                                        <span class="badge bg-success">买入</span>
                                    {% else %}
                                        <span class="badge bg-danger">卖出</span>
                                    {% endif %}
                                </td>
                                <td>{{ order['volume'] }}</td>
                                <td>{{ order['price'] }}</td>
                                <td>{{ order['close_price'] }}</td>
                                <td>{{ order['duration'] }}</td>
                                <td>{{ order['sl'] if order['sl'] else '-' }}</td>
                                <td>{{ order['tp'] if order['tp'] else '-' }}</td>
                                <td class="{% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% endif %} fw-bold">
                                    {{ "%.2f"|format(order['profit']|float) }}
                                </td>
                                <td class="{% if order['swap'] > 0 %}text-success{% elif order['swap'] < 0 %}text-danger{% endif %}">
                                    {{ "%.2f"|format(order['swap']|float) if order['swap'] is not none else '-' }}
                                </td>
                                <td>
                                    <a href="{{ url_for('order_detail', order_id=order['id']) }}" class="btn btn-sm btn-outline-primary">详情</a>
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="14" class="text-center py-3">暂无历史订单数据</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- MT5历史交易记录表格 -->
        <div id="mt5DealsTable" class="table-responsive" style="display: none;">
            <!-- Existing MT5 deals table content -->
        </div>
        
        <!-- MT5历史订单记录表格 -->
        <div id="mt5OrdersTable" class="table-responsive" style="display: none;">
            <!-- Existing MT5 orders table content -->
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 切换历史记录表格
    $("#showAllOrders").click(function() {
        $(this).addClass("active");
        $("#showMT5Deals").removeClass("active");
        $("#showMT5Orders").removeClass("active");
        $("#allOrdersTable").show();
        $("#mt5DealsTable").hide();
        $("#mt5OrdersTable").hide();
    });
    
    $("#showMT5Deals").click(function() {
        $(this).addClass("active");
        $("#showAllOrders").removeClass("active");
        $("#showMT5Orders").removeClass("active");
        $("#allOrdersTable").hide();
        $("#mt5DealsTable").show();
        $("#mt5OrdersTable").hide();
    });
    
    $("#showMT5Orders").click(function() {
        $(this).addClass("active");
        $("#showAllOrders").removeClass("active");
        $("#showMT5Deals").removeClass("active");
        $("#allOrdersTable").hide();
        $("#mt5DealsTable").hide();
        $("#mt5OrdersTable").show();
    });
});
</script>
{% endblock %}

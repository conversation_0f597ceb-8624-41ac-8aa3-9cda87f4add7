# TradingView自动交易系统 API文档

## 概述

本文档描述了TradingView自动交易系统的REST API接口。所有API都基于HTTP协议，使用JSON格式进行数据交换。

## 认证

除了系统健康检查接口外，所有API都需要认证。认证方式为Bearer Token。

### 获取认证令牌

```http
POST /api/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
```

响应：
```json
{
    "success": true,
    "message": "登录成功",
    "session_token": "your-session-token",
    "user_id": 1,
    "username": "admin"
}
```

### 使用认证令牌

在请求头中添加Authorization字段：
```http
Authorization: Bearer your-session-token
```

## API接口

### 1. 认证管理

#### 1.1 用户登录
- **URL**: `/api/auth/login`
- **方法**: `POST`
- **认证**: 无需认证
- **参数**:
  ```json
  {
      "username": "string",
      "password": "string"
  }
  ```

#### 1.2 用户登出
- **URL**: `/api/auth/logout`
- **方法**: `POST`
- **认证**: 需要认证

#### 1.3 验证会话
- **URL**: `/api/auth/validate`
- **方法**: `GET`
- **认证**: 需要认证

### 2. 配置管理

#### 2.1 获取所有交易对配置
- **URL**: `/api/config/symbols`
- **方法**: `GET`
- **认证**: 需要认证
- **响应**:
  ```json
  {
      "success": true,
      "data": {
          "XAUUSD": {
              "enabled": true,
              "lot_size": 0.01,
              "stop_loss_usd": 50.0,
              "take_profit_usd": 100.0,
              "signal_timeout": 180,
              "category": "precious_metals"
          }
      }
  }
  ```

#### 2.2 获取单个交易对配置
- **URL**: `/api/config/symbols/{symbol}`
- **方法**: `GET`
- **认证**: 需要认证

#### 2.3 更新交易对配置
- **URL**: `/api/config/symbols/{symbol}`
- **方法**: `PUT`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "enabled": true,
      "lot_size": 0.01,
      "stop_loss_usd": 50.0,
      "take_profit_usd": 100.0,
      "signal_timeout": 180,
      "category": "precious_metals"
  }
  ```

#### 2.4 批量更新交易对配置
- **URL**: `/api/config/symbols/batch`
- **方法**: `POST`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "symbols": {
          "XAUUSD": {
              "enabled": true,
              "lot_size": 0.01
          },
          "ETHUSD": {
              "enabled": false
          }
      }
  }
  ```

#### 2.5 获取全局止盈止损配置
- **URL**: `/api/config/global-sl-tp`
- **方法**: `GET`
- **认证**: 需要认证

#### 2.6 更新全局止盈止损配置
- **URL**: `/api/config/global-sl-tp`
- **方法**: `PUT`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "enabled": true,
      "global_stop_loss_usd": 500.0,
      "global_take_profit_usd": 1000.0,
      "check_interval_seconds": 30
  }
  ```

#### 2.7 获取系统配置
- **URL**: `/api/config/system`
- **方法**: `GET`
- **认证**: 需要认证

#### 2.8 更新系统配置
- **URL**: `/api/config/system`
- **方法**: `PUT`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "trading_cooldown": "300",
      "max_concurrent_trades": "5",
      "bark_forward_enabled": "True"
  }
  ```

### 3. 交易监控

#### 3.1 获取交易记录
- **URL**: `/api/trades`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认50）
  - `symbol`: 交易对过滤
  - `status`: 状态过滤

#### 3.2 获取交易详情
- **URL**: `/api/trades/{trade_id}`
- **方法**: `GET`
- **认证**: 需要认证

#### 3.3 获取警报历史
- **URL**: `/api/alerts`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认50）
  - `symbol`: 交易对过滤

#### 3.4 获取警报统计
- **URL**: `/api/alerts/statistics`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `days`: 统计天数（默认7）

### 4. 订单管理

#### 4.1 获取当前持仓
- **URL**: `/api/positions`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `symbol`: 交易对过滤

#### 4.2 平仓指定订单
- **URL**: `/api/positions/{ticket}/close`
- **方法**: `POST`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "reason": "手动平仓",
      "send_notification": true
  }
  ```

#### 4.3 平仓所有订单
- **URL**: `/api/positions/close-all`
- **方法**: `POST`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "reason": "批量平仓",
      "send_notification": true
  }
  ```

### 5. 策略控制

#### 5.1 获取策略状态
- **URL**: `/api/strategy/status`
- **方法**: `GET`
- **认证**: 需要认证

#### 5.2 启动策略
- **URL**: `/api/strategy/start`
- **方法**: `POST`
- **认证**: 需要认证

#### 5.3 停止策略
- **URL**: `/api/strategy/stop`
- **方法**: `POST`
- **认证**: 需要认证

#### 5.4 紧急停止
- **URL**: `/api/strategy/emergency-stop`
- **方法**: `POST`
- **认证**: 需要认证

#### 5.5 从紧急停止恢复
- **URL**: `/api/strategy/resume`
- **方法**: `POST`
- **认证**: 需要认证

#### 5.6 获取策略性能指标
- **URL**: `/api/strategy/performance`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `days`: 统计天数（默认7）

#### 5.7 验证策略配置
- **URL**: `/api/strategy/validate`
- **方法**: `GET`
- **认证**: 需要认证

### 6. 风险管理

#### 6.1 获取风险指标
- **URL**: `/api/risk/metrics`
- **方法**: `GET`
- **认证**: 需要认证

#### 6.2 获取余额历史
- **URL**: `/api/risk/balance-history`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `days`: 历史天数（默认7）
  - `limit`: 记录数量（默认100）

### 7. 通知管理

#### 7.1 测试通知发送
- **URL**: `/api/notifications/test`
- **方法**: `POST`
- **认证**: 需要认证
- **参数**:
  ```json
  {
      "type": "系统状态",
      "data": {
          "status_type": "测试通知",
          "details": "这是一条测试通知"
      }
  }
  ```

#### 7.2 获取通知历史
- **URL**: `/api/notifications/history`
- **方法**: `GET`
- **认证**: 需要认证
- **查询参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认50）

### 8. 系统状态

#### 8.1 获取系统状态
- **URL**: `/api/system/status`
- **方法**: `GET`
- **认证**: 需要认证

#### 8.2 获取系统健康状态
- **URL**: `/api/system/health`
- **方法**: `GET`
- **认证**: 无需认证

### 9. Webhook接口

#### 9.1 接收TradingView警报
- **URL**: `/webhook/tradingview`
- **方法**: `POST`
- **认证**: 无需认证
- **参数**:
  ```json
  {
      "symbol": "XAUUSD",
      "signal_direction": "BUY",
      "timeframe": "1m",
      "price": 2000.50,
      "signal_strength": "强",
      "timestamp": "2024-01-01T12:00:00Z"
  }
  ```

## 错误处理

所有API在发生错误时都会返回统一的错误格式：

```json
{
    "success": false,
    "error": "错误描述信息"
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `404`: 资源不存在
- `500`: 服务器内部错误

## 数据格式

### 时间格式
所有时间字段都使用ISO 8601格式：`2024-01-01T12:00:00Z`

### 货币格式
所有金额字段都使用美元（USD）作为单位，保留2位小数。

### 交易方向
- `BUY`: 做多
- `SELL`: 做空

### 订单状态
- `OPEN`: 开仓
- `CLOSED`: 已平仓
- `CANCELLED`: 已取消

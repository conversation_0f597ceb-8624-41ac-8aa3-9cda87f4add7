#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from datetime import datetime, timedelta

def test_alert_stats(hours=4):
    """测试警报统计逻辑"""
    print(f"=== 测试 {hours} 小时内的警报统计 ===")
    
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    # 使用与API相同的查询
    query = f"""
        SELECT standard_symbol, original_symbol, standard_signal, original_signal,
               timestamp, created_at,
               datetime(created_at, '+8 hours') as beijing_time
        FROM alerts
        WHERE datetime(created_at, '+8 hours') >= datetime('now', '+8 hours', '-{hours} hours')
        ORDER BY created_at DESC
    """
    
    cursor.execute(query)
    alerts = cursor.fetchall()
    conn.close()
    
    print(f"查询到 {len(alerts)} 条记录")
    
    # 统计数据
    stats = {}
    symbols = ['BTCUSD', 'ETHUSD', 'SOLUSD', 'XAUUSD', 'GBPJPY']
    
    # 初始化
    for s in symbols:
        stats[s] = {'buy': 0, 'sell': 0, 'latest': None}
    
    # 统计逻辑
    ethusd_records = []
    if alerts:
        for i, alert in enumerate(alerts):
            try:
                std_symbol, orig_symbol, std_signal, orig_signal, timestamp, created_at, beijing_time = alert
                
                symbol = std_symbol or orig_symbol
                signal = std_signal or orig_signal
                
                if symbol == 'ETHUSD' and signal == 'BUY':
                    ethusd_records.append(beijing_time)
                
                if not symbol or not signal:
                    continue
                
                symbol = symbol.upper()
                signal = signal.upper()
                
                if symbol not in stats:
                    stats[symbol] = {'buy': 0, 'sell': 0, 'latest': None}
                
                if signal in ['BUY', 'LONG']:
                    stats[symbol]['buy'] += 1
                elif signal in ['SELL', 'SHORT']:
                    stats[symbol]['sell'] += 1
                
                alert_time = beijing_time or created_at or timestamp
                if alert_time and (not stats[symbol]['latest'] or alert_time > stats[symbol]['latest']):
                    stats[symbol]['latest'] = alert_time
                    
            except Exception as e:
                print(f"处理第{i+1}条记录时出错: {e}")
                print(f"记录内容: {alert}")
    
    print(f"\nETHUSD BUY记录 ({len(ethusd_records)}条):")
    for i, time in enumerate(ethusd_records[:10]):
        print(f"  {i+1}. {time}")
    
    print(f"\n最终统计结果:")
    for symbol, data in stats.items():
        if data['buy'] > 0 or data['sell'] > 0:
            print(f"  {symbol}: BUY={data['buy']}, SELL={data['sell']}")
    
    return stats

if __name__ == "__main__":
    test_alert_stats(4)

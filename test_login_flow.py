#!/usr/bin/env python3
"""
测试登录流程
"""
import requests
import time

def test_login_flow():
    """测试完整的登录流程"""
    base_url = "http://127.0.0.1:7000"
    
    print("🔍 测试登录流程...")
    
    # 1. 测试登录页面访问
    print("\n1️⃣ 测试登录页面...")
    try:
        response = requests.get(f"{base_url}/login", timeout=5)
        if response.status_code == 200:
            print("✅ 登录页面可访问")
        else:
            print(f"❌ 登录页面状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录页面访问失败: {e}")
        return False
    
    # 2. 测试登录API
    print("\n2️⃣ 测试登录API...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'session_token' in data:
                print("✅ 登录API成功")
                token = data['session_token']
                print(f"📝 获得Token: {token[:20]}...")
            else:
                print("❌ 登录API响应格式错误")
                return False
        else:
            print(f"❌ 登录API失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
        return False
    
    # 3. 测试Token验证API
    print("\n3️⃣ 测试Token验证...")
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        response = requests.get(
            f"{base_url}/api/auth/validate",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Token验证成功")
        else:
            print(f"❌ Token验证失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Token验证测试失败: {e}")
        return False
    
    # 4. 测试受保护的API
    print("\n4️⃣ 测试受保护的API...")
    try:
        headers = {'Authorization': f'Bearer {token}'}
        
        response = requests.get(
            f"{base_url}/api/system/health",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 受保护API访问成功")
            print(f"📊 系统健康状态: {data.get('data', {}).get('status', 'unknown')}")
        else:
            print(f"❌ 受保护API访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 受保护API测试失败: {e}")
        return False
    
    # 5. 测试仪表板页面
    print("\n5️⃣ 测试仪表板页面...")
    try:
        response = requests.get(f"{base_url}/dashboard", timeout=5)
        if response.status_code == 200:
            print("✅ 仪表板页面可访问")
            if 'TradingView自动交易系统' in response.text:
                print("✅ 仪表板页面内容正确")
            else:
                print("⚠️ 仪表板页面内容可能不完整")
        else:
            print(f"❌ 仪表板页面状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 仪表板页面访问失败: {e}")
        return False
    
    # 6. 测试主页重定向
    print("\n6️⃣ 测试主页认证检查...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 主页可访问")
            if '正在验证登录状态' in response.text:
                print("✅ 主页显示认证检查页面")
            else:
                print("⚠️ 主页内容可能不是认证检查页面")
        else:
            print(f"❌ 主页状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
        return False
    
    print("\n" + "="*60)
    print("🎉 登录流程测试完成！")
    print("\n💡 测试结果说明:")
    print("✅ 登录API正常工作")
    print("✅ Token验证正常工作") 
    print("✅ 受保护API正常工作")
    print("✅ 页面路由正常工作")
    print("\n🔧 如果Web界面仍有问题，可能是前端JavaScript的问题")
    print("📝 建议检查浏览器控制台的错误信息")
    
    return True

if __name__ == '__main__':
    test_login_flow()

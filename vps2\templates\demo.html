<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>MT5交易系统 - 响应式设计演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 0.75rem;
            --border-radius-sm: 0.5rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--dark-color) !important;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand::before {
            content: "📊";
            font-size: 1.25rem;
        }

        .main-content {
            background: var(--light-color);
            min-height: calc(100vh - 80px);
            border-radius: 2rem 2rem 0 0;
            margin-top: 1rem;
            padding: 2rem 1rem;
            position: relative;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .demo-section {
            margin-bottom: 3rem;
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            padding: 0.625rem 1.25rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0.75rem;
                margin-top: 0.5rem;
                border-radius: 1rem 1rem 0 0;
            }

            .demo-title {
                font-size: 1.25rem;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 0.75rem 0.5rem;
            }
        }

        .device-preview {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            background: white;
            margin-bottom: 1rem;
        }

        .mobile-preview {
            max-width: 375px;
            margin: 0 auto;
        }

        .tablet-preview {
            max-width: 768px;
            margin: 0 auto;
        }

        .desktop-preview {
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">MT5交易系统</a>
            <span class="badge bg-info">响应式设计演示</span>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content" style="margin-top: 80px;">
        <div class="container-fluid">
            <!-- 页面标题 -->
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-primary mb-3">🎨 响应式设计演示</h1>
                <p class="lead text-muted">展示MT5交易系统在不同设备上的优化效果</p>
            </div>

            <!-- 设计特性展示 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="bi bi-palette"></i>
                    设计特性
                </h2>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-phone fs-1 text-primary mb-3"></i>
                                <h5>移动端优化</h5>
                                <p class="text-muted">触控友好的界面设计，适配各种屏幕尺寸</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-palette fs-1 text-success mb-3"></i>
                                <h5>现代化UI</h5>
                                <p class="text-muted">使用最新的设计趋势和视觉效果</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-lightning fs-1 text-warning mb-3"></i>
                                <h5>性能优化</h5>
                                <p class="text-muted">快速加载，流畅动画，优秀的用户体验</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 响应式演示 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="bi bi-display"></i>
                    响应式布局演示
                </h2>
                
                <!-- 移动端预览 -->
                <div class="mb-4">
                    <h4 class="mb-3">📱 移动端视图 (375px)</h4>
                    <div class="device-preview mobile-preview">
                        <div class="card">
                            <div class="card-header">
                                <i class="bi bi-speedometer2 me-2"></i>
                                控制面板
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold">$10,000</div>
                                            <small class="text-muted">账户余额</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold text-success">+$250</div>
                                            <small class="text-muted">浮动盈亏</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 平板端预览 -->
                <div class="mb-4">
                    <h4 class="mb-3">📱 平板端视图 (768px)</h4>
                    <div class="device-preview tablet-preview">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between">
                                <span><i class="bi bi-speedometer2 me-2"></i>控制面板</span>
                                <button class="btn btn-primary btn-sm">刷新</button>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold">$10,000</div>
                                            <small class="text-muted">账户余额</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold text-success">+$250</div>
                                            <small class="text-muted">浮动盈亏</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold">5</div>
                                            <small class="text-muted">活跃订单</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <div class="fw-bold">$9,750</div>
                                            <small class="text-muted">可用保证金</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交互演示 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="bi bi-hand-index"></i>
                    交互演示
                </h2>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5>按钮效果</h5>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-primary">主要按钮</button>
                                    <button class="btn btn-outline-secondary">次要按钮</button>
                                    <button class="btn btn-success">成功</button>
                                    <button class="btn btn-danger">危险</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5>状态徽章</h5>
                                <div class="d-flex gap-2 flex-wrap">
                                    <span class="badge bg-success"><i class="bi bi-check-circle"></i> 已连接</span>
                                    <span class="badge bg-danger"><i class="bi bi-x-circle"></i> 未连接</span>
                                    <span class="badge bg-warning"><i class="bi bi-clock"></i> 处理中</span>
                                    <span class="badge bg-info"><i class="bi bi-info-circle"></i> 信息</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="demo-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0"><i class="bi bi-info-circle me-2"></i>优化说明</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>移动端优化</h5>
                                <ul>
                                    <li>触控友好的按钮尺寸</li>
                                    <li>响应式表格设计</li>
                                    <li>滑动手势支持</li>
                                    <li>移动端专用布局</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>性能优化</h5>
                                <ul>
                                    <li>CSS动画硬件加速</li>
                                    <li>图片懒加载</li>
                                    <li>代码分割和压缩</li>
                                    <li>缓存策略优化</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有卡片添加悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>

@echo off
REM VPS2交易系统依赖安装脚本
REM 安装登录安全功能所需的依赖包

echo =======================================
echo     VPS2 依赖包安装器
echo =======================================
echo.

REM 获取脚本所在目录
cd /d "%~dp0"

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已正确安装并添加到PATH
    echo.
    pause
    exit /b 1
)

echo Python版本信息:
python --version
echo.

echo 开始安装依赖包...
echo.

REM 升级pip
echo 1. 升级pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo 警告: pip升级失败，继续安装其他包
)
echo.

REM 安装Pillow用于验证码生成
echo 2. 安装Pillow (用于验证码图片生成)...
python -m pip install Pillow
if errorlevel 1 (
    echo 错误: Pillow安装失败
    echo.
) else (
    echo ✓ Pillow安装成功
)
echo.

REM 安装Flask相关包（如果需要）
echo 3. 检查Flask相关包...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 安装Flask...
    python -m pip install Flask
    if errorlevel 1 (
        echo 错误: Flask安装失败
    ) else (
        echo ✓ Flask安装成功
    )
) else (
    echo ✓ Flask已安装
)
echo.

REM 安装werkzeug（如果需要）
python -c "import werkzeug" >nul 2>&1
if errorlevel 1 (
    echo 安装Werkzeug...
    python -m pip install Werkzeug
    if errorlevel 1 (
        echo 错误: Werkzeug安装失败
    ) else (
        echo ✓ Werkzeug安装成功
    )
) else (
    echo ✓ Werkzeug已安装
)
echo.

REM 安装MetaTrader5（如果需要）
python -c "import MetaTrader5" >nul 2>&1
if errorlevel 1 (
    echo 安装MetaTrader5...
    python -m pip install MetaTrader5
    if errorlevel 1 (
        echo 错误: MetaTrader5安装失败
    ) else (
        echo ✓ MetaTrader5安装成功
    )
) else (
    echo ✓ MetaTrader5已安装
)
echo.

echo 验证安装结果...
echo.

REM 验证关键包
python -c "import PIL; print('✓ PIL/Pillow:', PIL.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PIL/Pillow验证失败
) 

python -c "import flask; print('✓ Flask:', flask.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ Flask验证失败
)

python -c "import werkzeug; print('✓ Werkzeug:', werkzeug.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ Werkzeug验证失败
)

python -c "import MetaTrader5; print('✓ MetaTrader5: 已安装')" 2>nul
if errorlevel 1 (
    echo ❌ MetaTrader5验证失败
)

echo.
echo =======================================
echo 依赖包安装完成！
echo.
echo 现在可以运行 start_vps2.BAT 启动系统
echo =======================================
echo.
pause

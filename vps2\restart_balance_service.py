#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重启余额监测服务
"""

import balance_monitor
import time

def restart_service():
    """重启余额监测服务"""
    print('重新启动余额监测服务...')
    
    try:
        # 先停止服务
        balance_monitor.stop_balance_monitoring()
        time.sleep(1)
        
        # 再启动服务
        balance_monitor.start_balance_monitoring()
        time.sleep(2)
        
        # 检查状态
        status = balance_monitor.get_balance_monitoring_status()
        running = status.get("running", False)
        
        print(f'服务状态: {"运行中" if running else "未运行"}')
        
        if running:
            print('✅ 服务启动成功')
            
            # 显示配置
            periodic_config = status.get("periodic_balance_notification", {})
            custom_times = periodic_config.get("custom_times", [])
            print(f'配置的时间点: {custom_times}')
            
            # 发送测试通知
            print('发送测试通知...')
            balance_monitor.balance_monitor.send_periodic_balance_notification()
            print('✅ 测试通知已发送，请检查您的Bark设备')
            
            return True
        else:
            print('❌ 服务启动失败')
            return False
            
    except Exception as e:
        print(f'❌ 重启失败: {e}')
        return False

if __name__ == "__main__":
    restart_service()

# TradingView自动交易系统环境配置文件示例
# 复制此文件为 .env 并填入实际配置值

# ==================== 应用配置 ====================
# Flask应用密钥（生产环境请使用强密钥）
SECRET_KEY=your-secret-key-here-change-in-production

# Flask运行配置
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False

# 环境类型 (development/production/testing)
FLASK_ENV=production

# 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# ==================== MetaTrader5配置 ====================
# MT5账户信息
MT5_LOGIN=your_mt5_account_number
MT5_PASSWORD=your_mt5_password
MT5_SERVER=your_mt5_server

# MT5终端路径（可选，如果MT5不在默认位置）
MT5_PATH=C:\Program Files\MetaTrader 5\terminal64.exe

# ==================== Bark通知配置 ====================
# Bark设备密钥（从Bark应用获取）
BARK_DEVICE_1_KEY=your_bark_device_1_key
BARK_DEVICE_2_KEY=your_bark_device_2_key

# Bark服务器地址（通常不需要修改）
BARK_BASE_URL=https://api.day.app

# ==================== 交易配置 ====================
# 默认交易冷却时间（秒）
DEFAULT_TRADING_COOLDOWN=300

# 最大并发交易数
MAX_CONCURRENT_TRADES=5

# 日最大亏损限制（美元）
MAX_DAILY_LOSS_USD=500.0

# 日最大盈利限制（美元）
MAX_DAILY_PROFIT_USD=1000.0

# ==================== 数据库配置 ====================
# 数据库文件路径（相对于项目根目录）
DATABASE_PATH=trading_system.db

# ==================== 安全配置 ====================
# 会话超时时间（小时）
SESSION_TIMEOUT_HOURS=24

# 最大登录尝试次数
MAX_LOGIN_ATTEMPTS=5

# 账户锁定时间（分钟）
LOCKOUT_DURATION_MINUTES=30

# ==================== 高级配置 ====================
# 是否启用Bark通知转发
BARK_FORWARD_ENABLED=True

# 全局止损金额（美元）
GLOBAL_STOP_LOSS_USD=500.0

# 全局止盈金额（美元）
GLOBAL_TAKE_PROFIT_USD=1000.0

# 风险检查间隔（秒）
RISK_CHECK_INTERVAL=30

# 余额记录间隔（分钟）
BALANCE_RECORD_INTERVAL=5

# ==================== 开发配置 ====================
# 是否启用调试模式（仅开发环境）
DEBUG_MODE=False

# 是否启用详细日志
VERBOSE_LOGGING=False

# 测试模式（使用内存数据库）
TESTING_MODE=False

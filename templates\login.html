<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin-top: 10px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
            padding: 12px 15px;
            font-size: 16px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            margin-top: 20px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>🚀 交易系统登录</h2>
            <p>TradingView自动交易管理系统</p>
        </div>

        <div id="alertContainer"></div>

        <form id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input type="text" class="form-control" id="username" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input type="password" class="form-control" id="password" required>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    记住登录状态
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-login">
                <span id="loginSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                <span id="loginText">登录</span>
            </button>
        </form>

        <div class="text-center mt-3">
            <small class="text-muted">
                © 2024 TradingView自动交易系统
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.alertContainer = document.getElementById('alertContainer');
                this.init();
            }

            init() {
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                this.checkExistingSession();
            }

            async checkExistingSession() {
                const sessionToken = localStorage.getItem('session_token') ||
                                   sessionStorage.getItem('session_token');

                if (sessionToken) {
                    try {
                        const response = await fetch('/api/auth/validate', {
                            headers: {
                                'Authorization': `Bearer ${sessionToken}`
                            }
                        });

                        if (response.ok) {
                            window.location.href = '/';
                        }
                    } catch (error) {
                        localStorage.removeItem('session_token');
                        sessionStorage.removeItem('session_token');
                    }
                }
            }

            async handleLogin(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;

                this.setLoading(true);
                this.clearAlert();

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        const storage = rememberMe ? localStorage : sessionStorage;
                        storage.setItem('session_token', result.session_token);

                        this.showAlert('success', '登录成功，正在跳转...');

                        setTimeout(() => {
                            window.location.href = '/dashboard';
                        }, 500);

                    } else {
                        this.showAlert('danger', result.message);
                    }

                } catch (error) {
                    this.showAlert('danger', '登录失败，请检查网络连接');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                const spinner = document.getElementById('loginSpinner');
                const text = document.getElementById('loginText');
                const submitBtn = this.form.querySelector('button[type="submit"]');

                if (loading) {
                    spinner.classList.remove('d-none');
                    text.textContent = '登录中...';
                    submitBtn.disabled = true;
                } else {
                    spinner.classList.add('d-none');
                    text.textContent = '登录';
                    submitBtn.disabled = false;
                }
            }

            showAlert(type, message) {
                this.alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
            }

            clearAlert() {
                this.alertContainer.innerHTML = '';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // 检查是否已经登录
            const token = localStorage.getItem('session_token') || sessionStorage.getItem('session_token');
            if (token) {
                // 验证token是否有效
                fetch('/api/auth/validate', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    if (response.ok) {
                        // token有效，直接跳转到仪表板
                        window.location.href = '/dashboard';
                        return;
                    } else {
                        // token无效，清除并继续显示登录页面
                        localStorage.removeItem('session_token');
                        sessionStorage.removeItem('session_token');
                    }
                    // 初始化登录管理器
                    new LoginManager();
                }).catch(error => {
                    console.error('Token验证失败:', error);
                    // 清除可能无效的token
                    localStorage.removeItem('session_token');
                    sessionStorage.removeItem('session_token');
                    // 初始化登录管理器
                    new LoginManager();
                });
            } else {
                // 没有token，初始化登录管理器
                new LoginManager();
            }
        });
    </script>
</body>
</html>

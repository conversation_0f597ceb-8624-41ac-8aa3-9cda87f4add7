"""
Bark通知系统模块
"""
import requests
import json
import threading
from datetime import datetime
from database.manager import db_manager
from trading.config_manager import config_manager
from utils.logger import logger
from utils.helpers import format_currency, format_datetime, calculate_duration

class BarkNotificationManager:
    """Bark通知管理器"""
    
    def __init__(self):
        self.base_url = "https://api.day.app"
        self.notification_templates = self._get_notification_templates()
        self.default_settings = {
            "level": "active",
            "volume": 5,
            "group": "trading",
            "sound": "default"
        }
    
    def _get_notification_templates(self):
        """获取通知模板"""
        return {
            "开仓通知": {
                "title": "🔥 新订单开仓",
                "body": "交易对: {symbol}\n方向: {direction}\n手数: {lot_size}\n价格: {price}\n止损: {sl_usd} USD\n止盈: {tp_usd} USD\n时间: {timestamp}",
                "level": "active",
                "sound": "bell"
            },
            "平仓通知": {
                "title": "✅ 订单平仓",
                "body": "交易对: {symbol}\n方向: {direction}\n平仓原因: {reason}\n开仓价: {open_price}\n平仓价: {close_price}\n盈亏: {profit_usd} USD\n持仓时间: {duration}\n账户余额变化: {balance_change} USD\n当前余额: {current_balance} USD\n时间: {timestamp}",
                "level": "active",
                "sound": "success"
            },
            "定时余额报告": {
                "title": "💰 账户余额报告",
                "body": "账户余额: {balance} USD\n净值: {equity} USD\n已用保证金: {margin} USD\n可用保证金: {free_margin} USD\n当前持仓: {open_positions}个\n今日盈亏: {daily_pnl} USD\n报告时间: {timestamp}",
                "level": "passive",
                "sound": "default"
            },
            "全局止盈止损触发": {
                "title": "⚠️ 全局止盈止损触发",
                "body": "触发类型: {trigger_type}\n总盈亏: {total_pnl} USD\n触发阈值: {threshold} USD\n影响订单: {affected_orders}个\n执行时间: {timestamp}",
                "level": "critical",
                "sound": "alarm",
                "volume": 10
            },
            "组合盈亏警告": {
                "title": "📊 组合盈亏提醒",
                "body": "当前总盈亏: {total_pnl} USD\n距离止损: {distance_to_sl} USD\n距离止盈: {distance_to_tp} USD\n持仓订单: {position_count}个\n时间: {timestamp}",
                "level": "timeSensitive",
                "sound": "warning"
            },
            "TradingView警报转发": {
                "title": "📡 TradingView警报",
                "body": "交易对: {symbol}\n信号: {signal}\n时间框架: {timeframe}\n价格: {price}\n信号强度: {strength}\n时间: {timestamp}\n原始数据: {raw_data}",
                "level": "active",
                "sound": "default"
            },
            "系统状态": {
                "title": "🔧 系统状态通知",
                "body": "状态类型: {status_type}\n详细信息: {details}\n时间: {timestamp}",
                "level": "active",
                "sound": "default"
            }
        }
    
    def get_device_keys(self):
        """获取设备密钥"""
        try:
            # 优先从新的配置文件读取
            import json
            import os

            config_file = 'config/bark_config.json'
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        bark_config = json.load(f)

                    if bark_config.get('enabled', False):
                        device_keys = bark_config.get('device_keys', [])
                        if device_keys:
                            logger.debug(f"从配置文件获取到设备密钥: {len(device_keys)}个")
                            return device_keys
                except Exception as e:
                    logger.warning(f"读取Bark配置文件失败: {e}")

            # 回退到系统配置
            device1_key = config_manager.get_system_config("bark_device_1_key")
            device2_key = config_manager.get_system_config("bark_device_2_key")

            keys = []
            if device1_key:
                keys.append(device1_key)
            if device2_key:
                keys.append(device2_key)

            return keys
        except Exception as e:
            logger.error(f"获取设备密钥失败: {e}")
            return []
    
    def send_notification(self, notification_type, data, custom_settings=None):
        """
        发送通知
        
        Args:
            notification_type: 通知类型
            data: 通知数据
            custom_settings: 自定义设置
            
        Returns:
            发送结果
        """
        try:
            # 检查是否启用Bark转发
            bark_enabled = config_manager.get_system_config("bark_forward_enabled")
            if bark_enabled != "True":
                return {"success": False, "reason": "Bark通知已禁用"}

            # 检查通知类型是否启用
            if not self._is_notification_type_enabled(notification_type):
                return {"success": False, "reason": f"通知类型 {notification_type} 已禁用"}

            # 获取设备密钥
            device_keys = self.get_device_keys()
            if not device_keys:
                return {"success": False, "reason": "未配置Bark设备密钥"}
            
            # 获取通知模板
            template = self.notification_templates.get(notification_type)
            if not template:
                return {"success": False, "reason": f"未找到通知模板: {notification_type}"}
            
            # 格式化通知内容
            formatted_notification = self._format_notification(template, data)
            
            # 合并设置
            settings = self.default_settings.copy()
            settings.update(template)
            if custom_settings:
                settings.update(custom_settings)
            
            # 发送到所有设备
            results = []
            for device_key in device_keys:
                result = self._send_to_device(device_key, formatted_notification, settings)
                results.append(result)
            
            # 记录通知
            self._save_notification_record(notification_type, formatted_notification, results)
            
            # 判断整体成功状态
            success_count = sum(1 for r in results if r["success"])
            total_count = len(results)
            
            return {
                "success": success_count > 0,
                "total_devices": total_count,
                "success_devices": success_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _format_notification(self, template, data):
        """格式化通知内容"""
        try:
            # 准备格式化数据
            format_data = data.copy()
            
            # 格式化特殊字段
            if "timestamp" not in format_data:
                format_data["timestamp"] = format_datetime(datetime.now())
            
            if "profit_usd" in format_data:
                format_data["profit_usd"] = format_currency(format_data["profit_usd"])
            
            if "balance" in format_data:
                format_data["balance"] = format_currency(format_data["balance"])
            
            if "equity" in format_data:
                format_data["equity"] = format_currency(format_data["equity"])
            
            # 格式化标题和内容
            title = template["title"].format(**format_data)
            body = template["body"].format(**format_data)
            
            return {
                "title": title,
                "body": body
            }
            
        except Exception as e:
            logger.error(f"格式化通知内容失败: {e}")
            return {
                "title": template["title"],
                "body": str(data)
            }
    
    def _send_to_device(self, device_key, notification, settings):
        """发送到单个设备"""
        try:
            url = f"{self.base_url}/{device_key}"
            
            payload = {
                "title": notification["title"],
                "body": notification["body"],
                "level": settings.get("level", "active"),
                "volume": settings.get("volume", 5),
                "group": settings.get("group", "trading"),
                "sound": settings.get("sound", "default")
            }
            
            # 添加可选参数
            if settings.get("badge"):
                payload["badge"] = settings["badge"]
            if settings.get("url"):
                payload["url"] = settings["url"]
            if settings.get("copy"):
                payload["copy"] = settings["copy"]
            if settings.get("autoCopy"):
                payload["autoCopy"] = settings["autoCopy"]
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"Bark通知发送成功: {device_key}")
                return {
                    "success": True,
                    "device_key": device_key,
                    "response": response.json()
                }
            else:
                logger.error(f"Bark通知发送失败: {device_key}, 状态码: {response.status_code}")
                return {
                    "success": False,
                    "device_key": device_key,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"发送到设备失败: {device_key}, 错误: {e}")
            return {
                "success": False,
                "device_key": device_key,
                "error": str(e)
            }
    
    def _save_notification_record(self, notification_type, notification, results):
        """保存通知记录"""
        try:
            device1_sent = False
            device2_sent = False
            
            if len(results) > 0:
                device1_sent = results[0]["success"]
            if len(results) > 1:
                device2_sent = results[1]["success"]
            
            db_manager.execute_query(
                """INSERT INTO notifications 
                   (notification_type, title, body, bark_device_1_sent, bark_device_2_sent, sent_at)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (
                    notification_type,
                    notification["title"],
                    notification["body"],
                    device1_sent,
                    device2_sent,
                    datetime.now()
                )
            )
            
        except Exception as e:
            logger.error(f"保存通知记录失败: {e}")
    
    def send_trade_open_notification(self, order_info, alert_data):
        """发送开仓通知"""
        data = {
            "symbol": order_info["symbol"],
            "direction": "做多" if order_info["direction"] == "BUY" else "做空",
            "lot_size": order_info["volume"],
            "price": order_info["open_price"],
            "sl_usd": order_info["sl_usd"],
            "tp_usd": order_info["tp_usd"],
            "timestamp": format_datetime(order_info["open_time"])
        }
        
        return self.send_notification("开仓通知", data)
    
    def send_trade_close_notification(self, close_info, account_info=None):
        """发送平仓通知"""
        data = {
            "symbol": close_info["symbol"],
            "direction": "做多" if close_info["direction"] == "BUY" else "做空",
            "reason": close_info["reason"],
            "open_price": close_info["open_price"],
            "close_price": close_info["close_price"],
            "profit_usd": close_info["profit"],
            "duration": calculate_duration(close_info.get("open_time"), close_info["close_time"]),
            "balance_change": close_info["profit"],
            "current_balance": account_info["balance"] if account_info else "N/A",
            "timestamp": format_datetime(close_info["close_time"])
        }
        
        return self.send_notification("平仓通知", data)
    
    def send_balance_report(self, account_info, positions_count, daily_pnl):
        """发送余额报告"""
        data = {
            "balance": account_info["balance"],
            "equity": account_info["equity"],
            "margin": account_info["margin"],
            "free_margin": account_info["free_margin"],
            "open_positions": positions_count,
            "daily_pnl": daily_pnl,
            "timestamp": format_datetime(datetime.now())
        }
        
        return self.send_notification("定时余额报告", data)
    
    def send_alert_forward(self, alert_data):
        """转发TradingView警报原文"""
        try:
            # 提取警报信息，支持多种字段名
            symbol = (alert_data.get("symbol") or
                     alert_data.get("交易对") or
                     alert_data.get("original_symbol") or
                     "未知交易对")

            signal = (alert_data.get("signal") or
                     alert_data.get("信号") or
                     alert_data.get("original_signal") or
                     "未知信号")

            timeframe = (alert_data.get("timeframe") or
                        alert_data.get("周期") or
                        alert_data.get("interval") or
                        "")

            price = (alert_data.get("price") or
                    alert_data.get("价格") or
                    "")

            strength = (alert_data.get("signal_strength") or
                       alert_data.get("信号强度") or
                       alert_data.get("strength") or
                       "")

            # 构建转发数据
            data = {
                "symbol": symbol,
                "signal": signal,
                "timeframe": timeframe,
                "price": price,
                "strength": strength,
                "timestamp": format_datetime(datetime.now()),
                "raw_data": str(alert_data)  # 包含原始数据
            }

            logger.info(f"转发警报: {symbol} {signal}")
            return self.send_notification("TradingView警报转发", data)

        except Exception as e:
            logger.error(f"转发警报失败: {e}")
            return False

    def _is_notification_type_enabled(self, notification_type):
        """检查通知类型是否启用"""
        try:
            import json
            import os

            # 从Bark配置文件读取通知类型设置
            config_file = 'config/bark_config.json'
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        bark_config = json.load(f)

                    # 根据通知类型检查对应的设置
                    type_mapping = {
                        "开仓通知": bark_config.get("trade_open", True),
                        "平仓通知": bark_config.get("trade_close", True),
                        "TradingView警报转发": bark_config.get("alert_forward", False),
                        "系统状态通知": bark_config.get("system_status", True),
                        "定时余额报告": bark_config.get("balance_report", False)
                    }

                    return type_mapping.get(notification_type, True)

                except Exception as e:
                    logger.warning(f"读取通知类型配置失败: {e}")
                    return True

            # 如果配置文件不存在，使用默认设置
            default_settings = {
                "开仓通知": True,
                "平仓通知": True,
                "TradingView警报转发": False,
                "系统状态通知": True,
                "定时余额报告": False
            }

            return default_settings.get(notification_type, True)

        except Exception as e:
            logger.error(f"检查通知类型启用状态失败: {e}")
            return True  # 出错时默认启用

# 创建全局Bark通知管理器实例
bark_manager = BarkNotificationManager()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5交易模块 - 执行交易信号
"""

import MetaTrader5 as mt5
import time
import logging
import os
import json
import sys
import sqlite3
from datetime import datetime, timezone
import threading
from utils.filling_modes import try_order_with_filling_modes
import bark_notifier
from utils.beijing_time import get_beijing_time
# 导入历史数据获取模块
from mt5_history_functions import get_history_deals, get_history_orders, get_closed_positions, get_deal_type_string, get_deal_entry_string, get_deal_reason_string, get_order_state_string, get_order_reason_string, get_order_type_string

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    filename='logs/mt5_trader.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

# 数据库操作的线程锁
DB_LOCK = threading.Lock()

# 全局配置
config = {}
symbol_map = {}
mt5_initialized = False

# 交易品种小数位配置
SYMBOL_DIGITS = {
    "XAUUSD": 2,   # 黄金 - 2位小数
    "BTCUSD": 1,   # 比特币 - 1位小数
    "ETHUSD": 1,   # 以太坊 - 1位小数
    "GBPJPY": 3,   # 英镑/日元 - 3位小数
    "BRENT": 2,    # 布伦特原油 - 2位小数
    "XTIUSD": 2,   # WTI原油 - 2位小数
    "GBPUSD": 5,   # 英镑/美元 - 5位小数
    "DXY": 3,      # 美元指数 - 3位小数
    # 可以添加更多交易品种的小数位配置
}

# 如果MT5模块是直接作为库导入的，记录一条信息
logger.info("MT5交易模块被导入")

def load_config():
    """加载配置文件"""
    global config, symbol_map
    try:
        # 强制从文件中重新加载配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取交易对映射
        symbol_map = config.get('symbol_map', {})
        
        # 配置日志级别
        log_level = config.get('log_level', 'DEBUG')
        numeric_level = getattr(logging, log_level.upper(), None)
        if isinstance(numeric_level, int):
            logging.getLogger().setLevel(numeric_level)
        
        logger.info("配置加载成功")
        logger.info(f"当前止损点数: {config.get('default_sl_points')}, 止盈点数: {config.get('default_tp_points')}")
        logger.info(f"当前默认交易量: {config.get('default_volume')}")
        return True
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}", exc_info=True)
        return False

def init_mt5():
    """初始化MT5连接"""
    global mt5_initialized
    
    # 即使已初始化，也再次尝试连接以确保连接有效
    # 如果已初始化，只是记录一下，但还是会继续检查连接
    if mt5_initialized:
        logger.info("MT5已经初始化，再次验证连接")
    
    try:
        # 记录更多环境信息以帮助调试
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"当前工作目录: {os.getcwd()}")
        logger.info(f"MetaTrader5模块路径: {mt5.__file__}")
        
        # 关闭之前的连接（如果有）
        mt5.shutdown()
        logger.info("已关闭先前的MT5连接")
        
        # 检查MT5路径
        mt5_path = config.get('mt5_path', '')
        logger.info(f"配置的MT5路径: {mt5_path}")
        
        if os.path.exists(mt5_path):
            logger.info(f"MT5路径存在: {mt5_path}")
        else:
            logger.warning(f"MT5路径不存在: {mt5_path}，尝试默认初始化")
        
        # 初始化MT5连接 - 先尝试使用配置文件中的路径
        logger.info("尝试使用配置路径初始化MT5...")
        init_result = mt5.initialize(path=mt5_path)
        if not init_result:
            error = mt5.last_error()
            logger.warning(f"使用配置路径初始化MT5失败: {error}，尝试使用默认路径")
            
            # 尝试不带路径初始化
            init_result = mt5.initialize()
            if not init_result:
                error = mt5.last_error()
                logger.error(f"使用默认路径初始化MT5也失败: {error}")
                
                # 最后尝试搜索常见路径
                common_paths = [
                    "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
                    "C:\\Program Files (x86)\\MetaTrader 5\\terminal.exe"
                ]
                
                for path in common_paths:
                    if os.path.exists(path):
                        logger.info(f"尝试使用找到的路径初始化: {path}")
                        init_result = mt5.initialize(path=path)
                        if init_result:
                            logger.info(f"成功使用路径初始化: {path}")
                            break
                        else:
                            error = mt5.last_error()
                            logger.warning(f"使用路径 {path} 初始化失败: {error}")
                
                if not init_result:
                    logger.error("所有MT5初始化方法均失败，无法继续")
                    return False
        
        logger.info("MT5初始化成功，准备登录账户")
        
        # 检查是否找到MetaTrader5终端
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error("无法获取MT5终端信息，可能未找到MetaTrader5应用程序")
            return False
        else:
            logger.info(f"成功连接到MT5终端: {terminal_info.name}")
            # 安全地访问属性，有些版本的MT5 API可能没有这些属性
            try:
                logger.info(f"终端路径: {terminal_info.path}")
            except AttributeError:
                logger.warning("无法获取MT5终端路径")
                
            try:
                logger.info(f"数据路径: {terminal_info.data_path}")
            except AttributeError:
                logger.warning("无法获取MT5数据路径")
                
            try:
                logger.info(f"是否允许交易: {terminal_info.trade_allowed}")
            except AttributeError:
                logger.warning("无法获取MT5交易允许状态")
        
        # 登录MT5账户
        try:
            login_str = config.get('mt5_login', '0')
            login = int(login_str)  # 确保转换为整数
            logger.info(f"解析登录ID: {login_str} -> {login}")
        except ValueError:
            logger.error(f"配置文件中的MT5登录ID必须是数字，当前值: {config.get('mt5_login', '未设置')}")
            mt5.shutdown()
            return False
            
        password = config.get('mt5_password', '')
        server = config.get('mt5_server', '')
        
        logger.info(f"尝试登录MT5: 账号={login}, 服务器={server}")
        
        if not mt5.login(login=login, password=password, server=server):
            error = mt5.last_error()
            logger.error(f"MT5登录失败: {error}")
            
            # 尝试不指定服务器登录（使用默认服务器）
            logger.info("尝试不指定服务器登录...")
            if not mt5.login(login=login, password=password):
                error = mt5.last_error()
                logger.error(f"不指定服务器登录也失败: {error}")
                mt5.shutdown()
                return False
            else:
                logger.info("不指定服务器登录成功")
        else:
            logger.info("MT5登录成功")
            
        # 检查账户信息确认登录成功
        account_info = mt5.account_info()
        if account_info:
            logger.info(f"成功登录到MT5账户: {account_info.login}, 账户名: {account_info.name}")
            logger.info(f"账户服务器: {account_info.server}, 货币: {account_info.currency}")
            logger.info(f"账户余额: {account_info.balance}, 净值: {account_info.equity}")
        else:
            logger.warning("登录成功但无法获取账户信息")
        
        # 检查是否启用了自动交易
        terminal_info = mt5.terminal_info()
        if terminal_info is not None:
            if not terminal_info.trade_allowed:
                logger.warning("MT5自动交易功能未启用，请在MT5界面中启用自动交易")
                logger.warning("请在MT5界面中点击顶部菜单栏的'自动交易'按钮，或检查'工具 > 选项 > 专家顾问'中的设置")
                # 不返回False，因为连接本身是成功的，只是提醒用户启用自动交易
            else:
                logger.info("MT5自动交易功能已启用")
        
        logger.info(f"MT5初始化和登录成功，账户: {login}，服务器: {server}")
        mt5_initialized = True
        return True
    
    except Exception as e:
        logger.error(f"MT5初始化异常: {e}", exc_info=True)
        mt5_initialized = False
        return False

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}", exc_info=True)
        raise

def get_signal_by_id(signal_id):
    """从数据库获取信号信息"""
    try:
        conn = get_db_connection()
        c = conn.cursor()
        c.execute('SELECT * FROM signals WHERE id = ?', (signal_id,))
        signal = c.fetchone()
        conn.close()
        return dict(signal) if signal else None
    except sqlite3.Error as e:
        logger.error(f"获取信号信息失败: {e}", exc_info=True)
        return None

def update_signal_status(signal_id, processed=True, order_ticket=None, order_result=None,
                        process_status=None, failure_reason=None, success_details=None):
    """更新信号处理状态"""
    with DB_LOCK:
        try:
            conn = get_db_connection()
            c = conn.cursor()

            process_time = datetime.now().isoformat()

            # 构建更新数据
            update_data = {
                'processed': 1 if processed else 0,
                'process_time': process_time
            }

            # 添加订单信息（如果有）
            if order_ticket is not None:
                update_data['order_ticket'] = order_ticket

            if order_result is not None:
                update_data['order_result'] = order_result

            # 添加新的状态字段
            if process_status is not None:
                update_data['process_status'] = process_status

            if failure_reason is not None:
                update_data['failure_reason'] = failure_reason

            if success_details is not None:
                update_data['success_details'] = success_details
            
            # 构建SQL语句
            fields = ', '.join([f"{k} = ?" for k in update_data.keys()])
            values = list(update_data.values())
            values.append(signal_id)
            
            c.execute(f"UPDATE signals SET {fields} WHERE id = ?", values)
            conn.commit()
            conn.close()
            
            logger.info(f"信号状态已更新，ID: {signal_id}, 处理状态: {'已处理' if processed else '未处理'}")
            return True
        except sqlite3.Error as e:
            logger.error(f"更新信号状态失败: {e}", exc_info=True)
            return False

def store_order(order_info):
    """存储订单信息到数据库"""
    with DB_LOCK:
        try:
            conn = get_db_connection()
            c = conn.cursor()
            
            # 获取信号信息，用于关联订单和获取关键价格级别
            signal_id = order_info.get('signal_id')
            signal = None
            
            if signal_id:
                try:
                    c.execute("SELECT * FROM signals WHERE id = ?", (signal_id,))
                    signal_row = c.fetchone()
                    if signal_row:
                        signal = dict(signal_row)
                except sqlite3.Error as e:
                    logger.warning(f"获取信号信息失败: {e}")
            
            # 存储订单信息，包含关联的信号关键价格级别
            c.execute('''INSERT INTO orders
                         (timestamp, ticket, trading_pair, operation, volume, price, sl, tp, signal_id, status)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                      (datetime.now().isoformat(),
                       order_info.get('ticket'),
                       order_info.get('symbol'),
                       order_info.get('operation'),
                       order_info.get('volume'),
                       order_info.get('price'),
                       order_info.get('sl'),
                       order_info.get('tp'),
                       signal_id,
                       'open'))
            
            # 如果有关联的信号，存储价格级别以便监控使用
            if signal and 'id' in order_info:
                try:
                    c.execute('''UPDATE orders SET 
                              mean = ?, r1 = ?, r2 = ?, s1 = ?, s2 = ?
                              WHERE ticket = ?''',
                              (signal.get('mean'),
                               signal.get('r1'),
                               signal.get('r2'),
                               signal.get('s1'),
                               signal.get('s2'),
                               order_info.get('ticket')))
                except sqlite3.Error as e:
                    logger.warning(f"更新订单关联信号价格级别失败: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"订单信息已存储，订单号: {order_info.get('ticket')}")
            return True
        except sqlite3.Error as e:
            logger.error(f"存储订单信息失败: {e}", exc_info=True)
            return False

def update_order_status(ticket, status, close_price=None, profit=None):
    """更新订单状态"""
    with DB_LOCK:
        try:
            conn = get_db_connection()
            c = conn.cursor()
            
            # 先获取订单信息以确定交易品种
            c.execute("SELECT trading_pair FROM orders WHERE ticket = ?", (ticket,))
            order_row = c.fetchone()
            trading_pair = order_row['trading_pair'] if order_row else None
            
            update_data = {
                'status': status,
                'close_time': datetime.now().isoformat()
            }
            
            if close_price is not None and trading_pair:
                # 按交易品种舍入平仓价格
                update_data['close_price'] = round_price(trading_pair, close_price)
            elif close_price is not None:
                update_data['close_price'] = close_price
            
            if profit is not None:
                # 盈亏通常不需要舍入，因为它是实际的现金值
                update_data['profit'] = profit
            
            # 构建SQL语句
            fields = ', '.join([f"{k} = ?" for k in update_data.keys()])
            values = list(update_data.values())
            values.append(ticket)
            
            c.execute(f"UPDATE orders SET {fields} WHERE ticket = ?", values)
            conn.commit()
            conn.close()
            
            logger.info(f"订单状态已更新，订单号: {ticket}, 状态: {status}")
            return True
        except sqlite3.Error as e:
            logger.error(f"更新订单状态失败: {e}", exc_info=True)
            return False

def map_trading_pair(platform_symbol):
    """将平台交易对映射到MT5交易对"""
    mt5_symbol = symbol_map.get(platform_symbol, platform_symbol)
    logger.info(f"交易对映射: {platform_symbol} -> {mt5_symbol}")
    return mt5_symbol

def get_current_price(symbol, signal_type):
    """获取当前市场价格"""
    try:
        # 获取当前报价
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            logger.error(f"获取交易对 {symbol} 价格失败")
            return None
        
        # 根据信号类型选择价格
        if signal_type.lower() == 'buy':
            price = tick.ask  # 买入价
        else:
            price = tick.bid  # 卖出价
        
        logger.info(f"当前 {symbol} {'买入' if signal_type.lower() == 'buy' else '卖出'}价: {price}")
        return price
    except Exception as e:
        logger.error(f"获取价格时发生错误: {e}", exc_info=True)
        return None

def get_symbol_digits(symbol):
    """获取交易品种的小数位数"""
    try:
        # 首先检查配置字典中是否有预定义的小数位
        for pattern, digits in SYMBOL_DIGITS.items():
            if pattern in symbol:
                logger.debug(f"根据配置获取交易品种 {symbol} 小数位: {digits}")
                return digits
        
        # 如果没有预定义，则尝试从MT5获取
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            logger.debug(f"从MT5获取交易品种 {symbol} 小数位: {symbol_info.digits}")
            return symbol_info.digits
        
        logger.error(f"获取交易品种 {symbol} 信息失败")
        
        # 未能获取到信息，使用固定规则
        if "XAU" in symbol:  # 黄金
            return 2
        elif "BTC" in symbol:  # 比特币
            return 1
        elif "ETH" in symbol:  # 以太坊
            return 1
        elif "JPY" in symbol:  # 日元对
            return 3
        elif "BRENT" in symbol:  # 布伦特原油
            return 2
        elif "XTI" in symbol:  # WTI原油
            return 2
        elif "GBPUSD" in symbol:  # 英镑美元
            return 5
        else:
            return 5  # 其他品种默认值
    except Exception as e:
        logger.error(f"获取交易品种小数位数时发生错误: {e}", exc_info=True)
        # 根据不同交易品种设置默认值
        if "XAU" in symbol:  # 黄金
            return 2
        elif "BTC" in symbol:  # 比特币
            return 1
        elif "ETH" in symbol:  # 以太坊
            return 1
        elif "JPY" in symbol:  # 日元对
            return 3
        elif "BRENT" in symbol:  # 布伦特原油
            return 2
        elif "XTI" in symbol:  # WTI原油
            return 2
        elif "GBPUSD" in symbol:  # 英镑美元
            return 5
        else:
            return 5  # 其他品种默认值

def round_price(symbol, price):
    """根据交易品种舍入价格到适当的小数位"""
    if price is None:
        return None

    digits = get_symbol_digits(symbol)
    return round(price, digits)

def get_spread_multiplier(symbol):
    """获取交易品种的点差计算乘数"""
    if symbol in ["XAUUSD", "BRENT", "XTIUSD"]:  # 黄金和原油类
        return 10000
    elif symbol == "DXY":  # 美元指数
        return 1000
    elif "JPY" in symbol:  # 日元对
        return 100
    else:  # 其他外汇对
        return 100000

def calculate_sl_tp(symbol, signal_type, entry_price, signal=None):
    """计算止损止盈价格"""
    try:
        # 获取交易品种信息
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            logger.error(f"获取交易品种 {symbol} 信息失败")
            return None, None
        
        # 获取该交易对的点值和小数位数
        point = symbol_info.point
        digits = symbol_info.digits
        
        # 获取止损止盈计算方式
        sl_tp_method = config.get('sl_tp_calculation_method', 'points')  # 默认使用点数计算
        
        # 方法1: 使用点数计算止损止盈
        if sl_tp_method == 'points':
            # 对BTCUSD和ETHUSD使用配置的美元止损/止盈
            if symbol in ('BTCUSD', 'ETHUSD'):
                sl_points = config.get('symbol_sl_points', {}).get(symbol, config.get('default_sl_points', 0))
                tp_points = config.get('symbol_tp_points', {}).get(symbol, config.get('default_tp_points', 0))
                if signal_type.lower() == 'buy':
                    sl = round_price(symbol, entry_price - sl_points)
                    tp = round_price(symbol, entry_price + tp_points)
                else:
                    sl = round_price(symbol, entry_price + sl_points)
                    tp = round_price(symbol, entry_price - tp_points)
            else:
                # 获取止损止盈点数配置
                sl_points = config.get('symbol_sl_points', {}).get(symbol, config.get('default_sl_points', 400))
                tp_points = config.get('symbol_tp_points', {}).get(symbol, config.get('default_tp_points', 400))
                # 计算止损止盈价格
                if signal_type.lower() == 'buy':
                    sl = round_price(symbol, entry_price - sl_points * point)
                    tp = round_price(symbol, entry_price + tp_points * point)
                else:
                    sl = round_price(symbol, entry_price + sl_points * point)
                    tp = round_price(symbol, entry_price - tp_points * point)
            logger.info(f"{symbol} {'买入' if signal_type.lower() == 'buy' else '卖出'} 使用点数计算 - 止损: {sl}, 止盈: {tp}, 入场价: {entry_price}")
        
        # 方法2: 使用信号中的R2, S2, MEAN值计算止损止盈
        elif sl_tp_method == 'signal_levels' and signal is not None:
            # 提取所需的值
            s2 = signal.get('s2')
            r2 = signal.get('r2')
            mean = signal.get('mean')
            
            # 检查必要的值是否存在
            if (signal_type.lower() == 'buy' and (mean is None or r2 is None)) or \
               (signal_type.lower() == 'sell' and (mean is None or s2 is None)):
                logger.warning(f"信号缺少必要的值，无法使用信号级别计算止损止盈，改用点数计算")
                # 回退到点数计算方法
                sl_points = config.get('symbol_sl_points', {}).get(symbol, config.get('default_sl_points', 400))
                tp_points = config.get('symbol_tp_points', {}).get(symbol, config.get('default_tp_points', 400))
                
                if signal_type.lower() == 'buy':
                    sl = round_price(symbol, entry_price - sl_points * point)
                    tp = round_price(symbol, entry_price + tp_points * point)
                else:
                    sl = round_price(symbol, entry_price + sl_points * point)
                    tp = round_price(symbol, entry_price - tp_points * point)
            else:
                # 根据信号类型使用不同的值
                if signal_type.lower() == 'buy':
                    sl = round_price(symbol, float(mean)) if mean is not None else None
                    tp = round_price(symbol, float(r2)) if r2 is not None else None
                else:  # sell
                    sl = round_price(symbol, float(mean)) if mean is not None else None
                    tp = round_price(symbol, float(s2)) if s2 is not None else None
                
                logger.info(f"{symbol} {'买入' if signal_type.lower() == 'buy' else '卖出'} 使用信号级别计算 - 止损: {sl}, 止盈: {tp}, 入场价: {entry_price}")
        else:
            # 默认使用点数计算方式
            logger.warning(f"无法识别的止损止盈计算方式 {sl_tp_method} 或缺少信号数据，使用点数计算")
            sl_points = config.get('symbol_sl_points', {}).get(symbol, config.get('default_sl_points', 400))
            tp_points = config.get('symbol_tp_points', {}).get(symbol, config.get('default_tp_points', 400))
            
            if signal_type.lower() == 'buy':
                sl = round_price(symbol, entry_price - sl_points * point)
                tp = round_price(symbol, entry_price + tp_points * point)
            else:
                sl = round_price(symbol, entry_price + sl_points * point)
                tp = round_price(symbol, entry_price - tp_points * point)
            
            logger.info(f"{symbol} {'买入' if signal_type.lower() == 'buy' else '卖出'} 默认使用点数计算 - 止损: {sl}, 止盈: {tp}, 入场价: {entry_price}")
        
        return sl, tp
    except Exception as e:
        logger.error(f"计算止损止盈价格时发生错误: {e}", exc_info=True)
        return None, None

def execute_trade(signal):
    """执行交易信号"""
    # 确保使用最新配置
    load_config()
    
    if not mt5_initialized and not init_mt5():
        error_msg = "MT5未初始化，无法执行交易"
        logger.error(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg,
                           process_status='failed', failure_reason=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
        
    # 检查当前时间是否允许交易
    if config.get('enable_time_filter', False) and not is_trading_allowed_by_time():
        error_msg = f"当前时间不在允许交易的时段内，信号ID: {signal['id']}"
        logger.warning(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg,
                           process_status='failed', failure_reason="交易时段限制")
        # 不发送通知，因为这是正常的时段过滤，不是错误
        return False
        
    # 检查交易品种是否启用
    trading_pair = signal['trading_pair']
    mt5_symbol = map_trading_pair(trading_pair)
    
    # 获取已启用的交易品种列表
    enabled_symbols = config.get('enabled_symbols', {})
    
    # 如果交易品种未启用，不执行交易
    if mt5_symbol not in enabled_symbols or not enabled_symbols.get(mt5_symbol, True):
        error_msg = f"交易品种 {mt5_symbol} 已被禁用，不执行交易，信号ID: {signal['id']}"
        logger.warning(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg,
                           process_status='failed', failure_reason="交易品种已禁用")
        return False

    # 检查活跃订单数量限制
    max_active_orders = config.get('symbol_max_active_orders', {}).get(trading_pair, 2)  # 默认限制2个
    current_active_count = get_active_orders_count_by_symbol(mt5_symbol)

    if current_active_count >= max_active_orders:
        error_msg = f"{trading_pair}超出配置的活跃订单数量限制(当前:{current_active_count}, 限制:{max_active_orders})，信号ID: {signal['id']}"
        logger.warning(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg)
        return False

    # 检查是否启用了自动交易
    terminal_info = mt5.terminal_info()
    if terminal_info is not None and not terminal_info.trade_allowed:
        error_msg = "MT5自动交易功能未启用，无法执行交易。请在MT5界面中启用自动交易。"
        logger.error(error_msg)
        update_signal_status(signal['id'], processed=True, order_result=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
    
    signal_type = signal['signal_type']
    signal_id = signal['id']
    
    # 映射交易对
    mt5_symbol = map_trading_pair(trading_pair)
    
    # 检查交易对是否有效
    if not mt5.symbol_select(mt5_symbol, True):
        error_msg = f"交易对 {mt5_symbol} 不可用或不存在"
        logger.error(error_msg)
        update_signal_status(signal_id, processed=True, order_result=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
    
    # 获取当前市场价格
    price = get_current_price(mt5_symbol, signal_type)
    if not price:
        error_msg = f"无法获取 {mt5_symbol} 的价格"
        update_signal_status(signal_id, processed=True, order_result=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
    
    # 计算止损止盈价格
    sl, tp = calculate_sl_tp(mt5_symbol, signal_type, price, signal)
    if sl is None or tp is None:
        error_msg = f"计算 {mt5_symbol} 的止损止盈价格失败"
        update_signal_status(signal_id, processed=True, order_result=error_msg)
        bark_notifier.notify_error(error_msg)
        return False
    
    # 获取交易量 - 首先尝试从信号中获取，如果没有再使用配置
    volume = signal.get('volume') if signal.get('volume') else config.get('symbol_volumes', {}).get(trading_pair, config.get('default_volume', 0.1))
    logger.info(f"使用交易量: {volume} (来源: {'信号' if signal.get('volume') else '配置'})")
    
    # 如果信号中有止损止盈值，优先使用
    signal_sl = None
    signal_tp = None
    
    # 检查信号中是否有止损止盈数据，可能存储在不同的字段中
    if 'sl' in signal:
        signal_sl = signal['sl']
    elif 's1' in signal:  # 可能使用s1作为止损
        signal_sl = signal['s1']
    
    if 'tp' in signal:
        signal_tp = signal['tp']
    elif 'r1' in signal:  # 可能使用r1作为止盈
        signal_tp = signal['r1']
    
    # 使用信号中的值或计算的值，并确保正确舍入
    if signal_sl is not None:
        final_sl = round_price(mt5_symbol, float(signal_sl))
    else:
        final_sl = sl  # sl已经在calculate_sl_tp中舍入
    
    if signal_tp is not None:
        final_tp = round_price(mt5_symbol, float(signal_tp))
    else:
        final_tp = tp  # tp已经在calculate_sl_tp中舍入
    
    # 重构SL/TP逻辑，根据配置决定使用点数或信号级别
    sl_tp_method = config.get('sl_tp_calculation_method', 'points')
    if sl_tp_method == 'signal_levels':
        final_sl = round_price(mt5_symbol, float(signal_sl)) if signal_sl not in (None, 0) else None
        final_tp = round_price(mt5_symbol, float(signal_tp)) if signal_tp not in (None, 0) else None
    else:
        sl_points = config.get('symbol_sl_points', {}).get(mt5_symbol, config.get('default_sl_points', 0))
        tp_points = config.get('symbol_tp_points', {}).get(mt5_symbol, config.get('default_tp_points', 0))
        final_sl = sl if sl_points > 0 else None
        final_tp = tp if tp_points > 0 else None

    # 舍入入场价
    price = round_price(mt5_symbol, price)

    # 检查最小止停距离，避免Invalid stops错误
    symbol_info = mt5.symbol_info(mt5_symbol)
    min_dist = (symbol_info.trade_stops_level * symbol_info.point) if symbol_info else 0
    if final_sl is not None and abs(price - final_sl) < min_dist:
        logger.warning(f"SL距离过近({abs(price-final_sl)}<{min_dist})，忽略SL")
        final_sl = None
    if final_tp is not None and abs(final_tp - price) < min_dist:
        logger.warning(f"TP距离过近({abs(final_tp-price)}<{min_dist})，忽略TP")
        final_tp = None

    logger.info(f"最终止损价: {final_sl}")
    logger.info(f"最终止盈价: {final_tp}")
    
    # 设置交易请求，不在此处添加SL/TP，后面根据配置或点数判断是否添加
    # 针对BTCUSD/ETHUSD，先不带SL/TP下单，后续再设置
    skip_sl_tp = False
    if mt5_symbol in ("BTCUSD", "ETHUSD"):
        skip_sl_tp = True

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": mt5_symbol,
        "volume": float(volume),
        "type": mt5.ORDER_TYPE_BUY if signal_type.lower() == 'buy' else mt5.ORDER_TYPE_SELL,
        "price": price,
        "deviation": 10,
        "magic": 12345,
        "comment": f"Signal ID: {signal_id}",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }
    if not skip_sl_tp:
        if final_sl is not None:
            request['sl'] = final_sl
        if final_tp is not None:
            request['tp'] = final_tp

    logger.info(f"发送交易请求: {json.dumps(request, default=str)}")
    result = try_order_with_filling_modes(request)

    # 处理结果
    if hasattr(result, 'retcode') and result.retcode == mt5.TRADE_RETCODE_DONE:
        logger.info(f"交易成功，订单号: {result.order}")
        order_info = {
            'ticket': result.order,
            'symbol': mt5_symbol,
            'operation': signal_type.lower(),
            'volume': volume,
            'price': price,
            'sl': final_sl if not skip_sl_tp else None,
            'tp': final_tp if not skip_sl_tp else None,
            'signal_id': signal.get('id')
        }
        store_order(order_info)
        success_details = f"订单成功执行，票号: {result.order}, 价格: {entry_price}, 交易量: {volume}"
        update_signal_status(signal_id, processed=True, order_ticket=result.order, order_result="成功",
                           process_status='success', success_details=success_details)
        bark_notifier.notify_trade_execution(order_info)

        # 对BTCUSD/ETHUSD下单后再设置SL/TP
        if skip_sl_tp:
            # 获取最新持仓，查找该订单的入场价
            time.sleep(1)  # 等待订单入场
            positions = mt5.positions_get(ticket=result.order)
            if positions and len(positions) > 0:
                entry_price = positions[0].price_open
                sl_points = config.get('symbol_sl_points', {}).get(mt5_symbol, config.get('default_sl_points', 0))
                tp_points = config.get('symbol_tp_points', {}).get(mt5_symbol, config.get('default_tp_points', 0))
                if signal_type.lower() == 'buy':
                    new_sl = round_price(mt5_symbol, entry_price - sl_points)
                    new_tp = round_price(mt5_symbol, entry_price + tp_points)
                else:
                    new_sl = round_price(mt5_symbol, entry_price + sl_points)
                    new_tp = round_price(mt5_symbol, entry_price - tp_points)
                logger.info(f"BTCUSD/ETHUSD下单后设置SL/TP: SL={new_sl}, TP={new_tp}")
                modify_sl_tp(result.order, new_sl, new_tp)
        return True
    else:
        error_msg = f"交易失败: {result.retcode} - {result.comment}"
        logger.error(error_msg)
        update_signal_status(signal_id, processed=True, order_result=error_msg,
                           process_status='failed', failure_reason=f"MT5交易失败: {result.retcode} - {result.comment}")
        bark_notifier.notify_error(error_msg)
        return False

def modify_sl_tp(ticket, new_sl=None, new_tp=None):
    """修改止损止盈"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法修改止损止盈")
        return {'success': False, 'message': 'MT5未连接'}
    
    try:
        # 获取订单信息
        position = mt5.positions_get(ticket=ticket)
        if not position:
            logger.error(f"未找到订单 {ticket}")
            return {'success': False, 'message': f'未找到订单 {ticket}'}
        
        position = position[0]
        symbol = position.symbol
        
        # 如果未指定新值，则使用当前值
        if new_sl is None:
            new_sl = position.sl
        else:
            # 按交易品种舍入止损价
            new_sl = round_price(symbol, float(new_sl))
        
        if new_tp is None:
            new_tp = position.tp
        else:
            # 按交易品种舍入止盈价
            new_tp = round_price(symbol, float(new_tp))
        
        logger.info(f"修改止损止盈 - 订单: {ticket}, 交易品种: {symbol}, 新止损: {new_sl}, 新止盈: {new_tp}")
        
        # 设置修改请求
        request = {
            "action": mt5.TRADE_ACTION_SLTP,
            "symbol": symbol,
            "position": ticket,
            "sl": new_sl,
            "tp": new_tp
        }
        
        logger.info(f"发送修改止损止盈请求: {json.dumps(request, default=str)}")
        
        # 执行修改
        result = mt5.order_send(request)
        
        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
            logger.info(f"止损止盈修改成功，订单号: {ticket}")
            # 在数据库中更新SL/TP值
            conn = sqlite3.connect('trading_data.db')
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE orders SET sl = ?, tp = ? WHERE ticket = ? AND status IN ('open', 'partially_closed')
            ''', (new_sl, new_tp, ticket))
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': '止损止盈修改成功'}
        else:
            error_code = result.retcode if result else "未知"
            error_msg = result.comment if result else "未知错误"
            logger.error(f"止损止盈修改失败: {error_code} - {error_msg}")
            return {'success': False, 'message': f'修改失败: {error_msg}'}
    
    except Exception as e:
        logger.error(f"修改止损止盈时发生错误: {e}", exc_info=True)
        return {'success': False, 'message': f'修改时发生错误: {str(e)}'}

def close_position(ticket, partial_volume=None):
    """平仓指定订单"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法平仓")
        return {'success': False, 'message': 'MT5未连接'}
    
    try:
        # 获取订单信息
        position = mt5.positions_get(ticket=ticket)
        if not position:
            logger.error(f"未找到订单 {ticket}")
            return {'success': False, 'message': f'未找到订单 {ticket}'}
        
        position = position[0]
        
        # 设置交易量
        volume = partial_volume if partial_volume else position.volume
        if volume > position.volume:
            volume = position.volume
        
        # 获取当前价格
        if position.type == mt5.POSITION_TYPE_BUY:
            price = mt5.symbol_info_tick(position.symbol).bid  # 卖出价格
            trade_type = mt5.ORDER_TYPE_SELL
        else:
            price = mt5.symbol_info_tick(position.symbol).ask  # 买入价格
            trade_type = mt5.ORDER_TYPE_BUY
        
        # 设置平仓请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": float(volume),
            "type": trade_type,
            "position": ticket,
            "price": price,
            "deviation": 10,
            "magic": 12345,
            "comment": f"Close position {ticket}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        
        logger.info(f"发送平仓请求: {json.dumps(request, default=str)}")
        
        # 执行平仓
        result = try_order_with_filling_modes(request)
        
        # 处理结果
        if result['success']:
            logger.info(f"平仓成功，订单号: {ticket}, 平仓量: {volume}")
            
            # 计算盈亏
            profit = position.profit
            
            # 计算实际盈亏（如果是部分平仓）
            if partial_volume and partial_volume < position.volume:
                profit = profit * (partial_volume / position.volume)
            
            # 计算价格差
            if position.type == mt5.POSITION_TYPE_BUY:
                points = (price - position.price_open) / position.point
            else:
                points = (position.price_open - price) / position.point
            
            # 更新订单状态
            status = "closed" if partial_volume is None or partial_volume >= position.volume else "partially_closed"
            update_order_status(ticket, status, close_price=price, profit=profit)
            
            # 交易信息
            trade_info = {
                'ticket': ticket,
                'symbol': position.symbol,
                'operation': 'buy' if position.type == mt5.POSITION_TYPE_BUY else 'sell',
                'volume': volume,
                'open_price': position.price_open,
                'close_price': price,
                'profit': profit,
                'points': points
            }
            
            # 发送通知
            try:
                logger.info(f"准备发送平仓通知: 订单{ticket}, 盈亏{profit}")
                notification_result = bark_notifier.notify_trade_closed(trade_info)
                if notification_result:
                    logger.info(f"平仓通知发送成功: 订单{ticket}")
                else:
                    logger.warning(f"平仓通知发送失败: 订单{ticket}")
            except Exception as e:
                logger.error(f"发送平仓通知时出错: {e}", exc_info=True)
            
            return {'success': True, 'message': '平仓成功', 'ticket': ticket, 'volume': volume, 'profit': profit}
        else:
            logger.error(f"平仓失败: {result.get('error', '未知错误')}")
            return {'success': False, 'message': f"平仓失败: {result.get('error', '未知错误')}"}
    
    except Exception as e:
        logger.error(f"平仓时发生错误: {e}", exc_info=True)
        return {'success': False, 'message': f"平仓时发生错误: {str(e)}"}

def close_all_positions():
    """平仓所有持仓，无论交易对和来源"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法平仓所有持仓")
        return {'success': False, 'message': 'MT5未连接'}
    
    try:
        # 获取所有持仓
        positions = mt5.positions_get()
        if not positions:
            logger.info("没有持仓需要平仓")
            return {'success': True, 'message': '没有需要平仓的持仓', 'closed_count': 0}
        
        closed_count = 0
        failed_count = 0
        
        # 逐一平仓
        for position in positions:
            result = close_position(position.ticket)
            if result.get('success', False):
                closed_count += 1
            else:
                failed_count += 1
                logger.error(f"平仓订单 {position.ticket} 失败: {result.get('message', '未知错误')}")
        
        # 返回结果
        if failed_count == 0:
            return {'success': True, 'message': f'成功平仓所有 {closed_count} 个持仓', 'closed_count': closed_count}
        else:
            return {
                'success': True, 
                'message': f'部分平仓成功: {closed_count} 个成功, {failed_count} 个失败', 
                'closed_count': closed_count,
                'failed_count': failed_count
            }

    except Exception as e:
        logger.error(f"平仓所有持仓时发生错误: {e}", exc_info=True)
        return {'success': False, 'message': f'平仓过程中发生错误: {str(e)}'}

def close_profitable_positions():
    """平仓所有盈利的持仓"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法平仓")
        return {'success': False, 'message': 'MT5未连接'}

    try:
        # 获取所有持仓
        positions = mt5.positions_get()
        if not positions:
            logger.info("没有持仓需要平仓")
            return {'success': True, 'message': '没有盈利持仓需要平仓', 'closed_count': 0}

        # 筛选盈利的持仓
        profitable_positions = [pos for pos in positions if pos.profit > 0]

        if not profitable_positions:
            logger.info("没有盈利的持仓需要平仓")
            return {'success': True, 'message': '当前没有盈利的持仓', 'closed_count': 0}

        closed_count = 0
        failed_count = 0
        total_profit = 0

        logger.info(f"找到 {len(profitable_positions)} 个盈利持仓，开始平仓")

        # 逐一平仓盈利的持仓
        for position in profitable_positions:
            logger.info(f"平仓盈利订单: {position.ticket}, 当前盈利: {position.profit:.2f}")
            result = close_position(position.ticket)
            if result.get('success', False):
                closed_count += 1
                total_profit += position.profit
                logger.info(f"成功平仓盈利订单 {position.ticket}, 盈利: {position.profit:.2f}")
            else:
                failed_count += 1
                logger.error(f"平仓盈利订单 {position.ticket} 失败: {result.get('message', '未知错误')}")

        # 返回结果
        if failed_count == 0:
            return {
                'success': True,
                'message': f'成功平仓所有 {closed_count} 个盈利持仓，总盈利: {total_profit:.2f}',
                'closed_count': closed_count,
                'total_profit': total_profit
            }
        else:
            return {
                'success': True,
                'message': f'部分平仓成功: {closed_count} 个成功, {failed_count} 个失败，已实现盈利: {total_profit:.2f}',
                'closed_count': closed_count,
                'failed_count': failed_count,
                'total_profit': total_profit
            }

    except Exception as e:
        logger.error(f"平仓盈利持仓时发生错误: {e}", exc_info=True)
        return {'success': False, 'message': f'平仓失败: {str(e)}'}

def process_signal(signal_id):
    """处理交易信号"""
    logger.info(f"开始处理信号，ID: {signal_id}")

    # 获取信号信息
    signal = get_signal_by_id(signal_id)
    if not signal:
        logger.error(f"未找到信号，ID: {signal_id}")
        return False

    # 检查信号是否已处理
    if signal['processed'] == 1:
        logger.warning(f"信号已处理，跳过，ID: {signal_id}")
        # 发送跳过处理通知
        try:
            bark_notifier.notify_signal_processing(signal, 'skipped', '信号已经处理过')
        except Exception as e:
            logger.error(f"发送信号处理通知失败: {e}")
        return False

    # 检查交易开关
    if not config.get('enable_trading', False):
        logger.warning("交易功能未启用，信号不会执行")
        update_signal_status(signal_id, processed=True, order_result="交易功能未启用",
                             process_status='failed', failure_reason="交易功能未启用")
        # 发送处理失败通知
        try:
            bark_notifier.notify_signal_processing(signal, 'failed', '交易功能未启用')
        except Exception as e:
            logger.error(f"发送信号处理通知失败: {e}")
        return False

    # 发送开始处理通知
    try:
        bark_notifier.notify_signal_processing(signal, 'started')
    except Exception as e:
        logger.error(f"发送信号处理通知失败: {e}")

    # 执行交易
    result = execute_trade(signal)

    # 发送处理结果通知
    try:
        if result:
            bark_notifier.notify_signal_processing(signal, 'success', '信号处理成功，交易已执行')
        else:
            bark_notifier.notify_signal_processing(signal, 'failed', '信号处理失败，交易未执行')
    except Exception as e:
        logger.error(f"发送信号处理通知失败: {e}")

    return result

def check_for_signals():
    """检查是否有待处理的信号"""
    try:
        # 检查信号文件是否存在
        if os.path.exists('signal_pending.txt'):
            with open('signal_pending.txt', 'r') as f:
                signal_id = f.read().strip()
            
            # 删除信号文件
            os.remove('signal_pending.txt')
            
            # 处理信号
            if signal_id.isdigit():
                signal_id = int(signal_id)
                logger.info(f"发现待处理信号，ID: {signal_id}")
                process_signal(signal_id)
            else:
                logger.warning(f"无效的信号ID: {signal_id}")
    
    except Exception as e:
        logger.error(f"检查信号时发生错误: {e}", exc_info=True)

def monitor_positions():
    """监控持仓情况"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法监控持仓")
        return
    
    try:
        # 获取所有持仓
        positions = mt5.positions_get()
        
        if positions:
            logger.info(f"当前持仓数量: {len(positions)}")
            for position in positions:
                # 计算当前盈亏
                profit = position.profit
                
                # 更新数据库中的订单信息（如果需要）
                # ...
        
        # 可以添加更多的监控逻辑，如设置自动平仓条件等
        
    except Exception as e:
        logger.error(f"监控持仓时发生错误: {e}", exc_info=True)

def get_account_info():
    """获取MT5账户信息，包括余额和浮动盈亏"""
    # 检查MT5是否已初始化，如果没有则尝试初始化
    if not mt5_initialized:
        logger.warning("MT5未初始化，尝试初始化MT5连接")
        if not init_mt5():
            logger.error("MT5初始化失败，无法获取账户信息")
            return {
                'balance': 0.0,
                'equity': 0.0,
                'profit': 0.0,
                'margin': 0.0,
                'free_margin': 0.0,
                'margin_level': 0.0,
                'currency': 'USD'
            }
        else:
            logger.info("MT5成功初始化，继续获取账户信息")
    
    try:
        # 再次检查MT5终端连接状态
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error("MT5终端未连接，无法获取账户信息")
            # 再次尝试初始化
            if not init_mt5():
                logger.error("MT5重新初始化失败")
                return {
                    'balance': 0.0,
                    'equity': 0.0,
                    'profit': 0.0,
                    'margin': 0.0,
                    'free_margin': 0.0,
                    'margin_level': 0.0,
                    'currency': 'USD'
                }
        
        # 获取账户信息
        logger.info("开始获取MT5账户信息")
        account_info = mt5.account_info()
        if account_info is None:
            error = mt5.last_error()
            logger.error(f"无法获取MT5账户信息: {error}")
            return {
                'balance': 0.0,
                'equity': 0.0,
                'profit': 0.0,
                'margin': 0.0,
                'free_margin': 0.0,
                'margin_level': 0.0,
                'currency': 'USD'
            }
        
        # 转换为字典
        account_dict = {
            'balance': float(account_info.balance),          # 账户余额
            'equity': float(account_info.equity),            # 账户净值
            'profit': float(account_info.profit),            # 浮动盈亏
            'margin': float(account_info.margin),            # 已用保证金
            'free_margin': float(account_info.margin_free),  # 可用保证金
            'margin_level': float(account_info.margin_level) if account_info.margin_level else 0.0,  # 保证金水平
            'currency': account_info.currency                # 账户货币
        }
        
        logger.debug(f"账户信息: {account_dict}")
        return account_dict
    
    except Exception as e:
        logger.error(f"获取账户信息时发生错误: {e}", exc_info=True)
        return {
            'balance': 0.0,
            'equity': 0.0,
            'profit': 0.0,
            'margin': 0.0,
            'free_margin': 0.0,
            'margin_level': 0.0,
            'currency': 'USD'
        }

def main():
    """主函数"""
    logger.info("MT5交易模块启动")
    
    # 加载配置
    if not load_config():
        logger.error("加载配置失败，程序退出")
        sys.exit(1)
    
    # 初始化MT5
    if not init_mt5():
        logger.error("初始化MT5失败，程序退出")
        sys.exit(1)
    
    # 主循环
    try:
        while True:
            # 检查信号
            check_for_signals()
            
            # 监控持仓
            monitor_positions()
            
            # 定期重新初始化MT5，保持连接
            if mt5_initialized and mt5.terminal_info() is None:
                logger.warning("MT5连接丢失，正在重新初始化...")
                init_mt5()
            
            # 休眠
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("收到终止信号，程序退出")
    except Exception as e:
        logger.error(f"程序异常: {e}", exc_info=True)
    finally:
        # 关闭MT5连接
        if mt5_initialized:
            mt5.shutdown()
            logger.info("已关闭MT5连接")

def get_all_positions():
    """获取MT5所有活跃持仓"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法获取持仓")
        return []
    
    try:
        # 获取所有活跃持仓
        positions = mt5.positions_get()
        if positions is None or len(positions) == 0:
            logger.info("当前没有活跃持仓")
            return []
        
        positions_list = []
        for position in positions:
            position_info = {
                'ticket': position.ticket,
                'time': position.time,
                'time_msc': position.time_msc,
                'time_update': position.time_update,
                'type': position.type,  # 0表示买入，1表示卖出
                'type_str': 'buy' if position.type == 0 else 'sell',
                'magic': position.magic,
                'position_id': position.identifier,
                'reason': position.reason,
                'volume': position.volume,
                'price_open': position.price_open,
                'sl': position.sl,
                'tp': position.tp,
                'price_current': position.price_current,
                'swap': position.swap,
                'profit': position.profit,
                'symbol': position.symbol,
                'comment': position.comment,
                'external_id': position.external_id
            }
            positions_list.append(position_info)
        
        logger.info(f"获取到{len(positions_list)}个活跃持仓")
        return positions_list
    
    except Exception as e:
        logger.error(f"获取持仓时发生错误: {e}", exc_info=True)
        return []

def get_active_orders_count_by_symbol(symbol):
    """获取指定交易对的活跃订单数量"""
    try:
        if not mt5_initialized and not init_mt5():
            logger.error("MT5未初始化，无法获取持仓")
            return 0

        # 获取指定交易对的持仓
        positions = mt5.positions_get(symbol=symbol)
        if positions is None:
            return 0

        return len(positions)
    except Exception as e:
        logger.error(f"获取交易对 {symbol} 活跃订单数量时发生错误: {e}", exc_info=True)
        return 0

def get_all_orders():
    """获取MT5所有挂单（未执行的订单）"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法获取挂单")
        return []
    
    try:
        # 获取所有挂单
        orders = mt5.orders_get()
        if orders is None or len(orders) == 0:
            logger.info("当前没有挂单")
            return []
        
        orders_list = []
        for order in orders:
            order_info = {
                'ticket': order.ticket,
                'time_setup': order.time_setup,
                'time_setup_msc': order.time_setup_msc,
                'time_expiration': order.time_expiration,
                'type': order.type,
                'type_str': get_order_type_string(order.type),
                'state': order.state,
                'magic': order.magic,
                'position_id': order.position_id,
                'reason': order.reason,
                'volume_initial': order.volume_initial,
                'volume_current': order.volume_current,
                'price_open': order.price_open,
                'sl': order.sl,
                'tp': order.tp,
                'price_current': order.price_current,
                'symbol': order.symbol,
                'comment': order.comment,
                'external_id': order.external_id
            }
            orders_list.append(order_info)
        
        logger.info(f"获取到{len(orders_list)}个挂单")
        return orders_list
    
    except Exception as e:
        logger.error(f"获取挂单时发生错误: {e}", exc_info=True)
        return []

def get_order_type_string(order_type):
    """获取订单类型的字符串描述"""
    order_types = {
        mt5.ORDER_TYPE_BUY: "买入市价单",
        mt5.ORDER_TYPE_SELL: "卖出市价单",
        mt5.ORDER_TYPE_BUY_LIMIT: "买入限价单",
        mt5.ORDER_TYPE_SELL_LIMIT: "卖出限价单",
        mt5.ORDER_TYPE_BUY_STOP: "买入停损单",
        mt5.ORDER_TYPE_SELL_STOP: "卖出停损单",
        mt5.ORDER_TYPE_BUY_STOP_LIMIT: "买入止损限价单",
        mt5.ORDER_TYPE_SELL_STOP_LIMIT: "卖出止损限价单"
    }
    
    return order_types.get(order_type, f"未知类型({order_type})")

# 已在上面定义了更完整的 close_position 函数，这里不再重复定义

def cancel_order(ticket):
    """取消挂单"""
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法取消订单")
        return {
            'success': False,
            'error': 'MT5未连接'
        }
    
    try:
        # 准备删除订单请求
        request = {
            "action": mt5.TRADE_ACTION_REMOVE,
            "order": ticket,
        }
        
        # 发送请求
        result = mt5.order_send(request)
        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
            logger.info(f"成功取消订单: #{ticket}")
            return {
                'success': True
            }
        else:
            error = mt5.last_error()
            logger.error(f"取消订单失败: #{ticket}, 错误: {error}")
            return {
                'success': False,
                'error': str(error)
            }
    
    except Exception as e:
        logger.error(f"取消订单时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'error': str(e)
        }

# 当模块被导入时自动初始化
# 先加载配置
load_config()

# 尝试自动初始化MT5，但不在导入时终止程序
try:
    if not mt5_initialized:
        init_result = init_mt5()
        if init_result:
            logger.info("MT5模块自动初始化成功")
        else:
            logger.warning("MT5模块自动初始化失败，稍后将重试")
except Exception as e:
    logger.error(f"MT5模块自动初始化异常: {e}", exc_info=True)

if __name__ == "__main__":
    main()

def get_real_time_prices(symbols):
    """
    获取指定交易品种的实时价格
    
    参数:
        symbols: 交易品种列表，例如 ["XAUUSD", "BTCUSD", "ETHUSD", "GBPJPY"]
        
    返回:
        字典，包含每个品种的当前价格信息 {symbol: {'bid': 价格, 'ask': 价格, 'time': 时间戳}}
    """
    result = {}
    for symbol in symbols:
        try:
            # 尝试获取交易品种的最新报价
            tick = mt5.symbol_info_tick(symbol)
            if tick:
                result[symbol] = {
                    'bid': tick.bid,
                    'ask': tick.ask,
                    'time': datetime.fromtimestamp(tick.time).strftime('%Y-%m-%d %H:%M:%S'),
                    'spread': round((tick.ask - tick.bid) * get_spread_multiplier(symbol), 1)  # 计算点差
                }
            else:
                logger.warning(f"无法获取 {symbol} 的报价")
                result[symbol] = {
                    'bid': 0.0,
                    'ask': 0.0,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'spread': 0.0,
                    'error': f"无法获取报价"
                }
        except Exception as e:
            logger.error(f"获取 {symbol} 报价时出错: {e}", exc_info=True)
            result[symbol] = {
                'bid': 0.0,
                'ask': 0.0,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'spread': 0.0,
                'error': str(e)
            }
    
    return result


def get_server_time():
    """获取MT5服务器时间"""
    try:
        # 检查MT5是否已初始化
        if not mt5_initialized:
            if not init_mt5():
                logger.error("无法获取服务器时间：MT5未初始化")
                return datetime.now()  # 失败时返回本地时间
        
        # 通过EURUSD报价获取服务器时间（EURUSD是常见的交易对，一般都可用）
        tick = mt5.symbol_info_tick("EURUSD")
        if tick is not None and hasattr(tick, 'time'):
            server_time = datetime.fromtimestamp(tick.time)
            logger.debug(f"获取MT5服务器时间成功: {server_time}")
            return server_time
        else:
            # 尝试其他常见交易对
            for symbol in ["XAUUSD", "GBPUSD", "USDJPY"]:
                tick = mt5.symbol_info_tick(symbol)
                if tick is not None and hasattr(tick, 'time'):
                    server_time = datetime.fromtimestamp(tick.time)
                    logger.debug(f"通过{symbol}获取MT5服务器时间成功: {server_time}")
                    return server_time
            
            logger.error("无法获取MT5服务器时间：所有尝试的交易对都失败")
            return datetime.now()  # 失败时返回本地时间
    except Exception as e:
        logger.error(f"获取MT5服务器时间时出错: {e}", exc_info=True)
        return datetime.now()  # 失败时返回本地时间
        
        
def is_trading_allowed_by_time():
    """检查当前时间是否允许交易（基于北京时间配置）"""
    try:
        if not config.get('enable_time_filter', False):
            return True
        
        # 获取真实北京时间
        now = get_beijing_time()
        current_day = str(now.weekday() + 1 if now.weekday() < 6 else 0)
        trading_times = config.get('trading_times', {})
        day_config = trading_times.get(current_day, {'enabled': False})
        if not day_config.get('enabled', False):
            logger.debug(f"当前星期{current_day}未启用交易")
            return False
        current_time = now.strftime('%H:%M')
        period1 = day_config.get('period1', {'start': '00:00', 'end': '00:00'})
        period2 = day_config.get('period2', {'start': '00:00', 'end': '00:00'})
        current_hour, current_minute = map(int, current_time.split(':'))
        current_minutes = current_hour * 60 + current_minute
        # 时间段1
        start1 = period1.get('start', '00:00')
        end1 = period1.get('end', '00:00')
        start1_hour, start1_minute = map(int, start1.split(':'))
        end1_hour, end1_minute = map(int, end1.split(':'))
        start1_minutes = start1_hour * 60 + start1_minute
        end1_minutes = end1_hour * 60 + end1_minute
        if start1_minutes <= end1_minutes:
            if start1_minutes <= current_minutes <= end1_minutes:
                logger.debug(f"当前北京时间 {current_time} 在时间段1内 [{start1} - {end1}]")
                return True
        else:
            if start1_minutes <= current_minutes or current_minutes <= end1_minutes:
                logger.debug(f"当前北京时间 {current_time} 在跨越午夜的时间段1内 [{start1} - {end1}]")
                return True
        # 时间段2
        start2 = period2.get('start', '00:00')
        end2 = period2.get('end', '00:00')
        start2_hour, start2_minute = map(int, start2.split(':'))
        end2_hour, end2_minute = map(int, end2.split(':'))
        start2_minutes = start2_hour * 60 + start2_minute
        end2_minutes = end2_hour * 60 + end2_minute
        if start2_minutes <= end2_minutes:
            if start2_minutes <= current_minutes <= end2_minutes:
                logger.debug(f"当前北京时间 {current_time} 在时间段2内 [{start2} - {end2}]")
                return True
        else:
            if start2_minutes <= current_minutes or current_minutes <= end2_minutes:
                logger.debug(f"当前北京时间 {current_time} 在跨越午夜的时间段2内 [{start2} - {end2}]")
                return True
        logger.debug(f"当前北京时间 {current_time} 不在配置的交易时段内")
        return False
    except Exception as e:
        logger.error(f"检查交易时间时出错: {e}", exc_info=True)
        return True

def test_time_filter():
    """测试时间过滤功能"""
    try:
        # 加载配置
        load_config()
        
        # 获取当前MT5服务器时间
        now = get_server_time()
        
        print(f"\n=== 时间过滤功能测试 ===")
        print(f"当前MT5服务器时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"星期几: {now.weekday() + 1 if now.weekday() < 6 else 0} (0=星期日, 1=星期一, ..., 6=星期六)")
        print(f"时间过滤功能是否启用: {config.get('enable_time_filter', False)}")
        
        # 获取当前是星期几
        current_day = str(now.weekday() + 1 if now.weekday() < 6 else 0)
        
        # 检查当天的配置
        trading_times = config.get('trading_times', {})
        day_config = trading_times.get(current_day, {'enabled': False})
        
        print(f"\n当天交易配置:")
        print(f"是否允许交易: {day_config.get('enabled', False)}")
        
        if day_config.get('enabled', False):
            period1 = day_config.get('period1', {'start': '00:00', 'end': '00:00'})
            period2 = day_config.get('period2', {'start': '00:00', 'end': '00:00'})
            
            print(f"时间段1: {period1.get('start')} - {period1.get('end')}")
            print(f"时间段2: {period2.get('start')} - {period2.get('end')}")
        
        # 测试当前时间是否允许交易
        is_allowed = is_trading_allowed_by_time()
        print(f"\n当前时间是否允许交易: {is_allowed}")
        
        return is_allowed
    
    except Exception as e:
        print(f"测试时间过滤功能时出错: {e}")
        return False

def test_beijing_time_filter():
    """测试基于北京时间的交易时段过滤功能"""
    try:
        # 加载配置
        load_config()
        
        # 获取北京时间
        from utils.beijing_time import get_beijing_time
        beijing_time = get_beijing_time()
        
        print(f"\n=== 北京时间交易过滤功能测试 ===")
        print(f"当前北京时间: {beijing_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"星期几: {beijing_time.weekday() + 1 if beijing_time.weekday() < 6 else 0} (0=星期日, 1=星期一, ..., 6=星期六)")
        print(f"时间过滤功能是否启用: {config.get('enable_time_filter', False)}")
        
        # 获取当前是星期几
        current_day = str(beijing_time.weekday() + 1 if beijing_time.weekday() < 6 else 0)
        
        # 检查当天的配置
        trading_times = config.get('trading_times', {})
        day_config = trading_times.get(current_day, {'enabled': False})
        
        print(f"\n当天交易配置:")
        print(f"是否允许交易: {day_config.get('enabled', False)}")
        
        if day_config.get('enabled', False):
            period1 = day_config.get('period1', {'start': '00:00', 'end': '00:00'})
            period2 = day_config.get('period2', {'start': '00:00', 'end': '00:00'})
            
            print(f"时间段1: {period1.get('start')} - {period1.get('end')}")
            print(f"时间段2: {period2.get('start')} - {period2.get('end')}")
        
        # 测试当前时间是否允许交易
        is_allowed = is_trading_allowed_by_time()
        print(f"\n当前时间是否允许交易: {is_allowed}")
        print(f"(此结果基于真实北京时间)")
        
        return is_allowed
    
    except Exception as e:
        print(f"测试北京时间过滤功能时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

# 如果直接运行此模块，执行测试
if __name__ == "__main__":
    # 测试基于北京时间的过滤功能
    test_beijing_time_filter()

def apply_sl_tp_to_all_positions():
    """
    将配置中的止盈止损点数应用到所有当前活跃的订单中
    
    根据最新的配置文件中的symbol_sl_points和symbol_tp_points值，
    重新计算并设置所有活跃订单的止盈止损价格
    
    返回:
        dict: 包含操作结果的字典，格式为：
            {
                'success': 是否整体成功,
                'updated_count': 成功更新的订单数量,
                'failed_count': 更新失败的订单数量,
                'details': 详细结果列表
            }
    """
    # reload latest configuration
    load_config()
    # ensure MT5 is initialized
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法应用止盈止损设置")
        return {
            'success': False,
            'message': 'MT5未连接',
            'updated_count': 0, 
            'failed_count': 0,
            'details': []
        }
    
    try:
        # 获取所有活跃订单
        positions = mt5.positions_get()
        if not positions:
            logger.info("当前没有活跃订单，无需应用止盈止损设置")
            return {
                'success': True,
                'message': '当前没有活跃订单',
                'updated_count': 0,
                'failed_count': 0,
                'details': []
            }
        
        updated_count = 0
        failed_count = 0
        details = []
        
        # 获取配置中的止盈止损点数设置
        sl_tp_method = config.get('sl_tp_calculation_method', 'points')
        
        # 循环处理每个活跃订单
        for position in positions:
            symbol = position.symbol
            ticket = position.ticket
            position_type = position.type  # 0表示买入(BUY)，1表示卖出(SELL)
            price_open = position.price_open
            current_sl = position.sl
            current_tp = position.tp
            
            # 检查交易品种是否在配置中
            if symbol not in config.get('symbol_sl_points', {}) or symbol not in config.get('symbol_tp_points', {}):
                logger.warning(f"订单 #{ticket} 的交易品种 {symbol} 在配置中没有止盈止损设置，将使用默认设置")
                sl_points = config.get('default_sl_points', 100)
                tp_points = config.get('default_tp_points', 100)
            else:
                sl_points = config.get('symbol_sl_points', {}).get(symbol)
                tp_points = config.get('symbol_tp_points', {}).get(symbol)
            
            # 获取点值
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"无法获取交易品种 {symbol} 的信息")
                failed_count += 1
                details.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': False,
                    'message': f"无法获取交易品种信息"
                })
                continue
                
            point = symbol_info.point
            
            # 计算新的止损价格
            new_sl = 0.0
            if position_type == 0:  # BUY
                new_sl = price_open - sl_points * point
            else:  # SELL
                new_sl = price_open + sl_points * point
            
            # 计算新的止盈价格
            new_tp = 0.0
            if position_type == 0:  # BUY
                new_tp = price_open + tp_points * point
            else:  # SELL
                new_tp = price_open - tp_points * point
            
            # 按交易品种舍入价格
            new_sl = round_price(symbol, new_sl)
            new_tp = round_price(symbol, new_tp)
            
            # 如果止损止盈没有变化，则跳过
            if abs(new_sl - current_sl) < 0.00001 and abs(new_tp - current_tp) < 0.00001:
                logger.info(f"订单 #{ticket} 的止盈止损无需更新 (SL: {current_sl} -> {new_sl}, TP: {current_tp} -> {new_tp})")
                updated_count += 1  # 虽然没有实际更新，但计入成功数量
                details.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': True,
                    'message': f"止盈止损无变化，无需更新"
                })
                continue
            
            # 选择该交易品种，确保请求生效
            if not mt5.symbol_select(symbol, True):
                logger.error(f"symbol_select失败，无法选择交易品种 {symbol}")
                failed_count += 1
                details.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': False,
                    'message': f"symbol_select 失败"
                })
                continue
            # 构建修改止盈止损请求
            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "symbol": symbol,
                "position": ticket,
                "sl": new_sl,
                "tp": new_tp
            }
            
            logger.info(f"应用止盈止损 - 订单: #{ticket}, 交易品种: {symbol}, 止损: {current_sl} -> {new_sl}, 止盈: {current_tp} -> {new_tp}")
            
            # 执行修改
            result = mt5.order_send(request)
            
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                logger.info(f"订单 #{ticket} 的止盈止损更新成功")
                updated_count += 1
                details.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': True,
                    'message': f"止盈止损更新成功 (SL: {current_sl} -> {new_sl}, TP: {current_tp} -> {new_tp})"
                })
                
                # 在数据库中更新SL/TP值
                try:
                    conn = sqlite3.connect('trading_data.db')
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE orders SET sl = ?, tp = ? WHERE ticket = ? AND status IN ('open', 'partially_closed')
                    ''', (new_sl, new_tp, ticket))
                    conn.commit()
                    conn.close()
                except Exception as db_error:
                    logger.error(f"更新数据库中的止盈止损记录时出错: {db_error}")
            else:
                error_code = result.retcode if result else "未知"
                error_msg = result.comment if result else "未知错误"
                logger.error(f"订单 #{ticket} 的止盈止损更新失败: {error_code} - {error_msg}")
                failed_count += 1
                details.append({
                    'ticket': ticket,
                    'symbol': symbol,
                    'success': False,
                    'message': f"止盈止损更新失败: {error_msg}"
                })
        
        # 生成总结信息
        success = failed_count == 0
        message = f"成功更新 {updated_count} 个订单的止盈止损，失败 {failed_count} 个"
        logger.info(message)
        
        return {
            'success': success,
            'message': message,
            'updated_count': updated_count,
            'failed_count': failed_count,
            'details': details
        }
    
    except Exception as e:
        logger.error(f"应用止盈止损设置时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'message': f'应用止盈止损设置时发生错误: {str(e)}',
            'updated_count': 0,
            'failed_count': 0,
            'details': []
        }

def direct_market_order(symbol, operation, volume, comment=None):
    """
    直接以市场价格执行交易，不经过信号系统

    参数:
        symbol: 交易品种
        operation: 交易方向 ('buy' 或 'sell')
        volume: 交易量
        comment: 订单注释

    返回:
        字典，包含交易结果
    """
    if not mt5_initialized and not init_mt5():
        logger.error("MT5未初始化，无法执行交易")
        return {'success': False, 'message': 'MT5未连接'}

    try:
        # 确保使用最新配置
        load_config()

        # 检查交易品种是否启用
        mt5_symbol = map_trading_pair(symbol)

        # 获取已启用的交易品种列表
        enabled_symbols = config.get('enabled_symbols', {})

        # 如果交易品种未启用，不执行交易
        if mt5_symbol not in enabled_symbols or not enabled_symbols.get(mt5_symbol, True):
            error_msg = f"交易品种 {mt5_symbol} 已被禁用，不执行交易"
            logger.warning(error_msg)
            return {'success': False, 'message': error_msg}

        # 检查是否启用了自动交易
        terminal_info = mt5.terminal_info()
        if terminal_info is not None and not terminal_info.trade_allowed:
            error_msg = "MT5自动交易功能未启用，无法执行交易"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

        # 检查交易对是否有效
        if not mt5.symbol_select(mt5_symbol, True):
            error_msg = f"交易对 {mt5_symbol} 不可用或不存在"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

        # 获取当前市场价格
        if operation.lower() == 'buy':
            price = mt5.symbol_info_tick(mt5_symbol).ask
            order_type = mt5.ORDER_TYPE_BUY
        else:
            price = mt5.symbol_info_tick(mt5_symbol).bid
            order_type = mt5.ORDER_TYPE_SELL

        # 设置交易请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": mt5_symbol,
            "volume": float(volume),
            "type": order_type,
            "price": price,
            "deviation": 10,
            "magic": 12345,
            "comment": comment if comment else "Direct market order",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }

        logger.info(f"发送直接市场下单请求: {json.dumps(request, default=str)}")

        # 执行交易
        result = try_order_with_filling_modes(request)

        if result['success']:
            order_ticket = result['order']
            logger.info(f"直接市场下单成功，订单号: {order_ticket}")

            # 等待订单入场
            time.sleep(1)

            # 获取持仓信息，包括入场价格
            positions = mt5.positions_get(ticket=order_ticket)
            entry_price = None

            if positions and len(positions) > 0:
                position = positions[0]
                entry_price = position.price_open

                # 计算止损止盈价格
                sl_points = config.get('symbol_sl_points', {}).get(mt5_symbol, config.get('default_sl_points', 0))
                tp_points = config.get('symbol_tp_points', {}).get(mt5_symbol, config.get('default_tp_points', 0))

                sl = None
                tp = None

                if operation.lower() == 'buy':
                    if sl_points > 0:
                        sl = round_price(mt5_symbol, entry_price - (sl_points * mt5.symbol_info(mt5_symbol).point))
                    if tp_points > 0:
                        tp = round_price(mt5_symbol, entry_price + (tp_points * mt5.symbol_info(mt5_symbol).point))
                else:
                    if sl_points > 0:
                        sl = round_price(mt5_symbol, entry_price + (sl_points * mt5.symbol_info(mt5_symbol).point))
                    if tp_points > 0:
                        tp = round_price(mt5_symbol, entry_price - (tp_points * mt5.symbol_info(mt5_symbol).point))

                # 设置止损止盈
                if sl_points > 0 or tp_points > 0:
                    logger.info(f"设置订单 {order_ticket} 的止损止盈: SL={sl}, TP={tp}")
                    modify_result = modify_sl_tp(order_ticket, sl, tp)
                    if not modify_result or not modify_result.get('success', False):
                        logger.warning(f"设置止损止盈失败: {modify_result.get('message', '未知错误') if modify_result else '未知错误'}")

                # 保存订单信息到数据库
                order_info = {
                    'ticket': order_ticket,
                    'symbol': mt5_symbol,
                    'operation': operation.lower(),
                    'volume': volume,
                    'price': entry_price,
                    'sl': sl,
                    'tp': tp,
                    'signal_id': None  # 没有关联信号
                }
                store_order(order_info)

                # 发送Bark通知
                bark_notifier.notify_trade_execution(order_info)

                return {
                    'success': True,
                    'ticket': order_ticket,
                    'price': entry_price,
                    'sl': sl,
                    'tp': tp,
                    'message': '直接市场下单成功'
                }
            else:
                logger.warning(f"下单成功但无法获取订单 {order_ticket} 的入场价格")
                return {
                    'success': True,
                    'ticket': order_ticket,
                    'message': '下单成功但无法获取入场价格'
                }
        else:
            error_msg = f"交易失败: {result.get('error', '未知错误')}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }

    except Exception as e:
        logger.error(f"直接市场下单时发生错误: {e}", exc_info=True)
        return {
            'success': False,
            'message': f"下单时发生错误: {str(e)}"
        }

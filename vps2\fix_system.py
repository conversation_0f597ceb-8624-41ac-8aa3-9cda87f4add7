#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5交易系统修复工具 - 一键修复常见问题
"""

import os
import sys
import time
import json
import logging
import subprocess
import psutil

# 创建日志目录
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    filename='logs/fix_system.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def print_header(text):
    """打印带格式的标题"""
    print("\n" + "=" * 50)
    print(f"  {text}")
    print("=" * 50 + "\n")

def check_and_fix_mt5_connection():
    """检查并修复MT5连接"""
    print_header("检查MT5连接")
    
    # 检查MT5是否运行
    mt5_running = False
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] in ['terminal64.exe', 'terminal.exe']:
            mt5_running = True
            print(f"✅ MT5已在运行 (PID: {proc.info['pid']})")
            break
    
    if not mt5_running:
        print("❌ MT5未运行。正在尝试启动MetaTrader 5...")
        try:
            # 尝试从配置文件获取MT5路径
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            mt5_path = config.get('mt5_path', '')
            if os.path.exists(mt5_path):
                subprocess.Popen(mt5_path)
                print(f"✅ 已启动MT5：{mt5_path}")
                time.sleep(5)  # 等待MT5启动
            else:
                print(f"❌ 配置的MT5路径不存在：{mt5_path}")
                print("请手动启动MetaTrader 5")
        except Exception as e:
            print(f"❌ 启动MT5时出错：{e}")
    
    # 尝试重置MT5连接
    print("\n正在重置MT5连接...")
    try:
        result = subprocess.run(['python', 'restart_mt5_connection.py'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ MT5连接重置成功")
        else:
            print(f"❌ MT5连接重置失败：{result.stderr}")
    except Exception as e:
        print(f"❌ 执行连接重置脚本失败：{e}")

def check_and_fix_signal_server():
    """检查并修复信号服务器"""
    print_header("检查信号服务器")
    
    # 检查信号服务是否运行
    signal_running = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    signal_running = True
                    print(f"✅ 信号服务已在运行 (PID: {proc.info['pid']})")
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if not signal_running:
        print("❌ 信号服务未运行。正在启动...")
        try:
            process = subprocess.Popen(
                ['python', 'signal_receiver.py'],
                stdout=open('logs/signal_receiver.out.log', 'a'),
                stderr=open('logs/signal_receiver.err.log', 'a'),
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            print(f"✅ 信号服务已启动 (PID: {process.pid})")
        except Exception as e:
            print(f"❌ 启动信号服务失败：{e}")

def restart_web_interface():
    """重启Web界面"""
    print_header("重启Web界面")
    
    # 关闭现有的Web界面进程
    web_pids = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'web_interface.py' in cmdline:
                    web_pids.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if web_pids:
        print(f"找到 {len(web_pids)} 个Web界面进程，正在关闭...")
        for pid in web_pids:
            try:
                os.kill(pid, 9)
                print(f"已关闭进程 PID: {pid}")
            except Exception as e:
                print(f"关闭进程 {pid} 失败：{e}")
    else:
        print("未找到运行中的Web界面进程")
    
    # 启动新的Web界面进程
    print("\n正在启动新的Web界面进程...")
    try:
        process = subprocess.Popen(
            ['python', 'web_interface.py'],
            stdout=open('logs/web_interface.out.log', 'a'),
            stderr=open('logs/web_interface.err.log', 'a'),
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        print(f"✅ Web界面已启动 (PID: {process.pid})")
    except Exception as e:
        print(f"❌ 启动Web界面失败：{e}")

def check_config_settings():
    """检查配置文件设置"""
    print_header("检查配置设置")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查交易开关
        trading_enabled = config.get('enable_trading', False)
        if trading_enabled:
            print("✅ 交易功能已启用")
        else:
            print("❌ 交易功能已禁用")
            
            # 询问是否启用
            answer = input("是否启用交易功能？(y/n): ")
            if answer.lower() == 'y':
                config['enable_trading'] = True
                with open('config.json', 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
                print("✅ 已启用交易功能")
        
        # 检查MT5路径
        mt5_path = config.get('mt5_path', '')
        if os.path.exists(mt5_path):
            print(f"✅ MT5路径有效：{mt5_path}")
        else:
            print(f"❌ MT5路径无效：{mt5_path}")
            
            # 尝试查找MT5路径
            common_paths = [
                "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
                "C:\\Program Files (x86)\\MetaTrader 5\\terminal.exe",
                "D:\\Program Files\\MetaTrader 5\\terminal64.exe"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    print(f"找到可能的MT5路径：{path}")
                    answer = input(f"是否使用此路径？(y/n): ")
                    if answer.lower() == 'y':
                        config['mt5_path'] = path
                        with open('config.json', 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=4, ensure_ascii=False)
                        print(f"✅ 已更新MT5路径为：{path}")
                        break
    except Exception as e:
        print(f"❌ 检查配置设置时出错：{e}")

def main():
    """主函数"""
    print_header("MT5交易系统修复工具")
    
    print("此工具将检查并修复以下问题：")
    print("1. MT5连接问题")
    print("2. 信号服务器问题")
    print("3. Web界面问题")
    print("4. 配置设置问题")
    print("\n注意：此过程会重启Web界面和相关服务")
    
    input("\n按回车键继续...")
    
    # 执行修复步骤
    check_config_settings()
    check_and_fix_mt5_connection()
    check_and_fix_signal_server()
    restart_web_interface()
    
    print_header("修复完成")
    print("系统修复已完成，请重新打开浏览器访问交易系统")
    print("如果问题仍然存在，请检查日志文件或联系技术支持")

if __name__ == "__main__":
    main()

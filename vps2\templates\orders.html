<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 订单管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 56px);
        }
        .sidebar a {
            color: #adb5bd;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
        }
        .sidebar a:hover, .sidebar a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar i {
            margin-right: 8px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-red {
            background-color: #dc3545;
        }
        .status-yellow {
            background-color: #ffc107;
        }
        .table {
            font-size: 0.8rem;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .pagination-container {
            margin-top: 20px;
        }
        .price-column {
            width: 80px;
        }
        .signal-level-column {
            width: 70px;
        }
        .status-column {
            width: 80px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">订单管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="me-2">
                            <button id="closeAllPositionsBtn" class="btn btn-danger me-2">
                                <i class="bi bi-x-circle-fill"></i> 平仓所有持仓
                            </button>
                            <button id="closeProfitablePositionsBtn" class="btn btn-success me-2">
                                <i class="bi bi-check-circle-fill"></i> 平仓盈利订单
                            </button>
                            <button id="syncClosedOrdersBtn" class="btn btn-info me-2">
                                <i class="bi bi-arrow-clockwise"></i> 同步已平仓订单
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <a href="{{ url_for('orders', status='all') }}" class="btn btn-sm btn-outline-secondary {% if status == 'all' %}active{% endif %}">全部</a>
                            <a href="{{ url_for('orders', status='open') }}" class="btn btn-sm btn-outline-secondary {% if status == 'open' %}active{% endif %}">活跃订单</a>
                            <a href="{{ url_for('orders', status='closed') }}" class="btn btn-sm btn-outline-secondary {% if status == 'closed' %}active{% endif %}">今日已平仓</a>
                            <a href="{{ url_for('orders', status='partially_closed') }}" class="btn btn-sm btn-outline-secondary {% if status == 'partially_closed' %}active{% endif %}">部分平仓</a>
                        </div>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 浮动盈亏控制面板 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up-arrow"></i> 浮动盈亏控制
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- 总浮动盈亏显示 -->
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">总浮动盈亏</h6>
                                        <h3 id="totalFloatingPL" class="mb-2">$0.00</h3>
                                        <small class="text-muted">实时更新</small>
                                    </div>
                                </div>
                            </div>

                            <!-- 总盈亏平仓规则 -->
                            <div class="col-md-4">
                                <h6 class="text-primary mb-3"><i class="bi bi-collection"></i> 总盈亏平仓规则</h6>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="totalRuleEnabled">
                                        <label class="form-check-label fw-bold" for="totalRuleEnabled">
                                            启用总盈亏自动平仓
                                        </label>
                                    </div>
                                </div>
                                <div id="totalRuleSettings">
                                    <div class="mb-2">
                                        <label for="totalProfitThreshold" class="form-label">盈利阈值 (美元)</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="totalProfitThreshold"
                                                   value="1000" step="10" min="0">
                                        </div>
                                        <small class="form-text text-muted">达到此盈利额时自动平仓所有订单</small>
                                    </div>
                                    <div class="mb-2">
                                        <label for="totalLossThreshold" class="form-label">亏损阈值 (美元)</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="totalLossThreshold"
                                                   value="500" step="10" min="0">
                                        </div>
                                        <small class="form-text text-muted">达到此亏损额时自动平仓所有订单</small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-primary btn-sm" id="saveTotalRuleBtn">
                                        <i class="bi bi-check2"></i> 保存总规则设置
                                    </button>
                                </div>
                            </div>

                            <!-- 全局止盈止损配置 -->
                            <div class="col-md-5">
                                <h6 class="text-success mb-3"><i class="bi bi-bullseye"></i> 全局止盈止损配置</h6>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="globalSLTPEnabled">
                                        <label class="form-check-label fw-bold" for="globalSLTPEnabled">
                                            启用全局止盈止损
                                        </label>
                                    </div>
                                </div>
                                <div id="globalSLTPSettings">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <label for="globalTakeProfit" class="form-label">止盈金额 (美元)</label>
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">$</span>
                                                    <input type="number" class="form-control" id="globalTakeProfit"
                                                           value="100" step="5" min="0">
                                                </div>
                                                <small class="form-text text-muted">每个订单达到此盈利时自动平仓</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <label for="globalStopLoss" class="form-label">止损金额 (美元)</label>
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">$</span>
                                                    <input type="number" class="form-control" id="globalStopLoss"
                                                           value="50" step="5" min="0">
                                                </div>
                                                <small class="form-text text-muted">每个订单达到此亏损时自动平仓</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="alert alert-info py-2 px-3 mt-2">
                                        <small>
                                            <i class="bi bi-info-circle"></i>
                                            <strong>说明：</strong>此配置对所有活跃订单生效，独立于单个订单的止盈止损设置
                                        </small>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-success btn-sm" id="saveGlobalSLTPBtn">
                                        <i class="bi bi-check2"></i> 保存全局配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>订单列表</div>
                        {% if status == 'closed' %}
                        <div class="small text-muted">显示今日已平仓订单</div>
                        {% elif status == 'open' %}
                        <div class="small text-muted">显示当前活跃订单</div>
                        {% elif status == 'all' %}
                        <div class="small text-muted">显示所有订单（活跃+今日已平仓）</div>
                        {% endif %}
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th scope="col">订单号</th>
                                        <th scope="col">交易对</th>
                                        <th scope="col">方向</th>
                                        <th scope="col">交易量</th>
                                        <th scope="col" class="price-column">开仓价</th>
                                        <th scope="col" class="price-column">当前价</th>
                                        <th scope="col" class="status-column">状态</th>
                                        <th scope="col" class="price-column">止损</th>
                                        <th scope="col" class="price-column">止盈</th>
                                        <th scope="col" class="signal-level-column">S2</th>
                                        <th scope="col" class="signal-level-column">R2</th>
                                        <th scope="col">盈亏</th>
                                        <th scope="col">隔夜利息</th>
                                        <th scope="col">持仓时间</th>
                                        <th scope="col">平仓原因</th>
                                        <th scope="col">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if orders %}
                                        {% for order in orders %}
                                            <tr class="{% if order['status'] == 'closed' %}table-light{% endif %}">
                                                <td>{{ order['ticket'] }}</td>
                                                <td>{{ order['trading_pair'] }}</td>
                                                <td>
                                                    {% if order['operation'] == 'buy' %}
                                                        <span class="badge bg-success">买入</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">卖出</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ order['volume'] }}</td>
                                                <td>{{ order['price'] }}</td>
                                                <td>{{ order['current_price'] if order['current_price'] else '-' }}</td>
                                                <td>
                                                    {% if order['status'] == 'open' %}
                                                        <span class="badge bg-success">持仓中</span>
                                                    {% elif order['status'] == 'closed' %}
                                                        <span class="badge bg-secondary">已平仓</span> 
                                                        <small><i class="bi bi-check-circle-fill text-secondary"></i></small>
                                                    {% elif order['status'] == 'partially_closed' %}
                                                        <span class="badge bg-warning">部分平仓</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ order['sl'] if order['sl'] else '-' }}</td>
                                                <td>{{ order['tp'] if order['tp'] else '-' }}</td>
                                                <td>{{ "%.2f"|format(order['signal_s2']|float) if order.get('signal_s2') else '-' }}</td>
                                                <td>{{ "%.2f"|format(order['signal_r2']|float) if order.get('signal_r2') else '-' }}</td>
                                                <td class="{% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% endif %} fw-bold">
                                                    {{ "%.2f"|format(order['profit']|float) }} {% if order['status'] == 'closed' %}{% if order['profit'] > 0 %}✓{% elif order['profit'] < 0 %}✗{% endif %}{% endif %}
                                                </td>
                                                <td class="{% if order['swap'] > 0 %}text-success{% elif order['swap'] < 0 %}text-danger{% endif %}">
                                                    {{ "%.2f"|format(order['swap']|float) if order['swap'] is not none else '-' }}
                                                </td>
                                                <td>{% if order['time_formatted'] %}{{ order['time_formatted'] }}<br><small>{{ order['duration'] }}</small>{% else %}{{ order['timestamp'].replace('T', ' ').split('.')[0] if order['timestamp'] else '-' }}{% endif %}</td>
                                                <td>
                                                    {% if order['status'] == 'closed' and order.get('reason') %}
                                                        <span class="badge bg-info">{{ order['reason_str'] if order.get('reason_str') else order['reason'] }}</span>
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if order['id'] is not none %}
                                                        <a href="{{ url_for('order_detail', order_id=order['id']) }}" class="btn btn-sm btn-outline-primary">详情</a>
                                                    {% else %}
                                                        <a href="{{ url_for('position_detail', ticket=order['ticket']) }}" class="btn btn-sm btn-outline-primary">详情</a>
                                                    {% endif %}
                                                    {% if order['status'] == 'open' or order['status'] == 'partially_closed' %}
                                                        <button class="btn btn-sm btn-outline-danger close-position-btn" 
                                                                data-ticket="{{ order['ticket'] }}" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#closePositionModal">
                                                            平仓
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning modify-sl-tp-btn" 
                                                                data-ticket="{{ order['ticket'] }}"
                                                                data-sl="{{ order['sl'] }}"
                                                                data-tp="{{ order['tp'] }}"
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#modifySLTPModal">
                                                            修改
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info profit-loss-rule-btn" 
                                                                data-ticket="{{ order['ticket'] }}"
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#profitLossRuleModal">
                                                            <i class="bi bi-graph-up"></i> 盈亏
                                                        </button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="16" class="text-center py-3">暂无订单数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                {% if total_pages > 1 %}
                <div class="d-flex justify-content-center pagination-container">
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item {% if page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('orders', page=page-1, status=status) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            {% for p in range(1, total_pages + 1) %}
                                {% if p == page %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ p }}</a></li>
                                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                                    <li class="page-item"><a class="page-link" href="{{ url_for('orders', page=p, status=status) }}">{{ p }}</a></li>
                                {% elif p == 4 and page > 5 %}
                                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                {% elif p == total_pages - 3 and page < total_pages - 4 %}
                                    <li class="page-item disabled"><a class="page-link" href="#">...</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('orders', page=page+1, status=status) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </main>
        </div>
    </div>

    <!-- 平仓确认弹窗 -->
    <div class="modal fade" id="closePositionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认平仓</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要平仓订单 <span id="closePositionTicket" class="fw-bold"></span> 吗？</p>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="partialCloseCheck">
                        <label class="form-check-label" for="partialCloseCheck">
                            部分平仓
                        </label>
                    </div>
                    <div id="partialVolumeGroup" class="mb-3" style="display: none;">
                        <label for="partialVolume" class="form-label">平仓量</label>
                        <input type="number" class="form-control" id="partialVolume" step="0.01" min="0.01">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmCloseBtn">确认平仓</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改止损止盈弹窗 -->
    <div class="modal fade" id="modifySLTPModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">修改止损止盈</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>修改订单 <span id="modifyTicket" class="fw-bold"></span> 的止损止盈：</p>
                    <div class="mb-3">
                        <label for="newSL" class="form-label">止损价格</label>
                        <input type="number" class="form-control" id="newSL" step="0.00001">
                        <small class="form-text text-muted">留空表示不修改</small>
                    </div>
                    <div class="mb-3">
                        <label for="newTP" class="form-label">止盈价格</label>
                        <input type="number" class="form-control" id="newTP" step="0.00001">
                        <small class="form-text text-muted">留空表示不修改</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmModifyBtn">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 单个订单浮动盈亏设置弹窗 -->
    <div class="modal fade" id="profitLossRuleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设置订单浮动盈亏规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>为订单 <span id="ruleTicket" class="fw-bold"></span> 设置浮动盈亏自动平仓规则：</p>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="individualRuleEnabled">
                            <label class="form-check-label fw-bold" for="individualRuleEnabled">
                                启用此订单的自动平仓规则
                            </label>
                        </div>
                    </div>
                    
                    <div id="individualRuleSettings">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="individualProfitThreshold" class="form-label">盈利阈值 (美元)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="individualProfitThreshold" 
                                           value="100" step="10" min="0">
                                </div>
                                <small class="form-text text-muted">达到此盈利额时自动平仓此订单</small>
                            </div>
                            <div class="col-md-6">
                                <label for="individualLossThreshold" class="form-label">亏损阈值 (美元)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="individualLossThreshold" 
                                           value="50" step="10" min="0">
                                </div>
                                <small class="form-text text-muted">达到此亏损额时自动平仓此订单</small>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i>
                            <strong>注意：</strong>
                            <ul class="mb-0 mt-2">
                                <li>规则会实时监控此订单的浮动盈亏</li>
                                <li>达到设定条件时会自动发送平仓命令给MT5</li>
                                <li>可以随时修改或关闭规则</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmIndividualRuleBtn">保存规则</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 自动刷新活跃订单数据
            function updateOpenPositions() {
                $.ajax({
                    url: '/api/positions',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.positions) {
                            // 更新每个活跃订单的数据
                            response.positions.forEach(function(position) {
                                // 使用票号精确查找匹配的行
                                const ticketCell = $('td').filter(function() {
                                    return $(this).text() == position.ticket;
                                });
                                
                                if (ticketCell.length) {
                                    const row = ticketCell.parent('tr');
                                    
                                    // 更新当前价格
                                    row.find('td:eq(5)').text(position.price_current);
                                    
                                    // 更新盈亏 - 修正列索引，盈亏是第12列（索引11）
                                    let profitCell = row.find('td:eq(11)');
                                    profitCell.text(parseFloat(position.profit).toFixed(2));
                                    profitCell.removeClass('text-success text-danger');
                                    if (position.profit > 0) {
                                        profitCell.addClass('text-success fw-bold');
                                    } else if (position.profit < 0) {
                                        profitCell.addClass('text-danger fw-bold');
                                    }

                                    // 更新隔夜利息 - 修正列索引，隔夜利息是第13列（索引12）
                                    let swapCell = row.find('td:eq(12)');
                                    swapCell.text(parseFloat(position.swap).toFixed(2));
                                    swapCell.removeClass('text-success text-danger');
                                    if (position.swap > 0) {
                                        swapCell.addClass('text-success');
                                    } else if (position.swap < 0) {
                                        swapCell.addClass('text-danger');
                                    }

                                    // 更新持仓时间 - 修正列索引，持仓时间是第14列（索引13）
                                    row.find('td:eq(13) small').text(position.duration);
                                }
                            });
                        }
                    }
                });
            }
            
            // 每3秒更新一次数据
            setInterval(updateOpenPositions, 3000);
            
            // 页面加载完成立即执行一次
            updateOpenPositions();
            
            // 平仓所有持仓按钮点击事件
            $('#closeAllPositionsBtn').click(function() {
                if (confirm('确定要平仓所有持仓吗？此操作将关闭所有交易对的所有持仓，且不可撤销。')) {
                    $.ajax({
                        url: '/close_all_positions',
                        type: 'POST',
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                alert('平仓操作已成功执行: ' + response.message);
                                // 重新加载页面以显示最新状态
                                location.reload();
                            } else {
                                alert('平仓操作失败: ' + response.message);
                            }
                        },
                        error: function() {
                            alert('平仓请求失败，请稍后重试');
                        }
                    });
                }
            });

            // 平仓盈利订单按钮点击事件
            $('#closeProfitablePositionsBtn').click(function() {
                if (confirm('确定要平仓所有盈利订单吗？此操作将关闭所有当前盈利的持仓，锁定盈利。')) {
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // 添加加载状态
                    $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 处理中...');

                    $.ajax({
                        url: '/close_profitable_positions',
                        type: 'POST',
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                let message = response.message;
                                if (response.total_profit) {
                                    message += ` (总盈利: $${response.total_profit.toFixed(2)})`;
                                }
                                alert('盈利订单平仓操作已成功执行: ' + message);
                                // 重新加载页面以显示最新状态
                                location.reload();
                            } else {
                                alert('盈利订单平仓操作失败: ' + response.message);
                                $btn.prop('disabled', false).html(originalText);
                            }
                        },
                        error: function() {
                            alert('盈利订单平仓请求失败，请稍后重试');
                            $btn.prop('disabled', false).html(originalText);
                        }
                    });
                }
            });

            // 同步已平仓订单按钮点击事件
            $('#syncClosedOrdersBtn').click(function() {
                const $btn = $(this);
                const originalText = $btn.html();

                // 添加加载状态
                $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 同步中...');

                $.ajax({
                    url: '/sync_closed_orders',
                    type: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert('已平仓订单同步成功: ' + response.message);
                            // 重新加载页面以显示最新状态
                            location.reload();
                        } else {
                            alert('同步失败: ' + response.message);
                        }
                        $btn.prop('disabled', false).html(originalText);
                    },
                    error: function() {
                        alert('同步请求失败，请稍后重试');
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            });
            
            // 平仓按钮点击事件
            $('.close-position-btn').click(function() {
                const ticket = $(this).data('ticket');
                $('#closePositionTicket').text(ticket);
            });

            // 部分平仓选项
            $('#partialCloseCheck').change(function() {
                if($(this).is(':checked')) {
                    $('#partialVolumeGroup').show();
                } else {
                    $('#partialVolumeGroup').hide();
                }
            });

            // 确认平仓事件
            $('#confirmCloseBtn').click(function() {
                const ticket = $('#closePositionTicket').text();
                let data = {
                    ticket: ticket
                };
                
                if($('#partialCloseCheck').is(':checked')) {
                    const volume = $('#partialVolume').val();
                    if(volume && volume > 0) {
                        data.volume = volume;
                    } else {
                        alert('请输入有效的平仓量');
                        return;
                    }
                }
                
                $.ajax({
                    url: '/close_position',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if(response.success) {
                            alert('平仓成功');
                            location.reload();
                        } else {
                            alert('平仓失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#closePositionModal').modal('hide');
                    }
                });
            });

            // 修改止损止盈按钮点击事件
            $('.modify-sl-tp-btn').click(function() {
                const ticket = $(this).data('ticket');
                const sl = $(this).data('sl');
                const tp = $(this).data('tp');
                
                $('#modifyTicket').text(ticket);
                $('#newSL').val(sl);
                $('#newTP').val(tp);
            });

            // 确认修改止损止盈事件
            $('#confirmModifyBtn').click(function() {
                const ticket = $('#modifyTicket').text();
                const sl = $('#newSL').val();
                const tp = $('#newTP').val();
                
                $.ajax({
                    url: '/modify_sl_tp',
                    type: 'POST',
                    data: {
                        ticket: ticket,
                        sl: sl,
                        tp: tp
                    },
                    success: function(response) {
                        if(response.success) {
                            alert('修改成功');
                            location.reload();
                        } else {
                            alert('修改失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#modifySLTPModal').modal('hide');
                    }
                });
            });

            // 浮动盈亏功能
            // 获取总浮动盈亏
            function updateTotalFloatingPL() {
                $.ajax({
                    url: '/api/total_floating_pl',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const totalPL = response.total_floating_pl;
                            const formatted = (totalPL >= 0 ? '+' : '') + totalPL.toFixed(2);
                            $('#totalFloatingPL').text('$' + formatted);
                            
                            // 设置颜色
                            $('#totalFloatingPL').removeClass('text-success text-danger');
                            if (totalPL > 0) {
                                $('#totalFloatingPL').addClass('text-success');
                            } else if (totalPL < 0) {
                                $('#totalFloatingPL').addClass('text-danger');
                            }
                        }
                    }
                });
            }

            // 加载总规则设置
            function loadTotalRule() {
                $.ajax({
                    url: '/api/profit_loss_rules/total',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.rule) {
                            $('#totalRuleEnabled').prop('checked', response.rule.enabled);
                            $('#totalProfitThreshold').val(response.rule.profit_threshold);
                            $('#totalLossThreshold').val(Math.abs(response.rule.loss_threshold));
                            
                            // 控制设置区域显示/隐藏
                            toggleTotalRuleSettings();
                        }
                    }
                });
            }

            // 控制总规则设置区域显示/隐藏
            function toggleTotalRuleSettings() {
                if ($('#totalRuleEnabled').is(':checked')) {
                    $('#totalRuleSettings').show();
                } else {
                    $('#totalRuleSettings').hide();
                }
            }

            // 总规则开关事件
            $('#totalRuleEnabled').change(function() {
                toggleTotalRuleSettings();
            });

            // 保存总规则设置
            $('#saveTotalRuleBtn').click(function() {
                const enabled = $('#totalRuleEnabled').is(':checked') ? 1 : 0;
                const profitThreshold = parseFloat($('#totalProfitThreshold').val());
                const lossThreshold = -Math.abs(parseFloat($('#totalLossThreshold').val()));

                $.ajax({
                    url: '/api/profit_loss_rules/total',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        enabled: enabled,
                        profit_threshold: profitThreshold,
                        loss_threshold: lossThreshold
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('总规则设置已保存');
                        } else {
                            alert('保存失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('请求失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));
                    }
                });
            });

            // 单个订单浮动盈亏规则按钮点击事件
            $('.profit-loss-rule-btn').click(function() {
                const ticket = $(this).data('ticket');
                $('#ruleTicket').text(ticket);
                
                // 加载现有规则设置
                $.ajax({
                    url: '/api/profit_loss_rules/individual/' + ticket,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.rule) {
                            $('#individualRuleEnabled').prop('checked', response.rule.enabled);
                            $('#individualProfitThreshold').val(response.rule.profit_threshold);
                            $('#individualLossThreshold').val(Math.abs(response.rule.loss_threshold));
                        } else {
                            // 设置默认值
                            $('#individualRuleEnabled').prop('checked', false);
                            $('#individualProfitThreshold').val(100);
                            $('#individualLossThreshold').val(50);
                        }
                        toggleIndividualRuleSettings();
                    }
                });
            });

            // 控制单个规则设置区域显示/隐藏
            function toggleIndividualRuleSettings() {
                if ($('#individualRuleEnabled').is(':checked')) {
                    $('#individualRuleSettings').show();
                } else {
                    $('#individualRuleSettings').hide();
                }
            }

            // 单个规则开关事件
            $('#individualRuleEnabled').change(function() {
                toggleIndividualRuleSettings();
            });

            // 保存单个订单规则设置
            $('#confirmIndividualRuleBtn').click(function() {
                const ticket = parseInt($('#ruleTicket').text());
                const enabled = $('#individualRuleEnabled').is(':checked') ? 1 : 0;
                const profitThreshold = parseFloat($('#individualProfitThreshold').val());
                const lossThreshold = -Math.abs(parseFloat($('#individualLossThreshold').val()));

                $.ajax({
                    url: '/api/profit_loss_rules/individual',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        ticket: ticket,
                        enabled: enabled,
                        profit_threshold: profitThreshold,
                        loss_threshold: lossThreshold
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('订单规则设置已保存');
                            $('#profitLossRuleModal').modal('hide');
                        } else {
                            alert('保存失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('请求失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));
                    }
                });
            });

            // 加载全局止盈止损配置
            function loadGlobalSLTP() {
                $.ajax({
                    url: '/api/profit_loss_rules/global_sl_tp',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.rule) {
                            $('#globalSLTPEnabled').prop('checked', response.rule.enabled);
                            $('#globalTakeProfit').val(response.rule.profit_threshold);
                            $('#globalStopLoss').val(Math.abs(response.rule.loss_threshold));

                            // 控制设置区域显示/隐藏
                            toggleGlobalSLTPSettings();
                        }
                    }
                });
            }

            // 控制全局止盈止损设置区域显示/隐藏
            function toggleGlobalSLTPSettings() {
                if ($('#globalSLTPEnabled').is(':checked')) {
                    $('#globalSLTPSettings').show();
                } else {
                    $('#globalSLTPSettings').hide();
                }
            }

            // 全局止盈止损开关事件
            $('#globalSLTPEnabled').change(function() {
                toggleGlobalSLTPSettings();
            });

            // 保存全局止盈止损配置
            $('#saveGlobalSLTPBtn').click(function() {
                const enabled = $('#globalSLTPEnabled').is(':checked') ? 1 : 0;
                const profitThreshold = parseFloat($('#globalTakeProfit').val());
                const lossThreshold = -Math.abs(parseFloat($('#globalStopLoss').val()));

                $.ajax({
                    url: '/api/profit_loss_rules/global_sl_tp',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        enabled: enabled,
                        profit_threshold: profitThreshold,
                        loss_threshold: lossThreshold
                    }),
                    success: function(response) {
                        if (response.success) {
                            alert('全局止盈止损配置已保存');
                        } else {
                            alert('保存失败: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('请求失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));
                    }
                });
            });

            // 初始化浮动盈亏功能
            updateTotalFloatingPL();
            loadTotalRule();
            loadGlobalSLTP();

            // 定时更新总浮动盈亏
            setInterval(updateTotalFloatingPL, 5000);
        });
    </script>
</body>
</html>

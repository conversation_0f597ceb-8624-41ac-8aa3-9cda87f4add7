#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
升级数据库，为signals表添加新字段以支持详细的信号处理信息
"""

import sqlite3
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def upgrade_signals_table():
    """为signals表添加新字段"""
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        c = conn.cursor()
        
        logger.info("开始升级signals表...")
        
        # 要添加的新字段
        new_fields = [
            ('webhook_content', 'TEXT'),
            ('process_status', 'TEXT DEFAULT "pending"'),
            ('failure_reason', 'TEXT'),
            ('success_details', 'TEXT')
        ]
        
        for field_name, field_type in new_fields:
            try:
                # 检查字段是否已存在
                c.execute(f"SELECT {field_name} FROM signals LIMIT 1")
                logger.info(f"字段 {field_name} 已存在")
            except sqlite3.OperationalError:
                # 字段不存在，添加它
                try:
                    c.execute(f"ALTER TABLE signals ADD COLUMN {field_name} {field_type}")
                    logger.info(f"✅ 成功添加字段 {field_name}")
                except sqlite3.OperationalError as e:
                    logger.warning(f"❌ 添加字段 {field_name} 失败: {e}")
        
        # 为现有记录设置默认的process_status
        c.execute("""
            UPDATE signals 
            SET process_status = CASE 
                WHEN processed = 1 AND order_result = '成功' THEN 'success'
                WHEN processed = 1 AND order_result != '成功' THEN 'failed'
                WHEN processed = 1 THEN 'success'
                ELSE 'pending'
            END
            WHERE process_status IS NULL
        """)
        
        # 提交更改
        conn.commit()
        
        # 验证表结构
        logger.info("\n验证更新后的signals表结构:")
        c.execute("PRAGMA table_info(signals)")
        columns = c.fetchall()
        
        for column in columns:
            column_name = column[1]
            column_type = column[2]
            logger.info(f"  {column_name:20} : {column_type}")
        
        conn.close()
        logger.info("\n🎉 signals表升级完成!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 升级signals表时发生错误: {e}")
        return False

if __name__ == '__main__':
    # 检查数据库文件是否存在
    if not os.path.exists('trading_data.db'):
        logger.error("❌ 数据库文件 trading_data.db 不存在")
        logger.info("请先运行 signal_receiver.py 来创建数据库")
        exit(1)
    
    # 升级数据库
    success = upgrade_signals_table()
    
    if success:
        logger.info("✅ 数据库升级完成!")
    else:
        logger.error("❌ 数据库升级失败!")
        exit(1)

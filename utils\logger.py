"""
日志管理模块
"""
import os
import logging
import colorlog
from datetime import datetime
from config import Config

class Logger:
    """日志管理器"""
    
    def __init__(self, name="TradingSystem", log_file=None):
        self.name = name
        self.log_file = log_file or Config.LOG_FILE
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建日志目录
        log_dir = os.path.dirname(self.log_file)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 控制台处理器（彩色输出）
        console_handler = colorlog.StreamHandler()
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def debug(self, message):
        """调试日志"""
        self.logger.debug(message)
    
    def info(self, message):
        """信息日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """警告日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """错误日志"""
        self.logger.error(message)
    
    def critical(self, message):
        """严重错误日志"""
        self.logger.critical(message)
    
    def log_trade_action(self, action, symbol, direction=None, lot_size=None, price=None, **kwargs):
        """记录交易操作日志"""
        log_data = {
            'action': action,
            'symbol': symbol,
            'timestamp': datetime.now().isoformat()
        }
        
        if direction:
            log_data['direction'] = direction
        if lot_size:
            log_data['lot_size'] = lot_size
        if price:
            log_data['price'] = price
        
        log_data.update(kwargs)
        
        self.info(f"交易操作: {log_data}")
    
    def log_alert_received(self, alert_data):
        """记录接收到的警报"""
        self.info(f"收到TradingView警报: {alert_data}")
    
    def log_notification_sent(self, notification_type, success, error=None):
        """记录通知发送结果"""
        if success:
            self.info(f"通知发送成功: {notification_type}")
        else:
            self.error(f"通知发送失败: {notification_type}, 错误: {error}")

# 创建全局日志器实例
logger = Logger()

# 导出常用方法
debug = logger.debug
info = logger.info
warning = logger.warning
error = logger.error
critical = logger.critical
log_trade_action = logger.log_trade_action
log_alert_received = logger.log_alert_received
log_notification_sent = logger.log_notification_sent

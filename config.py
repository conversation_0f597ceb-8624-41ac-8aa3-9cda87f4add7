"""
MetaTrader5 交易管理系统配置文件
"""
import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 应用配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # 数据库配置
    DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'trading_system.db')
    
    # MT5配置
    MT5_LOGIN = int(os.environ.get('MT5_LOGIN', 0))
    MT5_PASSWORD = os.environ.get('MT5_PASSWORD', '')
    MT5_SERVER = os.environ.get('MT5_SERVER', '')
    MT5_PATH = os.environ.get('MT5_PATH', '')
    
    # Flask配置
    FLASK_HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
    FLASK_PORT = int(os.environ.get('FLASK_PORT', 5000))
    FLASK_DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 会话配置
    SESSION_TIMEOUT = timedelta(hours=24)
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = timedelta(minutes=30)
    
    # Bark通知配置
    BARK_DEVICE_1_KEY = os.environ.get('BARK_DEVICE_1_KEY', '')
    BARK_DEVICE_2_KEY = os.environ.get('BARK_DEVICE_2_KEY', '')
    BARK_BASE_URL = 'https://api.day.app'
    
    # 交易配置
    DEFAULT_TRADING_COOLDOWN = 300  # 5分钟
    MAX_CONCURRENT_TRADES = 5
    MAX_DAILY_LOSS_USD = 500.0
    MAX_DAILY_PROFIT_USD = 1000.0
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.path.join(os.path.dirname(__file__), 'logs', 'trading_system.log')
    
    # 支持的交易对
    SUPPORTED_SYMBOLS = [
        "XAUUSD", "ETHUSD", "BTCUSD", "SOLUSD", "BCHUSD",
        "ADAUSD", "XLMUSD", "DOGEUSD", "LINKUSD", "LTCUSD",
        "XRPUSD", "GBPUSD", "GBPJPY"
    ]
    
    # 交易对映射配置
    SYMBOL_MAPPING = {
        # 加密货币映射 (统一为USD后缀)
        "ETHUSD": ["ETHUSD", "ETHUSDT", "ETH-USD", "ETH/USD", "ETH_USD"],
        "BTCUSD": ["BTCUSD", "BTCUSDT", "BTC-USD", "BTC/USD", "BTC_USD"],
        "SOLUSD": ["SOLUSD", "SOLUSDT", "SOL-USD", "SOL/USD", "SOL_USD"],
        "BCHUSD": ["BCHUSD", "BCHUSDT", "BCH-USD", "BCH/USD", "BCH_USD"],
        "ADAUSD": ["ADAUSD", "ADAUSDT", "ADA-USD", "ADA/USD", "ADA_USD"],
        "XLMUSD": ["XLMUSD", "XLMUSDT", "XLM-USD", "XLM/USD", "XLM_USD"],
        "DOGEUSD": ["DOGEUSD", "DOGEUSDT", "DOGE-USD", "DOGE/USD", "DOGE_USD"],
        "LINKUSD": ["LINKUSD", "LINKUSDT", "LINK-USD", "LINK/USD", "LINK_USD"],
        "LTCUSD": ["LTCUSD", "LTCUSDT", "LTC-USD", "LTC/USD", "LTC_USD"],
        "XRPUSD": ["XRPUSD", "XRPUSDT", "XRP-USD", "XRP/USD", "XRP_USD"],
        
        # 贵金属映射
        "XAUUSD": ["XAUUSD", "XAU-USD", "XAU/USD", "GOLD", "GOLDUSD"],
        
        # 外汇映射
        "GBPUSD": ["GBPUSD", "GBP-USD", "GBP/USD", "GBP_USD"],
        "GBPJPY": ["GBPJPY", "GBP-JPY", "GBP/JPY", "GBP_JPY"],
    }
    
    # 信号方向映射表
    SIGNAL_DIRECTION_MAPPING = {
        # 做多信号映射 - 大写
        "BUY": "BUY",
        "LONG": "BUY",
        "UP": "BUY",
        
        # 做多信号映射 - 小写
        "buy": "BUY",
        "long": "BUY",
        "up": "BUY",
        
        # 做多信号映射 - 中文
        "买入": "BUY",
        "做多": "BUY",
        "综合做多信号": "BUY",
        "多": "BUY",
        
        # 做空信号映射 - 大写
        "SELL": "SELL",
        "SHORT": "SELL",
        "DOWN": "SELL",
        
        # 做空信号映射 - 小写
        "sell": "SELL",
        "short": "SELL",
        "down": "SELL",
        
        # 做空信号映射 - 中文
        "卖出": "SELL",
        "做空": "SELL",
        "综合做空信号": "SELL",
        "空": "SELL"
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_DEBUG = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DATABASE_PATH = ':memory:'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

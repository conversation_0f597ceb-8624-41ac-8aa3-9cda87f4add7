@echo off
title TradingView Trading System

echo ================================
echo   TradingView Trading System
echo ================================
echo.

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found
    echo Please install Python 3.8+ from https://www.python.org/downloads/
    pause
    exit /b 1
)

:: Check app.py
if not exist "app.py" (
    echo Error: app.py not found
    echo Please run this script in the project directory
    pause
    exit /b 1
)

echo Python found:
python --version
echo Project directory: %CD%
echo.

:: Auto install dependencies
echo Installing/updating dependencies...
python -m pip install --upgrade pip >nul 2>&1

if exist "requirements.txt" (
    echo Installing from requirements.txt...
    pip install -r requirements.txt
) else (
    echo Installing core packages...
    pip install flask flask-cors requests schedule MetaTrader5
)

echo.
echo Dependencies installation complete
echo.

:: Start system
echo Starting TradingView Trading System...
echo.
echo Web Interface: http://localhost:5000
echo Webhook URL: http://localhost:7000/webhook/tradingview
echo.
echo Press Ctrl+C to stop the system
echo ================================
echo.

python app.py

echo.
echo System stopped
pause

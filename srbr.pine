// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ChartPrime

//@version=6
indicator('Support and Resistance alert', shorttitle = 'SRBR alert ', overlay = true, max_boxes_count = 50)


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙐𝙎𝙀𝙍 𝙄𝙉𝙋𝙐𝙏𝙎
// ---------------------------------------------------------------------------------------------------------------------{
int lookbackPeriod = input.int(20, 'Lookback Period', minval = 1, group = 'Settings')
int vol_len = input.int(2, 'Delta Volume Filter Length', tooltip = 'Higher input, will filter low volume boxes', group = 'Settings')
float box_withd = input.float(1, 'Adjust Box Width', maxval = 1000, minval = 0, step = 0.1)


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙄𝙉𝘿𝙄𝘾𝘼𝙏𝙊𝙍 𝘾𝘼𝙇𝘾𝙐𝙇𝘼𝙏𝙄𝙊𝙉𝙎
// ---------------------------------------------------------------------------------------------------------------------{

// ═════════ 价格状态判断函数 ════════
get_price_status(price_val, support_val, resistance_val) =>
    if na(price_val) or na(support_val) or na(resistance_val)
        "N/A"
    else if price_val > resistance_val
        "看多"
    else if price_val < support_val
        "看空"
    else
        "震荡"


// Delta Volume Function
upAndDownVolume() =>
    posVol = 0.0
    negVol = 0.0

    var isBuyVolume = true

    switch 
        close > open => 
    	    isBuyVolume := true
    	    isBuyVolume
        close < open => 
    	    isBuyVolume := false
    	    isBuyVolume

    if isBuyVolume
        posVol := posVol + volume
        posVol
    else
        negVol := negVol - volume
        negVol

    posVol + negVol


// Function to identify support and resistance boxes
calcSupportResistance(src, lookbackPeriod) =>
    // Volume
    Vol = upAndDownVolume()
    vol_hi = ta.highest(Vol / 2.5, vol_len)
    vol_lo = ta.lowest(Vol / 2.5, vol_len)

    var float supportLevel = na
    var float supportLevel_1 = na
    var float resistanceLevel = na
    var float resistanceLevel_1 = na
    var box sup = na
    var box res = na
    var color res_color = na
    var color sup_color = na
    var float multi = na

    var bool brekout_res = false
    var bool brekout_sup = false
    var bool res_holds = false
    var bool sup_holds = false

    // Find pivot points
    pivotHigh = ta.pivothigh(src, lookbackPeriod, lookbackPeriod)
    pivotLow = ta.pivotlow(src, lookbackPeriod, lookbackPeriod)
    // Box width
    atr = ta.atr(200)
    withd = atr * box_withd

    // Volume range for color gradient
    vol_highest = ta.highest(Vol, 25)
    vol_lowest = ta.lowest(Vol, 25)

    // Find support levels with Positive Volume
    if not na(pivotLow) and Vol > vol_hi

        supportLevel := pivotLow
        supportLevel_1 := supportLevel - withd

        topLeft = chart.point.from_index(bar_index - lookbackPeriod, supportLevel)
        bottomRight = chart.point.from_index(bar_index, supportLevel_1)

        sup_color := color.from_gradient(Vol, 0, vol_highest, color(na), color.new(color.green, 30))

        sup := box.new(top_left = topLeft, bottom_right = bottomRight, border_color = color.green, border_width = 1, bgcolor = sup_color, text = 'Vol: ' + str.tostring(math.round(Vol, 2)), text_color = chart.fg_color, text_size = size.small)
        sup


    // Find resistance levels with Negative Volume
    if not na(pivotHigh) and Vol < vol_lo

        resistanceLevel := pivotHigh
        resistanceLevel_1 := resistanceLevel + withd

        topLeft = chart.point.from_index(bar_index - lookbackPeriod, resistanceLevel)
        bottomRight = chart.point.from_index(bar_index, resistanceLevel_1)

        res_color := color.from_gradient(Vol, vol_lowest, 0, color.new(color.red, 30), color(na))

        res := box.new(top_left = topLeft, bottom_right = bottomRight, border_color = color.red, border_width = 1, bgcolor = res_color, text = 'Vol: ' + str.tostring(math.round(Vol, 2)), text_color = chart.fg_color, text_size = size.small)
        res

    // Adaptive Box Len
    sup.set_right(bar_index + 1)
    res.set_right(bar_index + 1)

    // Break of support or resistance conditions
    brekout_res := ta.crossover(low, resistanceLevel_1)
    res_holds := ta.crossunder(high, resistanceLevel)

    sup_holds := ta.crossover(low, supportLevel)
    brekout_sup := ta.crossunder(high, supportLevel_1)

    // Change Color of Support to red if it was break, change color of resistance to green if it was break
    if brekout_sup
        sup.set_bgcolor(color.new(color.red, 80))
        sup.set_border_color(color.red)
        sup.set_border_style(line.style_dashed)

    if sup_holds
        sup.set_bgcolor(sup_color)
        sup.set_border_color(color.green)
        sup.set_border_style(line.style_solid)

    if brekout_res
        res.set_bgcolor(color.new(color.green, 80))
        res.set_border_color(color.new(color.green, 0))
        res.set_border_style(line.style_dashed)

    if res_holds
        res.set_bgcolor(res_color)
        res.set_border_color(color.new(color.red, 0))
        res.set_border_style(line.style_solid)

    [supportLevel, resistanceLevel, brekout_res, res_holds, sup_holds, brekout_sup]


// Calculate support and resistance levels and their breakouts
[supportLevel, resistanceLevel, brekout_res, res_holds, sup_holds, brekout_sup] = calcSupportResistance(close, lookbackPeriod)


// Check if Resistance become Support or Support Become Resistance
var bool res_is_sup = false
var bool sup_is_res = false

switch 
    brekout_res => 
	    res_is_sup := true
	    res_is_sup
    res_holds => 
	    res_is_sup := false
	    res_is_sup

switch 
    brekout_sup => 
	    sup_is_res := true
	    sup_is_res
    sup_holds => 
	    sup_is_res := false
	    sup_is_res


// ---------------------------------------------------------------------------------------------------------------------}
// 𝙑𝙄𝙎𝙐𝘼𝙇𝙄𝙕𝘼𝙏𝙄𝙊𝙉
// ---------------------------------------------------------------------------------------------------------------------{
// Plot Res and Sup breakouts and holds 
plotchar(res_holds, 'Resistance Holds', '◆', color = #e92929, size = size.tiny, location = location.abovebar, offset = -1)
plotchar(sup_holds, 'Support Holds', '◆', color = #20ca26, size = size.tiny, location = location.belowbar, offset = -1)

plotchar(brekout_res and res_is_sup[1], 'Resistance as Support Holds', '◆', color = #20ca26, size = size.tiny, location = location.belowbar, offset = -1)
plotchar(brekout_sup and sup_is_res[1], 'Support as Resistance Holds', '◆', color = #e92929, size = size.tiny, location = location.abovebar, offset = -1)

// Break Out Labels
if brekout_sup and not sup_is_res[1]
    label.new(bar_index[1], supportLevel[1], text = 'Break Sup', style = label.style_label_down, color = #7e1e1e, textcolor = chart.fg_color, size = size.small)

if brekout_res and not res_is_sup[1]
    label.new(bar_index[1], resistanceLevel[1], text = 'Break Res', style = label.style_label_up, color = #2b6d2d, textcolor = chart.fg_color, size = size.small)

// 警报标记 - 醒目的视觉提示
// 支撑位保持警报标记
if sup_holds
    label.new(bar_index, low, text = '🔔SUP', style = label.style_label_down, color = color.new(color.lime, 0), textcolor = color.black, size = size.large, tooltip = '支撑位保持警报')

// 阻力位保持警报标记
if res_holds
    label.new(bar_index, high, text = '🔔RES', style = label.style_label_up, color = color.new(color.red, 0), textcolor = color.white, size = size.large, tooltip = '阻力位保持警报')

// 支撑位突破警报标记
if brekout_sup
    label.new(bar_index[1], supportLevel[1], text = '🚨SUP⬇', style = label.style_label_down, color = color.new(color.orange, 0), textcolor = color.black, size = size.large, tooltip = '支撑位突破警报')

// 阻力位突破警报标记
if brekout_res
    label.new(bar_index[1], resistanceLevel[1], text = '🚨RES⬆', style = label.style_label_up, color = color.new(color.aqua, 0), textcolor = color.black, size = size.large, tooltip = '阻力位突破警报')

// 阻力转支撑警报标记
if brekout_res and res_is_sup[1]
    label.new(bar_index[1], resistanceLevel[1], text = '⭐R→S', style = label.style_label_down, color = color.new(color.purple, 0), textcolor = color.white, size = size.large, tooltip = '阻力转支撑保持警报')

// 支撑转阻力警报标记
if brekout_sup and sup_is_res[1]
    label.new(bar_index[1], supportLevel[1], text = '⭐S→R', style = label.style_label_up, color = color.new(color.maroon, 0), textcolor = color.white, size = size.large, tooltip = '支撑转阻力保持警报')

// ═════════ 当前时间周期价格状态判断（使用主要计算方法，包含成交量过滤） ════════
// 使用主要计算方法的价格状态判断，与通知模板保持一致
current_price_status = get_price_status(close, supportLevel, resistanceLevel)
is_bullish = current_price_status == "看多"
is_bearish = current_price_status == "看空"
is_ranging = current_price_status == "震荡"

// 绘制支撑位和阻力位用于警报模板变量
plot(supportLevel, title = "supportLevel", color = color.new(color.blue, 0), display = display.none)
plot(resistanceLevel, title = "resistanceLevel", color = color.new(color.red, 0), display = display.none)

// 全局作用域的plotshape - 警报形状标记
plotshape(sup_holds, title = '支撑保持警报', style = shape.triangleup, location = location.belowbar, color = color.new(color.lime, 0), size = size.large)
plotshape(res_holds, title = '阻力保持警报', style = shape.triangledown, location = location.abovebar, color = color.new(color.red, 0), size = size.large)
plotshape(brekout_sup, title = '支撑突破警报', style = shape.xcross, location = location.belowbar, color = color.new(color.orange, 0), size = size.large)
plotshape(brekout_res, title = '阻力突破警报', style = shape.xcross, location = location.abovebar, color = color.new(color.aqua, 0), size = size.large)
plotshape(brekout_res and res_is_sup[1], title = '阻力转支撑警报', style = shape.diamond, location = location.belowbar, color = color.new(color.purple, 0), size = size.large)
plotshape(brekout_sup and sup_is_res[1], title = '支撑转阻力警报', style = shape.diamond, location = location.abovebar, color = color.new(color.maroon, 0), size = size.large)


// ◆
// ---------------------------------------------------------------------------------------------------------------------}
// 𝘼𝙇𝙀𝙍𝙏 𝘾𝙊𝙉𝘿𝙄𝙏𝙄𝙊𝙉𝙎
// ---------------------------------------------------------------------------------------------------------------------{

// 支撑位保持警报
alertcondition(sup_holds, title = '支撑位保持', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"支撑位保持","位置":"支撑位","信号":"support_holds"}')

// 阻力位保持警报
alertcondition(res_holds, title = '阻力位保持', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"阻力位保持","位置":"阻力位","信号":"resistance_holds"}')

// 支撑位突破警报
alertcondition(brekout_sup, title = '支撑位突破', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"支撑位突破","位置":"支撑位下方","信号":"support_breakout"}')

// 阻力位突破警报
alertcondition(brekout_res, title = '阻力位突破', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"阻力位突破","位置":"阻力位上方","信号":"resistance_breakout"}')

// 阻力转支撑警报
alertcondition(brekout_res and res_is_sup[1], title = '阻力转支撑保持', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"阻力转支撑保持","位置":"前阻力位","信号":"resistance_as_support"}')

// 支撑转阻力警报
alertcondition(brekout_sup and sup_is_res[1], title = '支撑转阻力保持', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"支撑转阻力保持","位置":"前支撑位","信号":"support_as_resistance"}')

// 价格状态警报 - 震荡
alertcondition(is_ranging, title = '价格处于震荡', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于震荡","位置":"支撑阻力区间内","信号":"price_ranging"}')

// 价格状态警报 - 做多
alertcondition(is_bullish, title = '价格处于做多', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于做多","位置":"阻力位上方","信号":"price_bullish"}')

// 价格状态警报 - 做空
alertcondition(is_bearish, title = '价格处于做空', message = '{"指标名称":"SRBR","交易对":"{{ticker}}","周期":"{{interval}}","支撑位":"{{plot("supportLevel")}}","阻力位":"{{plot("resistanceLevel")}}","开盘价":"{{open}}","收盘价":"{{close}}","最高价":"{{high}}","最低价":"{{low}}","触发时间":"{{timenow}}","时间":"{{time}}","事件":"价格处于做空","位置":"支撑位下方","信号":"price_bearish"}')

// ---------------------------------------------------------------------------------------------------------------------}

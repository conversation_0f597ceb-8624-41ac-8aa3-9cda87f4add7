#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易过程监控工具 - 查看订单执行情况以及止损止盈配置
"""

import MetaTrader5 as mt5
import sqlite3
import json
import time
import logging
import os
from datetime import datetime
from tabulate import tabulate
import colorama
from colorama import Fore, Back, Style

# 初始化colorama
colorama.init()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def init_mt5():
    """初始化MT5连接"""
    if not mt5.initialize():
        logger.error("MT5初始化失败")
        return False
    logger.info("MT5初始化成功")
    return True

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def get_latest_signals(limit=5):
    """获取最新的交易信号"""
    try:
        conn = get_db_connection()
        if not conn:
            return []
        
        c = conn.cursor()
        c.execute('''
            SELECT id, timestamp, trading_pair, signal_type, processed, order_ticket, order_result,
                   mean, r1, r2, s1, s2, close_price
            FROM signals
            ORDER BY id DESC
            LIMIT ?
        ''', (limit,))
        
        signals = [dict(row) for row in c.fetchall()]
        conn.close()
        return signals
    except sqlite3.Error as e:
        logger.error(f"获取信号失败: {e}")
        return []

def get_active_orders():
    """获取活跃订单信息"""
    try:
        conn = get_db_connection()
        if not conn:
            return []
        
        c = conn.cursor()
        c.execute('''
            SELECT o.id, o.timestamp, o.ticket, o.trading_pair, o.operation, 
                   o.volume, o.price, o.sl, o.tp, o.signal_id, o.status, o.profit,
                   s.mean, s.r1, s.r2, s.s1, s.s2
            FROM orders o
            LEFT JOIN signals s ON o.signal_id = s.id
            WHERE o.status IN ('open', 'partially_closed')
            ORDER BY o.timestamp DESC
        ''')
        
        orders = [dict(row) for row in c.fetchall()]
        conn.close()
        return orders
    except sqlite3.Error as e:
        logger.error(f"获取订单失败: {e}")
        return []

def get_mt5_positions():
    """获取MT5持仓信息"""
    if not mt5.initialize():
        logger.error("MT5未初始化")
        return []
    
    positions = mt5.positions_get()
    if positions is None:
        return []
    
    positions_list = []
    for position in positions:
        position_info = {
            'ticket': position.ticket,
            'type': 'buy' if position.type == mt5.POSITION_TYPE_BUY else 'sell',
            'symbol': position.symbol,
            'volume': position.volume,
            'price_open': position.price_open,
            'price_current': position.price_current,
            'sl': position.sl,
            'tp': position.tp,
            'profit': position.profit,
            'time': position.time,
            'comment': position.comment
        }
        positions_list.append(position_info)
    
    return positions_list

def get_sl_tp_calculation_method():
    """获取止损止盈计算方式"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('sl_tp_calculation_method', 'points')
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return 'unknown'

def get_symbol_digits(symbol):
    """获取交易品种的小数位数"""
    # 根据不同交易品种返回合适的小数位数
    if "XAUUSD" in symbol:  # 黄金
        return 2
    elif "BTCUSD" in symbol:  # 比特币
        return 1
    elif "ETHUSD" in symbol:  # 以太坊
        return 1
    elif "JPY" in symbol:  # 日元对
        return 3
    else:
        # 尝试从MT5获取
        if not mt5.initialize():
            logger.error("MT5未初始化")
            return 5  # 默认值
        
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            return symbol_info.digits
        return 5  # 默认值

def format_price(symbol, price):
    """根据交易品种格式化价格显示"""
    if price is None:
        return "-"
    
    digits = get_symbol_digits(symbol)
    return f"{price:.{digits}f}"

def calculate_point_value(symbol):
    """计算指定品种的点值"""
    if not mt5.initialize():
        logger.error("MT5未初始化")
        return 0.0
    
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        logger.error(f"获取交易品种 {symbol} 信息失败")
        return 0.0
    
    return symbol_info.point

def monitor_trading():
    """监控交易执行情况"""
    print(f"\n{Fore.GREEN}===== 交易信号测试监控工具 ====={Style.RESET_ALL}")
    
    # 获取止损止盈计算方式
    sl_tp_method = get_sl_tp_calculation_method()
    print(f"\n当前止损止盈计算方式: {Fore.CYAN}{sl_tp_method}{Style.RESET_ALL}")
    
    if sl_tp_method == 'points':
        print(f"{Fore.YELLOW}使用点数计算: 入场价格 ± 配置的点数{Style.RESET_ALL}")
    elif sl_tp_method == 'signal_levels':
        print(f"{Fore.YELLOW}使用信号级别: BUY(止损=MEAN, 止盈=R2), SELL(止损=MEAN, 止盈=S2){Style.RESET_ALL}")
    
    # 获取最新信号
    signals = get_latest_signals(5)
    if signals:
        print(f"\n{Fore.GREEN}最新交易信号:{Style.RESET_ALL}")
        signal_data = []
        for s in signals:
            signal_status = f"{Fore.GREEN}已处理{Style.RESET_ALL}" if s['processed'] else f"{Fore.RED}未处理{Style.RESET_ALL}"
            signal_data.append([
                s['id'],
                s['trading_pair'],
                s['signal_type'],
                s['close_price'],
                signal_status,
                s['order_ticket'] if s['order_ticket'] else '-',
                s['order_result'] if s['order_result'] else '-'
            ])
        
        headers = ["ID", "交易对", "类型", "价格", "状态", "订单号", "结果"]
        print(tabulate(signal_data, headers=headers, tablefmt="grid"))
    else:
        print(f"{Fore.RED}未找到交易信号{Style.RESET_ALL}")
    
    # 获取活跃订单
    db_orders = get_active_orders()
    mt5_positions = get_mt5_positions()
    
    if db_orders or mt5_positions:
        print(f"\n{Fore.GREEN}活跃订单情况:{Style.RESET_ALL}")
        
        # 数据库中的订单
        if db_orders:
            db_order_data = []
            for o in db_orders:                # 获取交易品种
                trading_pair = o['trading_pair']
                # 格式化价格显示
                price = format_price(trading_pair, o['price'])
                sl = format_price(trading_pair, o['sl'])
                tp = format_price(trading_pair, o['tp'])
                
                # 计算止损止盈点数/价格差
                point = calculate_point_value(trading_pair)
                
                if o['operation'] == 'buy':
                    sl_points = (o['price'] - o['sl']) / point if o['sl'] and point > 0 else '-'
                    tp_points = (o['tp'] - o['price']) / point if o['tp'] and point > 0 else '-'
                else:
                    sl_points = (o['sl'] - o['price']) / point if o['sl'] and point > 0 else '-'
                    tp_points = (o['price'] - o['tp']) / point if o['tp'] and point > 0 else '-'
                
                # 计算是否使用了信号级别
                sl_source = "-"
                tp_source = "-"
                
                if sl_tp_method == 'signal_levels':
                    # 获取信号价格级别并格式化
                    mean_formatted = format_price(trading_pair, o['mean'])
                    r2_formatted = format_price(trading_pair, o['r2'])
                    s2_formatted = format_price(trading_pair, o['s2'])
                    
                    # 检查是否匹配信号级别，使用格式化后的价格比较
                    tolerance = 10 ** (-get_symbol_digits(trading_pair))  # 基于小数位数的容差
                    
                    if o['operation'] == 'buy' and o['mean'] and abs(o['sl'] - o['mean']) < tolerance:
                        sl_source = f"{Fore.CYAN}MEAN ({mean_formatted}){Style.RESET_ALL}"
                    if o['operation'] == 'buy' and o['r2'] and abs(o['tp'] - o['r2']) < tolerance:
                        tp_source = f"{Fore.CYAN}R2 ({r2_formatted}){Style.RESET_ALL}"
                    if o['operation'] == 'sell' and o['mean'] and abs(o['sl'] - o['mean']) < tolerance:
                        sl_source = f"{Fore.CYAN}MEAN ({mean_formatted}){Style.RESET_ALL}"
                    if o['operation'] == 'sell' and o['s2'] and abs(o['tp'] - o['s2']) < tolerance:
                        tp_source = f"{Fore.CYAN}S2 ({s2_formatted}){Style.RESET_ALL}"                
                if sl_source == "-":
                    sl_source = f"{Fore.YELLOW}Points: {sl_points}{Style.RESET_ALL}"
                if tp_source == "-":
                    tp_source = f"{Fore.YELLOW}Points: {tp_points}{Style.RESET_ALL}"
                
                db_order_data.append([
                    o['ticket'],
                    o['trading_pair'],
                    o['operation'],
                    o['volume'],
                    price,  # 格式化后的价格
                    sl,     # 格式化后的止损价
                    sl_source,
                    tp,     # 格式化后的止盈价
                    tp_source,
                    o['status']
                ])
            
            headers = ["订单号", "交易对", "类型", "手数", "入场价", "止损价", "止损来源", "止盈价", "止盈来源", "状态"]
            print(tabulate(db_order_data, headers=headers, tablefmt="grid"))
          # MT5中的持仓
        if mt5_positions:
            mt5_position_data = []
            for p in mt5_positions:
                # 格式化价格显示
                symbol = p['symbol']
                price_open = format_price(symbol, p['price_open'])
                price_current = format_price(symbol, p['price_current'])
                sl = format_price(symbol, p['sl'])
                tp = format_price(symbol, p['tp'])
                
                # 计算浮动盈亏颜色
                profit_str = f"{Fore.GREEN}{p['profit']:.2f}{Style.RESET_ALL}" if p['profit'] >= 0 else f"{Fore.RED}{p['profit']:.2f}{Style.RESET_ALL}"
                
                # 获取信号ID (从评论中提取)
                signal_id = "-"
                if p['comment'] and "Signal ID:" in p['comment']:
                    try:
                        signal_id = p['comment'].split("Signal ID:")[1].strip()
                    except:
                        pass
                
                mt5_position_data.append([
                    p['ticket'],
                    p['symbol'],
                    p['type'],
                    p['volume'],
                    price_open,       # 格式化后的开仓价
                    price_current,    # 格式化后的当前价
                    sl,               # 格式化后的止损价
                    tp,               # 格式化后的止盈价
                    profit_str,
                    signal_id
                ])
            
            print(f"\n{Fore.GREEN}MT5实际持仓:{Style.RESET_ALL}")
            headers = ["订单号", "交易对", "类型", "手数", "入场价", "当前价", "止损价", "止盈价", "浮动盈亏", "信号ID"]
            print(tabulate(mt5_position_data, headers=headers, tablefmt="grid"))
    else:
        print(f"\n{Fore.YELLOW}当前没有活跃订单{Style.RESET_ALL}")

def main():
    """主函数"""
    # 初始化MT5
    if not init_mt5():
        logger.error("无法启动MT5连接，但将继续尝试从数据库获取信息")
    
    try:
        print(f"{Fore.CYAN}开始监控交易执行情况，按 Ctrl+C 退出{Style.RESET_ALL}")
        
        # 持续监控
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
            monitor_trading()
            time.sleep(5)  # 每5秒刷新一次
    
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}退出监控{Style.RESET_ALL}")
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    main()

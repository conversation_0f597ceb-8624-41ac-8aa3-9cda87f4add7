"""
风险控制和监控系统
"""
import threading
import time
from datetime import datetime, timedelta
from mt5.connection import mt5_connection
from mt5.trading import trading_engine
from trading.config_manager import config_manager
from database.manager import db_manager
from notifications.bark import bark_manager
from utils.logger import logger

class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_balance_check = None
        self.daily_stats = {
            'start_balance': 0.0,
            'current_balance': 0.0,
            'daily_pnl': 0.0,
            'trade_count': 0,
            'last_reset': datetime.now().date()
        }
        # 添加通知频率控制 - 避免重复发送保证金通知
        self.last_margin_notification = {}  # 记录上次通知时间
    
    def start_monitoring(self):
        """启动风险监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("风险监控系统已启动")
    
    def stop_monitoring(self):
        """停止风险监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("风险监控系统已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 检查全局止盈止损
                self._check_global_sl_tp()
                
                # 检查日交易限制
                self._check_daily_limits()
                
                # 检查账户风险
                self._check_account_risk()
                
                # 更新余额记录
                self._update_balance_history()
                
                # 发送定时余额报告
                self._send_balance_report()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"风险监控循环异常: {e}")
                time.sleep(60)  # 异常时等待1分钟
    
    def _check_global_sl_tp(self):
        """检查全局止盈止损"""
        try:
            portfolio_config = config_manager.get_portfolio_config()
            if not portfolio_config or not portfolio_config.get("enabled", False):
                return
            
            # 计算当前总盈亏
            total_pnl = self._calculate_total_pnl()
            
            global_sl = portfolio_config["global_stop_loss_usd"]
            global_tp = portfolio_config["global_take_profit_usd"]
            
            # 检查止损
            if total_pnl <= -global_sl:
                logger.critical(f"触发全局止损: 总盈亏 {total_pnl:.2f} <= -{global_sl:.2f}")
                self._trigger_global_stop_loss(total_pnl, global_sl)
            
            # 检查止盈
            elif total_pnl >= global_tp:
                logger.info(f"触发全局止盈: 总盈亏 {total_pnl:.2f} >= {global_tp:.2f}")
                self._trigger_global_take_profit(total_pnl, global_tp)
            
            # 更新最后检查时间
            db_manager.execute_query(
                "UPDATE portfolio_sl_tp SET last_check_time = ?",
                (datetime.now(),)
            )
            
        except Exception as e:
            logger.error(f"检查全局止盈止损失败: {e}")
    
    def _trigger_global_stop_loss(self, total_pnl, threshold):
        """触发全局止损"""
        try:
            # 获取所有持仓
            positions = mt5_connection.get_positions()
            
            # 平仓所有订单
            closed_orders = []
            for position in positions:
                close_result = trading_engine.close_position(
                    position["ticket"],
                    reason="全局止损触发"
                )
                
                if close_result["success"]:
                    closed_orders.append(position["ticket"])
                    logger.info(f"全局止损平仓成功: 订单#{position['ticket']}")
                else:
                    logger.error(f"全局止损平仓失败: 订单#{position['ticket']}")
            
            # 发送通知
            bark_manager.send_notification("全局止盈止损触发", {
                "trigger_type": "全局止损",
                "total_pnl": total_pnl,
                "threshold": threshold,
                "affected_orders": len(closed_orders),
                "timestamp": datetime.now().isoformat()
            })
            
            # 设置紧急停止
            config_manager.update_system_config("emergency_stop", "True")
            
        except Exception as e:
            logger.error(f"执行全局止损失败: {e}")
    
    def _trigger_global_take_profit(self, total_pnl, threshold):
        """触发全局止盈"""
        try:
            # 获取所有持仓
            positions = mt5_connection.get_positions()
            
            # 平仓所有订单
            closed_orders = []
            for position in positions:
                close_result = trading_engine.close_position(
                    position["ticket"],
                    reason="全局止盈触发"
                )
                
                if close_result["success"]:
                    closed_orders.append(position["ticket"])
                    logger.info(f"全局止盈平仓成功: 订单#{position['ticket']}")
                else:
                    logger.error(f"全局止盈平仓失败: 订单#{position['ticket']}")
            
            # 发送通知
            bark_manager.send_notification("全局止盈止损触发", {
                "trigger_type": "全局止盈",
                "total_pnl": total_pnl,
                "threshold": threshold,
                "affected_orders": len(closed_orders),
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"执行全局止盈失败: {e}")
    
    def _check_daily_limits(self):
        """检查日交易限制"""
        try:
            # 重置日统计（如果是新的一天）
            today = datetime.now().date()
            if self.daily_stats['last_reset'] != today:
                self._reset_daily_stats()
            
            # 获取当前账户信息
            account_info = mt5_connection.get_account_info()
            if not account_info:
                return
            
            # 更新当前余额和日盈亏
            current_balance = account_info['balance']
            if self.daily_stats['start_balance'] == 0:
                self.daily_stats['start_balance'] = current_balance
            
            self.daily_stats['current_balance'] = current_balance
            self.daily_stats['daily_pnl'] = current_balance - self.daily_stats['start_balance']
            
            # 检查日亏损限制
            max_daily_loss = float(config_manager.get_system_config("max_daily_loss_usd") or 500)
            if self.daily_stats['daily_pnl'] <= -max_daily_loss:
                logger.critical(f"触发日亏损限制: {self.daily_stats['daily_pnl']:.2f} <= -{max_daily_loss:.2f}")
                self._trigger_daily_loss_limit()
            
            # 检查日盈利限制
            max_daily_profit = float(config_manager.get_system_config("max_daily_profit_usd") or 1000)
            if self.daily_stats['daily_pnl'] >= max_daily_profit:
                logger.info(f"达到日盈利限制: {self.daily_stats['daily_pnl']:.2f} >= {max_daily_profit:.2f}")
                self._trigger_daily_profit_limit()
            
        except Exception as e:
            logger.error(f"检查日交易限制失败: {e}")
    
    def _reset_daily_stats(self):
        """重置日统计"""
        account_info = mt5_connection.get_account_info()
        if account_info:
            self.daily_stats = {
                'start_balance': account_info['balance'],
                'current_balance': account_info['balance'],
                'daily_pnl': 0.0,
                'trade_count': 0,
                'last_reset': datetime.now().date()
            }
            logger.info("日统计已重置")
    
    def _trigger_daily_loss_limit(self):
        """触发日亏损限制"""
        try:
            # 平仓所有订单
            positions = mt5_connection.get_positions()
            for position in positions:
                trading_engine.close_position(
                    position["ticket"],
                    reason="日亏损限制触发"
                )
            
            # 设置紧急停止
            config_manager.update_system_config("emergency_stop", "True")
            
            # 发送通知
            bark_manager.send_notification("系统状态", {
                "status_type": "日亏损限制触发",
                "details": f"日盈亏: {self.daily_stats['daily_pnl']:.2f} USD，系统已停止交易",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"执行日亏损限制失败: {e}")
    
    def _trigger_daily_profit_limit(self):
        """触发日盈利限制"""
        try:
            # 平仓所有订单
            positions = mt5_connection.get_positions()
            for position in positions:
                trading_engine.close_position(
                    position["ticket"],
                    reason="日盈利限制触发"
                )
            
            # 发送通知
            bark_manager.send_notification("系统状态", {
                "status_type": "日盈利限制触发",
                "details": f"日盈亏: {self.daily_stats['daily_pnl']:.2f} USD，建议停止交易",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"执行日盈利限制失败: {e}")
    
    def _check_account_risk(self):
        """检查账户风险"""
        try:
            account_info = mt5_connection.get_account_info()
            if not account_info:
                return

            current_time = datetime.now()

            # 检查保证金水平
            margin_level = account_info.get('margin_level', 0)
            if margin_level > 0 and margin_level < 200:  # 保证金水平低于200%
                logger.warning(f"保证金水平过低: {margin_level:.2f}%")

                # 检查是否需要发送通知（避免频繁通知）
                notification_key = "margin_level_warning"
                last_notification = self.last_margin_notification.get(notification_key)

                # 只有在30分钟内没有发送过相同通知时才发送
                if (not last_notification or
                    (current_time - last_notification).total_seconds() > 1800):  # 30分钟

                    # 检查系统状态通知是否启用
                    if bark_manager._is_notification_type_enabled("系统状态通知"):
                        # 发送警告通知
                        bark_manager.send_notification("系统状态", {
                            "status_type": "保证金水平警告",
                            "details": f"当前保证金水平: {margin_level:.2f}%，请注意风险",
                            "timestamp": current_time.isoformat()
                        })
                        # 记录通知时间
                        self.last_margin_notification[notification_key] = current_time

            # 检查可用保证金
            free_margin = account_info.get('free_margin', 0)
            if free_margin < 100:  # 可用保证金低于100美元
                logger.warning(f"可用保证金不足: ${free_margin:.2f}")

                # 检查是否需要发送通知（避免频繁通知）
                notification_key = "free_margin_warning"
                last_notification = self.last_margin_notification.get(notification_key)

                # 只有在30分钟内没有发送过相同通知时才发送
                if (not last_notification or
                    (current_time - last_notification).total_seconds() > 1800):  # 30分钟

                    # 检查系统状态通知是否启用
                    if bark_manager._is_notification_type_enabled("系统状态通知"):
                        # 发送警告通知
                        bark_manager.send_notification("系统状态", {
                            "status_type": "可用保证金不足",
                            "details": f"当前可用保证金: ${free_margin:.2f}",
                            "timestamp": current_time.isoformat()
                        })
                        # 记录通知时间
                        self.last_margin_notification[notification_key] = current_time

        except Exception as e:
            logger.error(f"检查账户风险失败: {e}")
    
    def _calculate_total_pnl(self):
        """计算总盈亏"""
        try:
            positions = mt5_connection.get_positions()
            total_pnl = sum(pos["profit"] for pos in positions)
            return total_pnl
        except Exception as e:
            logger.error(f"计算总盈亏失败: {e}")
            return 0.0
    
    def _update_balance_history(self):
        """更新余额记录"""
        try:
            # 每5分钟记录一次
            if (self.last_balance_check and 
                (datetime.now() - self.last_balance_check).seconds < 300):
                return
            
            account_info = mt5_connection.get_account_info()
            if not account_info:
                return
            
            positions = mt5_connection.get_positions()
            
            db_manager.execute_query(
                """INSERT INTO balance_history 
                   (balance, equity, margin, free_margin, daily_pnl, open_positions)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (
                    account_info['balance'],
                    account_info['equity'],
                    account_info['margin'],
                    account_info['free_margin'],
                    self.daily_stats['daily_pnl'],
                    len(positions)
                )
            )
            
            self.last_balance_check = datetime.now()
            
        except Exception as e:
            logger.error(f"更新余额记录失败: {e}")
    
    def _send_balance_report(self):
        """发送定时余额报告"""
        try:
            # 每小时发送一次
            if (self.last_balance_check and 
                (datetime.now() - self.last_balance_check).seconds < 3600):
                return
            
            account_info = mt5_connection.get_account_info()
            if not account_info:
                return
            
            positions = mt5_connection.get_positions()
            
            bark_manager.send_balance_report(
                account_info,
                len(positions),
                self.daily_stats['daily_pnl']
            )
            
        except Exception as e:
            logger.error(f"发送余额报告失败: {e}")
    
    def get_risk_metrics(self):
        """获取风险指标"""
        try:
            account_info = mt5_connection.get_account_info()
            positions = mt5_connection.get_positions()
            
            if not account_info:
                return None
            
            total_pnl = sum(pos["profit"] for pos in positions)
            
            return {
                "account_balance": account_info['balance'],
                "account_equity": account_info['equity'],
                "margin_level": account_info.get('margin_level', 0),
                "free_margin": account_info['free_margin'],
                "total_positions": len(positions),
                "total_pnl": total_pnl,
                "daily_pnl": self.daily_stats['daily_pnl'],
                "daily_trade_count": self.daily_stats['trade_count'],
                "risk_status": self._assess_risk_level(account_info, total_pnl),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取风险指标失败: {e}")
            return None
    
    def _assess_risk_level(self, account_info, total_pnl):
        """评估风险等级"""
        try:
            margin_level = account_info.get('margin_level', 0)
            free_margin = account_info.get('free_margin', 0)
            
            # 高风险条件
            if margin_level > 0 and margin_level < 150:
                return "HIGH"
            if free_margin < 50:
                return "HIGH"
            if abs(total_pnl) > account_info['balance'] * 0.1:  # 盈亏超过余额10%
                return "HIGH"
            
            # 中风险条件
            if margin_level > 0 and margin_level < 300:
                return "MEDIUM"
            if free_margin < 200:
                return "MEDIUM"
            if abs(total_pnl) > account_info['balance'] * 0.05:  # 盈亏超过余额5%
                return "MEDIUM"
            
            return "LOW"
            
        except Exception:
            return "UNKNOWN"

# 创建全局风险管理器实例
risk_manager = RiskManager()

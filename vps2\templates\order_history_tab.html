<!-- Column 1: Order History Tab Content -->
<div id="allOrdersTab" class="tab-pane fade show active">
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col">订单号</th>
                    <th scope="col">开仓时间</th>
                    <th scope="col">平仓时间</th>
                    <th scope="col">交易对</th>
                    <th scope="col">方向</th>
                    <th scope="col">交易量</th>
                    <th scope="col">开仓价</th>
                    <th scope="col">平仓价</th>
                    <th scope="col">持仓时长</th>
                    <th scope="col">止损</th>
                    <th scope="col">止盈</th>
                    <th scope="col">盈亏</th>
                    <th scope="col">隔夜利息</th>
                    <th scope="col">详情</th>
                </tr>
            </thead>
            <tbody>
                {% if all_history_orders %}
                    {% for order in all_history_orders %}
                        <tr>
                            <td>{{ order['ticket'] }}</td>
                            <td>{{ order['open_time_formatted'] }}</td>
                            <td>{{ order['close_time_formatted'] }}</td>
                            <td>{{ order['trading_pair'] }}</td>
                            <td>
                                {% if order['operation'] == 'buy' %}
                                    <span class="badge bg-success">买入</span>
                                {% else %}
                                    <span class="badge bg-danger">卖出</span>
                                {% endif %}
                            </td>
                            <td>{{ order['volume'] }}</td>
                            <td>{{ order['price'] }}</td>
                            <td>{{ order['close_price'] }}</td>
                            <td>{{ order['duration'] }}</td>
                            <td>{{ order['sl'] if order['sl'] else '-' }}</td>
                            <td>{{ order['tp'] if order['tp'] else '-' }}</td>
                            <td class="{% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% endif %} fw-bold">
                                {{ "%.2f"|format(order['profit']|float) }}
                            </td>
                            <td class="{% if order['swap'] > 0 %}text-success{% elif order['swap'] < 0 %}text-danger{% endif %}">
                                {{ "%.2f"|format(order['swap']|float) if order['swap'] is not none else '-' }}
                            </td>
                            <td>
                                <a href="{{ url_for('order_detail', order_id=order['id']) }}" class="btn btn-sm btn-outline-primary">详情</a>
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="14" class="text-center py-3">暂无历史订单数据</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

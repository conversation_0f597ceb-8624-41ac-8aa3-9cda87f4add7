#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
import sqlite3
from datetime import datetime

app = Flask(__name__)

@app.route('/api/alerts/stats', methods=['GET'])
def get_alert_stats_fixed():
    """修复后的警报统计API - 简单直接的实现"""
    try:
        hours = int(request.args.get('hours', '1'))
        
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 简单直接的查询 - 基于北京时间
        query = f"""
            SELECT standard_symbol, standard_signal, 
                   datetime(created_at, '+8 hours') as beijing_time
            FROM alerts
            WHERE datetime(created_at, '+8 hours') >= datetime('now', '+8 hours', '-{hours} hours')
            ORDER BY created_at DESC
        """
        
        cursor.execute(query)
        alerts = cursor.fetchall()
        conn.close()
        
        # 统计
        stats = {
            'BTCUSD': {'buy': 0, 'sell': 0, 'latest': None},
            'ETHUSD': {'buy': 0, 'sell': 0, 'latest': None},
            'SOLUSD': {'buy': 0, 'sell': 0, 'latest': None},
            'XAUUSD': {'buy': 0, 'sell': 0, 'latest': None},
            'GBPJPY': {'buy': 0, 'sell': 0, 'latest': None}
        }
        
        for alert in alerts:
            symbol, signal, beijing_time = alert
            
            if symbol in stats:
                if signal == 'BUY':
                    stats[symbol]['buy'] += 1
                elif signal == 'SELL':
                    stats[symbol]['sell'] += 1
                
                if not stats[symbol]['latest'] or beijing_time > stats[symbol]['latest']:
                    stats[symbol]['latest'] = beijing_time
        
        return jsonify({
            "success": True,
            "data": stats,
            "time_range_hours": hours,
            "total_alerts": len(alerts),
            "query_time": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    # 测试这个修复的API
    with app.test_client() as client:
        print("测试修复后的API:")
        for hours in [1, 4, 24]:
            response = client.get(f'/api/alerts/stats?hours={hours}')
            if response.status_code == 200:
                data = response.get_json()
                total = data.get('total_alerts', 0)
                print(f'\n{hours}小时 (总计{total}条):')
                for symbol, stats in data['data'].items():
                    if stats['buy'] > 0 or stats['sell'] > 0:
                        print(f'  {symbol}: 📈{stats["buy"]} 📉{stats["sell"]}')
            else:
                print(f'{hours}小时: 错误 {response.status_code}')

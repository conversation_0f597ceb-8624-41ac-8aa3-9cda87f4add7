<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 信号管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 56px);
        }
        .sidebar a {
            color: #adb5bd;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
        }
        .sidebar a:hover, .sidebar a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar i {
            margin-right: 8px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-red {
            background-color: #dc3545;
        }
        .filter-form {
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .table {
            font-size: 0.75rem;
            border: 1px dashed #dee2e6;
        }

        /* 表格边框样式 */
        .table th, .table td {
            border: 1px dashed #dee2e6 !important;
            padding: 0.4rem 0.2rem;
            vertical-align: middle;
            line-height: 1.2;
            text-align: center; /* 所有内容居中 */
        }

        /* 表头样式统一 */
        .table thead th {
            background-color: #f8f9fa;
            font-weight: 600;
            font-size: 0.75rem;
            text-align: center;
            border-bottom: 2px dashed #adb5bd;
        }

        /* 列宽设置 */
        .table .action-column {
            width: 80px;
        }
        .timestamp-column {
            width: 110px;
            font-size: 0.7rem;
        }
        .price-column {
            width: 60px;
            font-family: 'Courier New', monospace;
        }
        .signal-level-column {
            width: 50px;
            font-family: 'Courier New', monospace;
            font-size: 0.7rem;
        }
        .status-column {
            width: 65px;
        }
        .trading-pair-column {
            width: 70px;
            font-weight: 500;
        }
        .signal-type-column {
            width: 60px;
        }
        .id-column {
            width: 40px;
        }
        .order-ticket-column {
            width: 60px;
            font-size: 0.7rem;
        }
        .order-result-column {
            width: 60px;
        }

        /* 响应式优化 */
        @media (max-width: 1400px) {
            .table {
                font-size: 0.7rem;
            }
            .signal-level-column {
                width: 45px;
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">信号管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#addSignalModal">
                            <i class="bi bi-plus-lg"></i> 添加手动信号
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 信号筛选表单 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-funnel me-1"></i> 信号筛选
                    </div>
                    <div class="card-body">
                        <form method="get" action="{{ url_for('signals') }}" class="row g-3">
                            <div class="col-md-3">
                                <label for="trading_pair" class="form-label">交易对</label>
                                <input type="text" class="form-control" id="trading_pair" name="trading_pair" 
                                    value="{{ request.args.get('trading_pair', '') }}" placeholder="例如：EURUSD">
                            </div>
                            <div class="col-md-2">
                                <label for="signal_type" class="form-label">信号类型</label>
                                <select class="form-select" id="signal_type" name="signal_type">
                                    <option value="" {% if not request.args.get('signal_type') %}selected{% endif %}>全部</option>
                                    <option value="buy" {% if request.args.get('signal_type') == 'buy' %}selected{% endif %}>买入</option>
                                    <option value="sell" {% if request.args.get('signal_type') == 'sell' %}selected{% endif %}>卖出</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="processed" class="form-label">处理状态</label>
                                <select class="form-select" id="processed" name="processed">
                                    <option value="" {% if not request.args.get('processed') %}selected{% endif %}>全部</option>
                                    <option value="1" {% if request.args.get('processed') == '1' %}selected{% endif %}>已处理</option>
                                    <option value="0" {% if request.args.get('processed') == '0' %}selected{% endif %}>未处理</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                    value="{{ request.args.get('date_from', '') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                    value="{{ request.args.get('date_to', '') }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 信号表格 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5 class="mb-0">信号列表</h5>
                        <div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" id="exportCsvBtn">
                                    <i class="bi bi-file-earmark-spreadsheet"></i> 导出CSV
                                </button>
                                <button class="btn btn-outline-danger" id="batchDeleteBtn" data-bs-toggle="modal" data-bs-target="#batchDeleteModal">
                                    <i class="bi bi-trash"></i> 批量删除
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th scope="col" width="40">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllSignals">
                                            </div>
                                        </th>
                                        <th scope="col" class="id-column">ID</th>
                                        <th scope="col" class="timestamp-column">时间</th>
                                        <th scope="col" class="trading-pair-column">交易对</th>
                                        <th scope="col" class="signal-level-column">周期</th>
                                        <th scope="col" class="signal-type-column">类型</th>
                                        <th scope="col" class="price-column">价格</th>
                                        <th scope="col" class="price-column">订单价格</th>
                                        <th scope="col" class="signal-level-column">均值</th>
                                        <th scope="col" class="signal-level-column">S1</th>
                                        <th scope="col" class="signal-level-column">R1</th>
                                        <th scope="col" class="signal-level-column">S2</th>
                                        <th scope="col" class="signal-level-column">R2</th>
                                        <th scope="col" class="signal-level-column">S2_1</th>
                                        <th scope="col" class="signal-level-column">R2_1</th>
                                        <th scope="col" class="signal-level-column">S2_9</th>
                                        <th scope="col" class="signal-level-column">R2_9</th>
                                        <th scope="col" class="status-column">状态</th>
                                        <th scope="col" class="order-ticket-column">订单号</th>
                                        <th scope="col" class="order-result-column">结果</th>
                                        <th scope="col" class="action-column">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if signals %}
                                        {% for signal in signals %}
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input signal-checkbox" type="checkbox" 
                                                            value="{{ signal['id'] }}" data-processed="{{ signal['processed'] }}">
                                                    </div>
                                                </td>
                                                <td class="id-column">{{ signal['id'] }}</td>
                                                <td class="timestamp-column">
                                                    <small>{{ signal['timestamp'].replace('T', ' ').split('.')[0] }}</small>
                                                </td>
                                                <td class="trading-pair-column">{{ signal['trading_pair'] }}</td>
                                                <td class="signal-level-column">{{ signal['interval'] if signal['interval'] else '-' }}</td>
                                                <td class="signal-type-column">
                                                    {% if signal['signal_type'] == 'buy' %}
                                                        <span class="badge bg-success" style="font-size: 0.65rem;">买</span>
                                                    {% else %}
                                                        <span class="badge bg-danger" style="font-size: 0.65rem;">卖</span>
                                                    {% endif %}
                                                </td>
                                                <td class="price-column">{{ "%.2f"|format(signal['price']|float) if signal['price'] else '-' }}</td>
                                                <td class="price-column">
                                                    {{ "%.2f"|format(signal['order_price']|float) if signal['order_price'] is not none and signal['order_price']|string != '' else '-' }}
                                                </td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['mean']|float) if signal['mean'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['sl']|float) if signal['sl'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['tp']|float) if signal['tp'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['s2']|float) if signal['s2'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['r2']|float) if signal['r2'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['s2_1']|float) if signal['s2_1'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['r2_1']|float) if signal['r2_1'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['s2_9']|float) if signal['s2_9'] else '-' }}</td>
                                                <td class="signal-level-column">{{ "%.1f"|format(signal['r2_9']|float) if signal['r2_9'] else '-' }}</td>
                                                <td class="status-column">
                                                    {% if signal['processed'] == 1 %}
                                                        <span class="badge bg-success" style="font-size: 0.6rem;">已处理</span>
                                                    {% else %}
                                                        <span class="badge bg-warning" style="font-size: 0.6rem;">未处理</span>
                                                    {% endif %}
                                                </td>
                                                <td class="order-ticket-column">{{ signal['order_ticket'] if signal['order_ticket'] else '-' }}</td>
                                                <td class="order-result-column">
                                                    {% if signal['order_result'] %}
                                                        {% if signal['order_result'] == '成功' %}
                                                            <span class="badge bg-success" style="font-size: 0.6rem;">成功</span>
                                                        {% else %}
                                                            <span class="badge bg-danger" style="font-size: 0.6rem;">失败</span>
                                                        {% endif %}
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td class="action-column">
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ url_for('signal_detail', signal_id=signal['id']) }}"
                                                           class="btn btn-outline-primary btn-sm" style="padding: 0.1rem 0.3rem;">
                                                            <i class="bi bi-eye" style="font-size: 0.7rem;"></i>
                                                        </a>
                                                        {% if signal['processed'] == 0 %}
                                                            <button class="btn btn-outline-success btn-sm process-signal-btn"
                                                                style="padding: 0.1rem 0.3rem;"
                                                                data-signal-id="{{ signal['id'] }}" data-bs-toggle="modal"
                                                                data-bs-target="#processSignalModal">
                                                                <i class="bi bi-play" style="font-size: 0.7rem;"></i>
                                                            </button>
                                                        {% endif %}
                                                        <button class="btn btn-outline-danger btn-sm delete-signal-btn"
                                                            style="padding: 0.1rem 0.3rem;"
                                                            data-signal-id="{{ signal['id'] }}" data-bs-toggle="modal"
                                                            data-bs-target="#deleteSignalModal">
                                                            <i class="bi bi-trash" style="font-size: 0.7rem;"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="21" class="text-center py-3">暂无信号数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <!-- 分页 -->
                        <nav aria-label="信号列表分页">
                            <ul class="pagination justify-content-center mb-0">
                                {% if pagination and pagination.pages > 1 %}
                                    <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                                        <a class="page-link" href="{{ url_for('signals', page=pagination.page-1, **filter_args) }}">上一页</a>
                                    </li>
                                    
                                    {% for p in range(max(1, pagination.page - 2), min(pagination.pages + 1, pagination.page + 3)) %}
                                        <li class="page-item {% if p == pagination.page %}active{% endif %}">
                                            <a class="page-link" href="{{ url_for('signals', page=p, **filter_args) }}">{{ p }}</a>
                                        </li>
                                    {% endfor %}
                                    
                                    <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                                        <a class="page-link" href="{{ url_for('signals', page=pagination.page+1, **filter_args) }}">下一页</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加手动信号弹窗 -->
    <div class="modal fade" id="addSignalModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加手动信号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSignalForm" method="post" action="{{ url_for('add_signal') }}">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="trading_pair" class="form-label">交易对</label>
                                <input type="text" class="form-control" id="trading_pair" name="trading_pair" 
                                    required placeholder="例如：EURUSD">
                            </div>
                            <div class="col-md-6">
                                <label for="signal_type" class="form-label">信号类型</label>
                                <select class="form-select" id="signal_type" name="signal_type" required>
                                    <option value="buy" selected>买入</option>
                                    <option value="sell">卖出</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="price" class="form-label">开仓价格</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                    step="0.00001" required placeholder="开仓价格">
                            </div>
                            <div class="col-md-4">
                                <label for="sl" class="form-label">止损价格</label>
                                <input type="number" class="form-control" id="sl" name="sl" 
                                    step="0.00001" placeholder="止损价格">
                            </div>
                            <div class="col-md-4">
                                <label for="tp" class="form-label">止盈价格</label>
                                <input type="number" class="form-control" id="tp" name="tp" 
                                    step="0.00001" placeholder="止盈价格">
                            </div>
                            <div class="col-md-6">
                                <label for="volume" class="form-label">交易量</label>
                                <input type="number" class="form-control" id="volume" name="volume" 
                                    step="0.01" min="0.01" value="0.01" required placeholder="交易量">
                            </div>
                            <div class="col-md-6">
                                <label for="comment" class="form-label">备注</label>
                                <input type="text" class="form-control" id="comment" name="comment" 
                                    placeholder="备注信息">
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="process_immediately" name="process_immediately" checked>
                                    <label class="form-check-label" for="process_immediately">
                                        立即处理信号（立即开仓）
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="addSignalBtn">添加信号</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 处理信号弹窗 -->
    <div class="modal fade" id="processSignalModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">处理信号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要处理ID为 <span id="processSignalId" class="fw-bold"></span> 的信号吗？</p>
                    <p>处理信号将会在MT5中执行相应的交易操作。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmProcessBtn">确认处理</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除信号弹窗 -->
    <div class="modal fade" id="deleteSignalModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">删除信号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除ID为 <span id="deleteSignalId" class="fw-bold"></span> 的信号吗？</p>
                    <p class="text-danger">警告：此操作无法撤销！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量删除信号弹窗 -->
    <div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量删除信号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除选中的 <span id="batchDeleteCount" class="fw-bold">0</span> 条信号吗？</p>
                    <p class="text-danger">警告：此操作无法撤销！</p>
                    <div id="batchDeleteWarning" class="alert alert-warning d-none">
                        您选择了已处理的信号，删除已处理的信号可能会导致数据不一致。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmBatchDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 刷新按钮
            $('#refreshBtn').click(function() {
                location.reload();
            });

            // 处理信号按钮
            $('.process-signal-btn').click(function() {
                const signalId = $(this).data('signal-id');
                $('#processSignalId').text(signalId);
            });

            // 确认处理信号
            $('#confirmProcessBtn').click(function() {
                const signalId = $('#processSignalId').text();
                
                $.ajax({
                    url: '/process_signal/' + signalId,
                    type: 'POST',
                    success: function(response) {
                        if(response.success) {
                            alert('信号处理成功');
                            location.reload();
                        } else {
                            alert('信号处理失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#processSignalModal').modal('hide');
                    }
                });
            });

            // 删除信号按钮
            $('.delete-signal-btn').click(function() {
                const signalId = $(this).data('signal-id');
                $('#deleteSignalId').text(signalId);
            });

            // 确认删除信号
            $('#confirmDeleteBtn').click(function() {
                const signalId = $('#deleteSignalId').text();
                
                $.ajax({
                    url: '/delete_signal/' + signalId,
                    type: 'POST',
                    success: function(response) {
                        if(response.success) {
                            alert('信号删除成功');
                            location.reload();
                        } else {
                            alert('信号删除失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#deleteSignalModal').modal('hide');
                    }
                });
            });

            // 添加信号
            $('#addSignalBtn').click(function() {
                $('#addSignalForm').submit();
            });

            // 全选/全不选
            $('#selectAllSignals').change(function() {
                $('.signal-checkbox').prop('checked', $(this).is(':checked'));
                updateBatchDeleteCount();
            });

            // 单个选择框变化
            $('.signal-checkbox').change(function() {
                updateBatchDeleteCount();
            });

            // 更新批量删除计数
            function updateBatchDeleteCount() {
                const selectedCount = $('.signal-checkbox:checked').length;
                $('#batchDeleteCount').text(selectedCount);
                
                // 检查是否包含已处理的信号
                const hasProcessed = $('.signal-checkbox:checked').toArray().some(
                    checkbox => $(checkbox).data('processed') == 1
                );
                
                if (hasProcessed) {
                    $('#batchDeleteWarning').removeClass('d-none');
                } else {
                    $('#batchDeleteWarning').addClass('d-none');
                }
                
                // 禁用确认按钮如果没有选择项
                $('#confirmBatchDeleteBtn').prop('disabled', selectedCount === 0);
            }

            // 确认批量删除
            $('#confirmBatchDeleteBtn').click(function() {
                const selectedIds = $('.signal-checkbox:checked').map(function() {
                    return $(this).val();
                }).get();
                
                if (selectedIds.length === 0) {
                    alert('请选择要删除的信号');
                    return;
                }
                
                $.ajax({
                    url: '/batch_delete_signals',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ signal_ids: selectedIds }),
                    success: function(response) {
                        if(response.success) {
                            alert('成功删除 ' + response.deleted_count + ' 条信号');
                            location.reload();
                        } else {
                            alert('批量删除失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#batchDeleteModal').modal('hide');
                    }
                });
            });

            // 导出CSV
            $('#exportCsvBtn').click(function() {
                window.location.href = "{{ url_for('export_signals_csv') }}?" + window.location.search.substring(1);
            });
        });
    </script>
</body>
</html>

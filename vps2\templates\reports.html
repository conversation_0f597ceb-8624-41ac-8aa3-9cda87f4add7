<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 交易报告</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 56px);
        }
        .sidebar a {
            color: #adb5bd;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
        }
        .sidebar a:hover, .sidebar a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar i {
            margin-right: 8px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }
        .stats-card {
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-red {
            background-color: #dc3545;
        }
        .status-yellow {
            background-color: #ffc107;
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">交易报告</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="filterToday">今日</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="filterWeek">本周</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="filterMonth">本月</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary active" id="filterAll">全部</button>
                        </div>
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-info" id="customRangeBtn">自定义时间</button>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="exportBtn">
                            <i class="bi bi-download"></i> 导出报告
                        </button>
                    </div>
                </div>

                <!-- 自定义时间范围选择器 -->
                <div class="row mb-3" id="customDateRange" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-end">
                                    <div class="col-md-3">
                                        <label for="startDate" class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="startDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="endDate" class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="endDate">
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-primary" id="applyCustomRange">应用</button>
                                        <button type="button" class="btn btn-secondary ms-2" id="cancelCustomRange">取消</button>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">注：只统计通过信号执行的交易</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 总体统计卡片 -->
                <div class="row">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2 stats-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总交易数</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_stats['total_orders'] }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-bar-chart h3 text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2 stats-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">盈利交易</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_stats['profitable_orders'] }} ({{ "%.1f"|format(overall_stats['profitable_orders'] / overall_stats['total_orders'] * 100 if overall_stats['total_orders'] > 0 else 0) }}%)</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-graph-up-arrow h3 text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2 stats-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">亏损交易</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ overall_stats['loss_orders'] }} ({{ "%.1f"|format(overall_stats['loss_orders'] / overall_stats['total_orders'] * 100 if overall_stats['total_orders'] > 0 else 0) }}%)</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-graph-down-arrow h3 text-danger"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2 stats-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">净盈亏</div>
                                        <div class="h5 mb-0 font-weight-bold {% if overall_stats['net_profit'] > 0 %}text-success{% elif overall_stats['net_profit'] < 0 %}text-danger{% else %}text-gray-800{% endif %}">
                                            {{ "%.2f"|format(overall_stats['net_profit']|float) }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-cash h3 text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表 -->
                <div class="row">
                    <div class="col-xl-8 col-lg-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">盈亏走势图</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="profitChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-lg-5">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">交易结果分布</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="resultPieChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析图表 -->
                <div class="row mt-4">
                    <div class="col-xl-6 col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">按交易对统计图表</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="symbolChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-6 col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">按交易方向统计图表</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="operationChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按交易对统计 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">按交易对统计</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">交易对</th>
                                                <th scope="col">总交易数</th>
                                                <th scope="col">盈利交易</th>
                                                <th scope="col">亏损交易</th>
                                                <th scope="col">胜率</th>
                                                <th scope="col">净盈亏</th>
                                                <th scope="col">平均盈利</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if symbol_stats %}
                                                {% for stat in symbol_stats %}
                                                    <tr>
                                                        <td>{{ stat['trading_pair'] }}</td>
                                                        <td>{{ stat['total_orders'] }}</td>
                                                        <td class="text-success">{{ stat['profitable_orders'] }}</td>
                                                        <td class="text-danger">{{ stat['loss_orders'] }}</td>
                                                        <td>{{ "%.1f"|format(stat['profitable_orders'] / stat['total_orders'] * 100 if stat['total_orders'] > 0 else 0) }}%</td>
                                                        <td class="{% if stat['net_profit'] > 0 %}text-success{% elif stat['net_profit'] < 0 %}text-danger{% endif %}">
                                                            {{ "%.2f"|format(stat['net_profit']|float) }}
                                                        </td>
                                                        <td class="{% if stat['total_orders'] > 0 and (stat['net_profit'] / stat['total_orders']) > 0 %}text-success{% elif stat['total_orders'] > 0 and (stat['net_profit'] / stat['total_orders']) < 0 %}text-danger{% endif %}">
                                                            {{ "%.2f"|format(stat['net_profit'] / stat['total_orders'] if stat['total_orders'] > 0 else 0) }}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td colspan="7" class="text-center py-3">暂无交易数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按交易类型统计 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">按交易方向统计</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">交易方向</th>
                                                <th scope="col">总交易数</th>
                                                <th scope="col">盈利交易</th>
                                                <th scope="col">亏损交易</th>
                                                <th scope="col">胜率</th>
                                                <th scope="col">净盈亏</th>
                                                <th scope="col">平均盈利</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if operation_stats %}
                                                {% for stat in operation_stats %}
                                                    <tr>
                                                        <td>
                                                            {% if stat['operation'] == 'buy' %}
                                                                <span class="badge bg-success">买入</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">卖出</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ stat['total_orders'] }}</td>
                                                        <td class="text-success">{{ stat['profitable_orders'] }}</td>
                                                        <td class="text-danger">{{ stat['loss_orders'] }}</td>
                                                        <td>{{ "%.1f"|format(stat['profitable_orders'] / stat['total_orders'] * 100 if stat['total_orders'] > 0 else 0) }}%</td>
                                                        <td class="{% if stat['net_profit'] > 0 %}text-success{% elif stat['net_profit'] < 0 %}text-danger{% endif %}">
                                                            {{ "%.2f"|format(stat['net_profit']|float) }}
                                                        </td>
                                                        <td class="{% if stat['total_orders'] > 0 and (stat['net_profit'] / stat['total_orders']) > 0 %}text-success{% elif stat['total_orders'] > 0 and (stat['net_profit'] / stat['total_orders']) < 0 %}text-danger{% endif %}">
                                                            {{ "%.2f"|format(stat['net_profit'] / stat['total_orders'] if stat['total_orders'] > 0 else 0) }}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td colspan="7" class="text-center py-3">暂无交易数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近订单 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">最近平仓的交易</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">ID</th>
                                                <th scope="col">时间</th>
                                                <th scope="col">交易对</th>
                                                <th scope="col">方向</th>
                                                <th scope="col">交易量</th>
                                                <th scope="col">盈亏</th>
                                                <th scope="col">状态</th>
                                                <th scope="col">来源</th>
                                                <th scope="col">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if recent_orders %}
                                                {% for order in recent_orders %}
                                                    <tr>
                                                        <td>{{ order['id'] }}</td>
                                                        <td>{{ order['timestamp'].replace('T', ' ').split('.')[0] if 'T' in order['timestamp'] else order['timestamp'] }}</td>
                                                        <td>{{ order['trading_pair'] }}</td>
                                                        <td>
                                                            {% if order['operation'] == 'buy' %}
                                                                <span class="badge bg-success">买入</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">卖出</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ order['volume'] }}</td>
                                                        <td class="{% if order['profit'] > 0 %}text-success{% elif order['profit'] < 0 %}text-danger{% endif %}">
                                                            {{ "%.2f"|format(order['profit']|float) }}
                                                        </td>
                                                        <td>
                                                            {% if order['status'] == 'open' %}
                                                                <span class="badge bg-success">持仓中</span>
                                                            {% elif order['status'] == 'closed' %}
                                                                <span class="badge bg-secondary">已平仓</span>
                                                            {% elif order['status'] == 'partially_closed' %}
                                                                <span class="badge bg-warning">部分平仓</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            {% if order.get('from_mt5', False) %}
                                                                <span class="badge bg-info">MT5</span>
                                                            {% else %}
                                                                <span class="badge bg-primary">数据库</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            {% if not order.get('from_mt5', False) %}
                                                                <a href="{{ url_for('order_detail', order_id=order['id']) }}" class="btn btn-sm btn-outline-primary">详情</a>
                                                            {% else %}
                                                                <button class="btn btn-sm btn-outline-secondary" disabled>详情</button>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <td colspan="9" class="text-center py-3">暂无交易数据</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- MT5历史交易记录 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">MT5历史交易记录</h5>
                                <div class="btn-group">
                                    <button id="showMT5Deals" class="btn btn-sm btn-outline-primary active">交易记录</button>
                                    <button id="showMT5Orders" class="btn btn-sm btn-outline-primary">订单记录</button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- 加载状态指示器 -->
                                <div id="mt5LoadingIndicator" class="text-center py-3" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载MT5历史记录...</div>
                                </div>

                                <!-- MT5历史交易记录表格 -->
                                <div id="mt5DealsTable" class="table-responsive">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">交易ID</th>
                                                <th scope="col">时间</th>
                                                <th scope="col">订单ID</th>
                                                <th scope="col">交易对</th>
                                                <th scope="col">类型</th>
                                                <th scope="col">方向</th>
                                                <th scope="col">交易量</th>
                                                <th scope="col">价格</th>
                                                <th scope="col">盈亏</th>
                                                <th scope="col">佣金</th>
                                                <th scope="col">掉期点</th>
                                                <th scope="col">交易原因</th>
                                            </tr>
                                        </thead>
                                        <tbody id="mt5DealsTableBody">
                                            <tr>
                                                <td colspan="12" class="text-center py-3">请选择时间范围以加载MT5历史交易记录</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- MT5历史订单记录表格 -->
                                <div id="mt5OrdersTable" class="table-responsive" style="display: none;">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th scope="col">订单ID</th>
                                                <th scope="col">创建时间</th>
                                                <th scope="col">完成时间</th>
                                                <th scope="col">交易对</th>
                                                <th scope="col">类型</th>
                                                <th scope="col">状态</th>
                                                <th scope="col">初始交易量</th>
                                                <th scope="col">当前交易量</th>
                                                <th scope="col">开仓价</th>
                                                <th scope="col">止损</th>
                                                <th scope="col">止盈</th>
                                                <th scope="col">订单原因</th>
                                            </tr>
                                        </thead>
                                        <tbody id="mt5OrdersTableBody">
                                            <tr>
                                                <td colspan="12" class="text-center py-3">请选择时间范围以加载MT5历史订单记录</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页控件 -->
                                <div id="mt5Pagination" class="d-flex justify-content-between align-items-center p-3" style="display: none;">
                                    <div class="text-muted">
                                        显示第 <span id="mt5CurrentStart">0</span> - <span id="mt5CurrentEnd">0</span> 条，共 <span id="mt5Total">0</span> 条记录
                                    </div>
                                    <nav aria-label="MT5历史记录分页">
                                        <ul class="pagination pagination-sm mb-0" id="mt5PaginationList">
                                            <!-- 分页按钮将通过JavaScript动态生成 -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 切换MT5历史记录表格
            $("#showMT5Deals").click(function() {
                $(this).addClass("active");
                $("#showMT5Orders").removeClass("active");
                currentMT5RecordType = 'deals';
                // 重新加载当前时间范围的数据
                loadMT5History(currentMT5Period, currentMT5StartDate, currentMT5EndDate, 1);
            });

            $("#showMT5Orders").click(function() {
                $(this).addClass("active");
                $("#showMT5Deals").removeClass("active");
                currentMT5RecordType = 'orders';
                // 重新加载当前时间范围的数据
                loadMT5History(currentMT5Period, currentMT5StartDate, currentMT5EndDate, 1);
            });
        
            // 盈亏走势图
            const ctxProfit = document.getElementById('profitChart').getContext('2d');
            const profitChart = new Chart(ctxProfit, {
                type: 'line',
                data: {
                    labels: ['示例'],  // 这里会在API调用中被替换
                    datasets: [{
                        label: '累计盈亏',
                        data: [0],  // 这里会在API调用中被替换
                        fill: false,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });

            // 交易结果饼图
            const ctxPie = document.getElementById('resultPieChart').getContext('2d');
            const resultPieChart = new Chart(ctxPie, {
                type: 'pie',
                data: {
                    labels: ['盈利交易', '亏损交易'],
                    datasets: [{
                        label: '交易结果',
                        data: [
                            {{ overall_stats['profitable_orders'] }},
                            {{ overall_stats['loss_orders'] }}
                        ],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 按交易对统计柱状图
            const ctxSymbol = document.getElementById('symbolChart').getContext('2d');
            const symbolChart = new Chart(ctxSymbol, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '盈利交易',
                        data: [],
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }, {
                        label: '亏损交易',
                        data: [],
                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    // 动态获取当前数据
                                    var symbolIndex = context.dataIndex;
                                    var labels = symbolChart.data.labels;
                                    if (labels[symbolIndex] && window.currentSymbolStats) {
                                        var stat = window.currentSymbolStats[symbolIndex];
                                        if (stat) {
                                            var winRate = stat.total_orders > 0 ? (stat.profitable_orders / stat.total_orders * 100).toFixed(1) : '0.0';
                                            return '净盈亏: ' + stat.net_profit.toFixed(2) + '\n胜率: ' + winRate + '%';
                                        }
                                    }
                                    return '';
                                }
                            }
                        }
                    }
                }
            });

            // 按交易方向统计饼图
            const ctxOperation = document.getElementById('operationChart').getContext('2d');
            const operationChart = new Chart(ctxOperation, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        label: '交易数量',
                        data: [],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.6)',  // 绿色 - 买入
                            'rgba(239, 68, 68, 0.6)'   // 红色 - 卖出
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    // 动态获取当前数据
                                    if (window.currentOperationStats && window.currentOperationStats[context.dataIndex]) {
                                        var stat = window.currentOperationStats[context.dataIndex];
                                        var winRate = stat.total_orders > 0 ? (stat.profitable_orders / stat.total_orders * 100).toFixed(1) : '0.0';
                                        return [
                                            context.label + ': ' + context.parsed + ' 笔',
                                            '胜率: ' + winRate + '%',
                                            '净盈亏: ' + stat.net_profit.toFixed(2)
                                        ];
                                    }
                                    return context.label + ': ' + context.parsed + ' 笔';
                                }
                            }
                        }
                    }
                }
            });

            // 加载盈亏走势数据
            function loadProfitData(period, startDate, endDate) {
                var requestData = { period: period };
                if (period === 'custom' && startDate && endDate) {
                    requestData.start_date = startDate;
                    requestData.end_date = endDate;
                }

                $.ajax({
                    url: '/api/profit_history',
                    type: 'GET',
                    data: requestData,
                    dataType: 'json',
                    success: function(data) {
                        if(data.success) {
                            // 更新图表数据
                            profitChart.data.labels = data.dates;
                            profitChart.data.datasets[0].data = data.profits;
                            profitChart.update();
                        }
                    }
                });
            }

            // 加载统计数据
            function loadStatsData(period, startDate, endDate) {
                var requestData = { period: period };
                if (period === 'custom' && startDate && endDate) {
                    requestData.start_date = startDate;
                    requestData.end_date = endDate;
                }

                $.ajax({
                    url: '/api/report_stats',
                    type: 'GET',
                    data: requestData,
                    dataType: 'json',
                    success: function(data) {
                        if(data.success) {
                            updateStatsDisplay(data);
                        }
                    }
                });
            }

            // 更新统计数据显示
            function updateStatsDisplay(data) {
                var stats = data.overall_stats;

                // 更新总体统计卡片
                $('.stats-card').eq(0).find('.h5').text(stats.total_orders);
                $('.stats-card').eq(1).find('.h5').text(stats.profitable_orders + ' (' +
                    (stats.total_orders > 0 ? (stats.profitable_orders / stats.total_orders * 100).toFixed(1) : '0.0') + '%)');
                $('.stats-card').eq(2).find('.h5').text(stats.loss_orders + ' (' +
                    (stats.total_orders > 0 ? (stats.loss_orders / stats.total_orders * 100).toFixed(1) : '0.0') + '%)');
                $('.stats-card').eq(3).find('.h5').text(stats.net_profit.toFixed(2))
                    .removeClass('text-success text-danger text-gray-800')
                    .addClass(stats.net_profit > 0 ? 'text-success' : (stats.net_profit < 0 ? 'text-danger' : 'text-gray-800'));

                // 更新饼图
                resultPieChart.data.datasets[0].data = [stats.profitable_orders, stats.loss_orders];
                resultPieChart.update();

                // 更新按交易对统计图表
                updateSymbolChart(data.symbol_stats);

                // 更新按交易方向统计图表
                updateOperationChart(data.operation_stats);

                // 更新按交易对统计表格
                updateSymbolStatsTable(data.symbol_stats);

                // 更新按交易方向统计表格
                updateOperationStatsTable(data.operation_stats);
            }

            // 更新按交易对统计图表
            function updateSymbolChart(symbolStats) {
                var labels = [];
                var profitableData = [];
                var lossData = [];

                symbolStats.forEach(function(stat) {
                    labels.push(stat.trading_pair);
                    profitableData.push(stat.profitable_orders);
                    lossData.push(stat.loss_orders);
                });

                // 存储当前数据供tooltip使用
                window.currentSymbolStats = symbolStats;

                symbolChart.data.labels = labels;
                symbolChart.data.datasets[0].data = profitableData;
                symbolChart.data.datasets[1].data = lossData;
                symbolChart.update();
            }

            // 更新按交易方向统计图表
            function updateOperationChart(operationStats) {
                var labels = [];
                var data = [];
                var backgroundColors = [];
                var borderColors = [];

                operationStats.forEach(function(stat) {
                    labels.push(stat.operation === 'buy' ? '买入' : '卖出');
                    data.push(stat.total_orders);

                    // 根据操作类型设置颜色
                    if (stat.operation === 'buy') {
                        backgroundColors.push('rgba(34, 197, 94, 0.6)');  // 绿色 - 买入
                        borderColors.push('rgba(34, 197, 94, 1)');
                    } else {
                        backgroundColors.push('rgba(239, 68, 68, 0.6)');   // 红色 - 卖出
                        borderColors.push('rgba(239, 68, 68, 1)');
                    }
                });

                // 存储当前数据供tooltip使用
                window.currentOperationStats = operationStats;

                operationChart.data.labels = labels;
                operationChart.data.datasets[0].data = data;
                operationChart.data.datasets[0].backgroundColor = backgroundColors;
                operationChart.data.datasets[0].borderColor = borderColors;
                operationChart.update();
            }

            // 更新按交易对统计表格
            function updateSymbolStatsTable(symbolStats) {
                var tbody = $('table').eq(0).find('tbody');
                tbody.empty();

                if (symbolStats.length > 0) {
                    symbolStats.forEach(function(stat) {
                        var winRate = stat.total_orders > 0 ? (stat.profitable_orders / stat.total_orders * 100).toFixed(1) : '0.0';
                        var avgProfit = stat.total_orders > 0 ? (stat.net_profit / stat.total_orders).toFixed(2) : '0.00';
                        var profitClass = stat.net_profit > 0 ? 'text-success' : (stat.net_profit < 0 ? 'text-danger' : '');
                        var avgProfitClass = parseFloat(avgProfit) > 0 ? 'text-success' : (parseFloat(avgProfit) < 0 ? 'text-danger' : '');

                        tbody.append(
                            '<tr>' +
                            '<td>' + stat.trading_pair + '</td>' +
                            '<td>' + stat.total_orders + '</td>' +
                            '<td class="text-success">' + stat.profitable_orders + '</td>' +
                            '<td class="text-danger">' + stat.loss_orders + '</td>' +
                            '<td>' + winRate + '%</td>' +
                            '<td class="' + profitClass + '">' + stat.net_profit.toFixed(2) + '</td>' +
                            '<td class="' + avgProfitClass + '">' + avgProfit + '</td>' +
                            '</tr>'
                        );
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center py-3">暂无交易数据</td></tr>');
                }
            }

            // 更新按交易方向统计表格
            function updateOperationStatsTable(operationStats) {
                var tbody = $('table').eq(1).find('tbody');
                tbody.empty();

                if (operationStats.length > 0) {
                    operationStats.forEach(function(stat) {
                        var winRate = stat.total_orders > 0 ? (stat.profitable_orders / stat.total_orders * 100).toFixed(1) : '0.0';
                        var avgProfit = stat.total_orders > 0 ? (stat.net_profit / stat.total_orders).toFixed(2) : '0.00';
                        var profitClass = stat.net_profit > 0 ? 'text-success' : (stat.net_profit < 0 ? 'text-danger' : '');
                        var avgProfitClass = parseFloat(avgProfit) > 0 ? 'text-success' : (parseFloat(avgProfit) < 0 ? 'text-danger' : '');
                        var operationBadge = stat.operation === 'buy' ? '<span class="badge bg-success">买入</span>' : '<span class="badge bg-danger">卖出</span>';

                        tbody.append(
                            '<tr>' +
                            '<td>' + operationBadge + '</td>' +
                            '<td>' + stat.total_orders + '</td>' +
                            '<td class="text-success">' + stat.profitable_orders + '</td>' +
                            '<td class="text-danger">' + stat.loss_orders + '</td>' +
                            '<td>' + winRate + '%</td>' +
                            '<td class="' + profitClass + '">' + stat.net_profit.toFixed(2) + '</td>' +
                            '<td class="' + avgProfitClass + '">' + avgProfit + '</td>' +
                            '</tr>'
                        );
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center py-3">暂无交易数据</td></tr>');
                }
            }

            // MT5历史记录相关变量
            var currentMT5RecordType = 'deals';  // 当前显示的记录类型
            var currentMT5Page = 1;              // 当前页码
            var currentMT5Period = 'all';        // 当前时间范围
            var currentMT5StartDate = '';        // 当前开始日期
            var currentMT5EndDate = '';          // 当前结束日期

            // 加载MT5历史记录
            function loadMT5History(period, startDate, endDate, page) {
                page = page || 1;
                currentMT5Page = page;
                currentMT5Period = period;
                currentMT5StartDate = startDate;
                currentMT5EndDate = endDate;

                var requestData = {
                    type: currentMT5RecordType,
                    period: period,
                    page: page,
                    per_page: 30
                };

                if (period === 'custom' && startDate && endDate) {
                    requestData.start_date = startDate;
                    requestData.end_date = endDate;
                }

                // 显示加载指示器
                $('#mt5LoadingIndicator').show();
                $('#mt5DealsTable, #mt5OrdersTable').hide();
                $('#mt5Pagination').hide();

                $.ajax({
                    url: '/api/mt5_history',
                    type: 'GET',
                    data: requestData,
                    dataType: 'json',
                    success: function(data) {
                        $('#mt5LoadingIndicator').hide();

                        if (data.success) {
                            updateMT5Table(data);
                            updateMT5Pagination(data);
                        } else {
                            showMT5Error(data.error || '加载失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#mt5LoadingIndicator').hide();
                        showMT5Error('网络错误: ' + error);
                    }
                });
            }

            // 更新MT5表格数据
            function updateMT5Table(data) {
                var tbody = currentMT5RecordType === 'deals' ? $('#mt5DealsTableBody') : $('#mt5OrdersTableBody');
                tbody.empty();

                if (data.data && data.data.length > 0) {
                    data.data.forEach(function(record) {
                        var row = '';
                        if (currentMT5RecordType === 'deals') {
                            row = buildDealRow(record);
                        } else {
                            row = buildOrderRow(record);
                        }
                        tbody.append(row);
                    });
                } else {
                    var colCount = currentMT5RecordType === 'deals' ? 12 : 12;
                    tbody.append('<tr><td colspan="' + colCount + '" class="text-center py-3">暂无记录</td></tr>');
                }

                // 显示对应的表格
                if (currentMT5RecordType === 'deals') {
                    $('#mt5DealsTable').show();
                    $('#mt5OrdersTable').hide();
                } else {
                    $('#mt5DealsTable').hide();
                    $('#mt5OrdersTable').show();
                }
            }

            // 构建交易记录行
            function buildDealRow(deal) {
                var profitClass = '';
                if (deal.profit > 0) profitClass = 'text-success';
                else if (deal.profit < 0) profitClass = 'text-danger';

                var time = deal.time ? deal.time.replace('T', ' ').split('.')[0] : '';

                return '<tr>' +
                    '<td>' + (deal.deal || '') + '</td>' +
                    '<td>' + time + '</td>' +
                    '<td>' + (deal.order || '') + '</td>' +
                    '<td>' + (deal.symbol || '') + '</td>' +
                    '<td>' + (deal.type_str || '') + '</td>' +
                    '<td>' + (deal.entry_str || '') + '</td>' +
                    '<td>' + (deal.volume || '') + '</td>' +
                    '<td>' + (deal.price || '') + '</td>' +
                    '<td class="' + profitClass + '">' + (deal.profit ? parseFloat(deal.profit).toFixed(2) : '0.00') + '</td>' +
                    '<td>' + (deal.commission ? parseFloat(deal.commission).toFixed(2) : '0.00') + '</td>' +
                    '<td>' + (deal.swap ? parseFloat(deal.swap).toFixed(2) : '0.00') + '</td>' +
                    '<td>' + (deal.reason_str || '') + '</td>' +
                    '</tr>';
            }

            // 构建订单记录行
            function buildOrderRow(order) {
                var timeSetup = order.time_setup ? order.time_setup.replace('T', ' ').split('.')[0] : '';
                var timeDone = order.time_done ? order.time_done.replace('T', ' ').split('.')[0] : '未完成';

                return '<tr>' +
                    '<td>' + (order.ticket || '') + '</td>' +
                    '<td>' + timeSetup + '</td>' +
                    '<td>' + timeDone + '</td>' +
                    '<td>' + (order.symbol || '') + '</td>' +
                    '<td>' + (order.type_str || '') + '</td>' +
                    '<td>' + (order.state_str || '') + '</td>' +
                    '<td>' + (order.volume_initial || '') + '</td>' +
                    '<td>' + (order.volume_current || '') + '</td>' +
                    '<td>' + (order.price_open || '') + '</td>' +
                    '<td>' + (order.sl || '') + '</td>' +
                    '<td>' + (order.tp || '') + '</td>' +
                    '<td>' + (order.reason_str || '') + '</td>' +
                    '</tr>';
            }

            // 更新分页控件
            function updateMT5Pagination(data) {
                if (data.total > 0) {
                    $('#mt5Pagination').show();

                    // 更新统计信息
                    var start = (data.page - 1) * data.per_page + 1;
                    var end = Math.min(data.page * data.per_page, data.total);
                    $('#mt5CurrentStart').text(start);
                    $('#mt5CurrentEnd').text(end);
                    $('#mt5Total').text(data.total);

                    // 生成分页按钮
                    var paginationList = $('#mt5PaginationList');
                    paginationList.empty();

                    // 上一页按钮
                    var prevDisabled = data.page <= 1 ? 'disabled' : '';
                    var prevBtn = $('<li class="page-item ' + prevDisabled + '"><a class="page-link" href="#">上一页</a></li>');
                    if (data.page > 1) {
                        prevBtn.find('a').click(function(e) {
                            e.preventDefault();
                            loadMT5History(currentMT5Period, currentMT5StartDate, currentMT5EndDate, data.page - 1);
                        });
                    }
                    paginationList.append(prevBtn);

                    // 页码按钮
                    var startPage = Math.max(1, data.page - 2);
                    var endPage = Math.min(data.total_pages, data.page + 2);

                    for (var i = startPage; i <= endPage; i++) {
                        var activeClass = i === data.page ? 'active' : '';
                        var pageBtn = $('<li class="page-item ' + activeClass + '"><a class="page-link" href="#">' + i + '</a></li>');
                        (function(pageNum) {
                            pageBtn.find('a').click(function(e) {
                                e.preventDefault();
                                loadMT5History(currentMT5Period, currentMT5StartDate, currentMT5EndDate, pageNum);
                            });
                        })(i);
                        paginationList.append(pageBtn);
                    }

                    // 下一页按钮
                    var nextDisabled = data.page >= data.total_pages ? 'disabled' : '';
                    var nextBtn = $('<li class="page-item ' + nextDisabled + '"><a class="page-link" href="#">下一页</a></li>');
                    if (data.page < data.total_pages) {
                        nextBtn.find('a').click(function(e) {
                            e.preventDefault();
                            loadMT5History(currentMT5Period, currentMT5StartDate, currentMT5EndDate, data.page + 1);
                        });
                    }
                    paginationList.append(nextBtn);
                } else {
                    $('#mt5Pagination').hide();
                }
            }

            // 显示MT5错误信息
            function showMT5Error(message) {
                var tbody = currentMT5RecordType === 'deals' ? $('#mt5DealsTableBody') : $('#mt5OrdersTableBody');
                var colCount = 12;
                tbody.empty().append('<tr><td colspan="' + colCount + '" class="text-center py-3 text-danger">错误: ' + message + '</td></tr>');

                if (currentMT5RecordType === 'deals') {
                    $('#mt5DealsTable').show();
                    $('#mt5OrdersTable').hide();
                } else {
                    $('#mt5DealsTable').hide();
                    $('#mt5OrdersTable').show();
                }
                $('#mt5Pagination').hide();
            }

            // 加载数据的通用函数
            function loadData(period, startDate, endDate) {
                loadProfitData(period, startDate, endDate);
                loadStatsData(period, startDate, endDate);
                loadMT5History(period, startDate, endDate, 1);  // 加载MT5历史记录，重置到第1页
            }

            // 初始化图表数据
            var initialSymbolStats = [{% for stat in symbol_stats %}{ trading_pair: '{{ stat.trading_pair }}', total_orders: {{ stat.total_orders }}, profitable_orders: {{ stat.profitable_orders }}, loss_orders: {{ stat.loss_orders }}, net_profit: {{ stat.net_profit }} }{% if not loop.last %},{% endif %}{% endfor %}];
            var initialOperationStats = [{% for stat in operation_stats %}{ operation: '{{ stat.operation }}', total_orders: {{ stat.total_orders }}, profitable_orders: {{ stat.profitable_orders }}, loss_orders: {{ stat.loss_orders }}, net_profit: {{ stat.net_profit }} }{% if not loop.last %},{% endif %}{% endfor %}];

            updateSymbolChart(initialSymbolStats);
            updateOperationChart(initialOperationStats);

            // 初始加载全部数据
            loadData('all');

            // 时间筛选按钮
            $('#filterToday').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                $('#customDateRange').hide();
                loadData('today');
            });

            $('#filterWeek').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                $('#customDateRange').hide();
                loadData('week');
            });

            $('#filterMonth').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                $('#customDateRange').hide();
                loadData('month');
            });

            $('#filterAll').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                $('#customDateRange').hide();
                loadData('all');
            });

            // 自定义时间范围
            $('#customRangeBtn').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                $('#customDateRange').show();

                // 设置默认日期（最近30天）
                var endDate = new Date();
                var startDate = new Date();
                startDate.setDate(startDate.getDate() - 30);

                $('#startDate').val(startDate.toISOString().split('T')[0]);
                $('#endDate').val(endDate.toISOString().split('T')[0]);
            });

            $('#applyCustomRange').click(function() {
                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();

                if (!startDate || !endDate) {
                    alert('请选择开始日期和结束日期');
                    return;
                }

                if (startDate > endDate) {
                    alert('开始日期不能晚于结束日期');
                    return;
                }

                loadData('custom', startDate, endDate);
            });

            $('#cancelCustomRange').click(function() {
                $('#customDateRange').hide();
                $('.btn-group .btn').removeClass('active');
                $('#filterAll').addClass('active');
                loadData('all');
            });

            // 导出报告
            $('#exportBtn').click(function() {
                window.location.href = '/export_report';
            });
        });
    </script>
</body>
</html>

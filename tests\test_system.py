"""
系统集成测试
"""
import unittest
import tempfile
import os
import json
from unittest.mock import patch, MagicMock
from app import create_app
from database.manager import DatabaseManager

class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False)
        self.temp_db.close()
        
        # 设置测试环境
        os.environ['DATABASE_PATH'] = self.temp_db.name
        os.environ['TESTING_MODE'] = 'True'
        
        # 创建测试应用
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # 初始化数据库
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.init_database()
    
    def tearDown(self):
        """测试后清理"""
        self.app_context.pop()
        os.unlink(self.temp_db.name)
    
    def test_health_check(self):
        """测试系统健康检查"""
        response = self.client.get('/api/system/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
        self.assertIn('overall_health', data['data'])
    
    def test_login_flow(self):
        """测试登录流程"""
        # 测试登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = self.client.post('/api/auth/login', 
                                  data=json.dumps(login_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('session_token', data)
        
        # 使用token访问受保护的端点
        token = data['session_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        response = self.client.get('/api/auth/validate', headers=headers)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['valid'])
    
    @patch('webhook.handler.trading_strategy')
    def test_webhook_endpoint(self, mock_strategy):
        """测试Webhook端点"""
        mock_strategy.execute_strategy.return_value = {
            "success": True,
            "message": "策略执行成功"
        }
        
        alert_data = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m",
            "price": 2000.0
        }
        
        response = self.client.post('/webhook/tradingview',
                                  data=json.dumps(alert_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    def test_config_management(self):
        """测试配置管理"""
        # 先登录获取token
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = self.client.post('/api/auth/login', 
                                  data=json.dumps(login_data),
                                  content_type='application/json')
        token = json.loads(response.data)['session_token']
        headers = {'Authorization': f'Bearer {token}'}
        
        # 测试获取交易对配置
        response = self.client.get('/api/config/symbols', headers=headers)
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
        
        # 测试更新交易对配置
        config_data = {
            "enabled": True,
            "lot_size": 0.02,
            "stop_loss_usd": 60.0,
            "take_profit_usd": 120.0,
            "signal_timeout": 200,
            "category": "test"
        }
        
        response = self.client.put('/api/config/symbols/XAUUSD',
                                 data=json.dumps(config_data),
                                 content_type='application/json',
                                 headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])

class TestSystemComponents(unittest.TestCase):
    """系统组件测试类"""
    
    def test_symbol_normalization(self):
        """测试交易对标准化"""
        from utils.helpers import normalize_symbol
        
        test_cases = [
            ("XAUUSD", "XAUUSD"),
            ("ETHUSD", "ETHUSD"),
            ("ETHUSDT", "ETHUSD"),
            ("ETH-USD", "ETHUSD"),
            ("eth/usd", "ETHUSD"),
            ("BTC_USD", "BTCUSD"),
        ]
        
        for input_symbol, expected in test_cases:
            with self.subTest(input_symbol=input_symbol):
                result = normalize_symbol(input_symbol)
                self.assertEqual(result, expected)
    
    def test_signal_normalization(self):
        """测试信号标准化"""
        from utils.helpers import normalize_signal_direction
        
        buy_signals = ["BUY", "buy", "LONG", "long", "UP", "买入", "做多", "综合做多信号"]
        sell_signals = ["SELL", "sell", "SHORT", "short", "DOWN", "卖出", "做空", "综合做空信号"]
        
        for signal in buy_signals:
            with self.subTest(signal=signal):
                result = normalize_signal_direction(signal)
                self.assertEqual(result, "BUY")
        
        for signal in sell_signals:
            with self.subTest(signal=signal):
                result = normalize_signal_direction(signal)
                self.assertEqual(result, "SELL")
    
    def test_alert_validation(self):
        """测试警报验证"""
        from utils.helpers import validate_alert_data
        
        # 有效警报
        valid_alert = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m"
        }
        
        is_valid, error = validate_alert_data(valid_alert)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # 无效警报 - 缺少字段
        invalid_alert = {
            "symbol": "XAUUSD"
            # 缺少 signal_direction 和 timeframe
        }
        
        is_valid, error = validate_alert_data(invalid_alert)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    @patch('mt5.connection.mt5')
    def test_mt5_connection(self, mock_mt5):
        """测试MT5连接"""
        from mt5.connection import MT5Connection
        
        # 模拟MT5连接成功
        mock_mt5.initialize.return_value = True
        mock_mt5.login.return_value = True
        mock_mt5.account_info.return_value = MagicMock(
            login=12345,
            server="TestServer",
            balance=10000.0,
            equity=10000.0
        )
        
        connection = MT5Connection()
        result = connection.connect(12345, "password", "server")
        
        self.assertTrue(result)
        self.assertTrue(connection.is_connected)

class TestErrorHandling(unittest.TestCase):
    """错误处理测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.app = create_app('testing')
        self.client = self.app.test_client()
    
    def test_invalid_webhook_data(self):
        """测试无效Webhook数据"""
        invalid_data = {
            "invalid": "data"
        }
        
        response = self.client.post('/webhook/tradingview',
                                  data=json.dumps(invalid_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('error', data)
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        response = self.client.get('/api/config/symbols')
        self.assertEqual(response.status_code, 401)
    
    def test_invalid_login(self):
        """测试无效登录"""
        login_data = {
            'username': 'invalid',
            'password': 'invalid'
        }
        
        response = self.client.post('/api/auth/login',
                                  data=json.dumps(login_data),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])

if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)

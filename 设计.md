# TradingView Pine脚本自动交易系统设计文档

## 原始需求（保持不变）

1、3.pine是运行在tradingview上的策略，会市场强烈的时候发出综合警报，但是没有转化为交易执行系统，我需要你设计和修改下综合警报的通知内容（已存在的内容不要修改），添加关键的json，用来接下来实际执行交易的系统来使用；

2、运行在windows11的metatrader5交易程序，需要使用python编写一个系统，用来执行3.pine发出的综合警报的交易，具体方案如下;

3、程序分为及几部分：webhook接收服务（用于接收tradingview 3.pine发出的综合警报）、警报通知记录和存储、警报的交易策略和订单执行（当系统接收到tradingview该指标发出的某交易对的1分钟时间周期综合信号警报时，下对应方向的订单，随后系统收到的每条综合警报（不再继续下单）作为是否继续持有该订单的依据，即第一次出现信号则下单，随后的警报的信号不下单，但是随后接收同样的警报（比如某交易对（交易对可以配置）第一次收到1分钟时间周期的”综合做空信号“，下XX手（web页面可以配置，并且区分交易对，不同交易对可以设置不同的手数）空单，该空单需要自动设置止盈止损（web页面可以配置，并且区分交易对，不同交易对可以设置不同的止损点位和止盈点位），当系统接下来3分钟内（这个延迟时间web页面可以修改和配置）再次收到该交易对的“综合做空信号”时表面该订单需要继续持有）与否需要用来确认这个订单是否需要平仓或继续持有，由于警报需要时间产生，即可以延迟3分钟（这个延迟时间web页面可以修改和配置），即3分钟没有继续出现新的同交易对同方向的综合警报，则系统自动将该订单平仓（并且web页面可以看到平仓原因：XX时间内未收到综合警报），如果3分钟内继续出现新的警报则继续持有（持仓至直到没有再收到+附加延迟的时间（举例使用的3分钟，web页面可以配置））。

4、可以参考成熟交易系统的代码

## 系统设计补充

### 1. TradingView警报JSON格式设计

为了支持自动交易系统，需要在现有的综合警报内容基础上添加以下JSON格式（**已存在的内容不要修改**）：

```json
{
  "alert_type": "综合信号",
  "symbol": "SOLUSDT",
  "timeframe": "1m",
  "signal_direction": "BUY",
  "signal_strength": "强",
  "timestamp": "2024-01-01T12:00:00Z",
  "price": 145.50,
  "alert_id": "unique_alert_id_12345",
  "source": "3.pine",
  "additional_data": {
    "rsi": 35.5,
    "macd": 0.0012,
    "volume": 1250000
  }
}
```

#### 1.1 信号方向标准化映射

系统支持多种信号格式，自动映射为标准格式：

```python
# 信号方向映射表（支持大小写）
SIGNAL_DIRECTION_MAPPING = {
    # 做多信号映射 - 大写
    "BUY": "BUY",
    "LONG": "BUY",
    "UP": "BUY",

    # 做多信号映射 - 小写
    "buy": "BUY",
    "long": "BUY",
    "up": "BUY",

    # 做多信号映射 - 中文
    "买入": "BUY",
    "做多": "BUY",
    "综合做多信号": "BUY",
    "多": "BUY",

    # 做空信号映射 - 大写
    "SELL": "SELL",
    "SHORT": "SELL",
    "DOWN": "SELL",

    # 做空信号映射 - 小写
    "sell": "SELL",
    "short": "SELL",
    "down": "SELL",

    # 做空信号映射 - 中文
    "卖出": "SELL",
    "做空": "SELL",
    "综合做空信号": "SELL",
    "空": "SELL"
}

def normalize_signal_direction(signal):
    """
    标准化信号方向（支持大小写）

    Args:
        signal: 原始信号方向

    Returns:
        标准化的信号方向 (BUY/SELL)
    """
    # 先去除空格，保持原始大小写进行匹配
    signal_clean = str(signal).strip()

    # 直接匹配（保持大小写）
    if signal_clean in SIGNAL_DIRECTION_MAPPING:
        return SIGNAL_DIRECTION_MAPPING[signal_clean]

    # 如果直接匹配失败，尝试大写匹配（兼容性处理）
    signal_upper = signal_clean.upper()
    return SIGNAL_DIRECTION_MAPPING.get(signal_upper, signal_upper)

# 使用示例
# normalize_signal_direction("buy") -> "BUY"
# normalize_signal_direction("BUY") -> "BUY"
# normalize_signal_direction("sell") -> "SELL"
# normalize_signal_direction("SELL") -> "SELL"
# normalize_signal_direction("综合做空信号") -> "SELL"
# normalize_signal_direction("做多") -> "BUY"
```

### 2. 系统架构组件

- **Webhook接收服务**: 接收TradingView警报
- **数据存储层**: 警报历史记录和配置存储
- **交易执行引擎**: MetaTrader5订单管理
- **Web管理界面**: 配置和监控面板
- **通知服务**: Bark推送系统

### 3. 交易执行逻辑流程

```
接收警报 → 验证信号 → 检查冷却时间 → 执行交易 → 设置止盈止损 → 监控后续信号 → 自动平仓
```

### 4. 配置管理结构

#### 4.1 主要交易对列表和映射机制
系统支持以下主要交易对，并自动处理不同后缀的映射：

**贵金属**:
- XAUUSD (黄金)

**加密货币** (支持USD/USDT自动映射):
- ETHUSD ← 映射: ETHUSD, ETHUSDT, ETH-USD, ETH/USD
- BTCUSD ← 映射: BTCUSD, BTCUSDT, BTC-USD, BTC/USD
- SOLUSD ← 映射: SOLUSD, SOLUSDT, SOL-USD, SOL/USD
- BCHUSD ← 映射: BCHUSD, BCHUSDT, BCH-USD, BCH/USD
- ADAUSD ← 映射: ADAUSD, ADAUSDT, ADA-USD, ADA/USD
- XLMUSD ← 映射: XLMUSD, XLMUSDT, XLM-USD, XLM/USD
- DOGEUSD ← 映射: DOGEUSD, DOGEUSDT, DOGE-USD, DOGE/USD
- LINKUSD ← 映射: LINKUSD, LINKUSDT, LINK-USD, LINK/USD
- LTCUSD ← 映射: LTCUSD, LTCUSDT, LTC-USD, LTC/USD
- XRPUSD ← 映射: XRPUSD, XRPUSDT, XRP-USD, XRP/USD

**外汇**:
- GBPUSD ← 映射: GBPUSD, GBP-USD, GBP/USD
- GBPJPY ← 映射: GBPJPY, GBP-JPY, GBP/JPY

#### 4.2 交易对映射配置
```python
# 交易对映射字典
SYMBOL_MAPPING = {
    # 加密货币映射 (统一为USD后缀)
    "ETHUSD": ["ETHUSD", "ETHUSDT", "ETH-USD", "ETH/USD", "ETH_USD"],
    "BTCUSD": ["BTCUSD", "BTCUSDT", "BTC-USD", "BTC/USD", "BTC_USD"],
    "SOLUSD": ["SOLUSD", "SOLUSDT", "SOL-USD", "SOL/USD", "SOL_USD"],
    "BCHUSD": ["BCHUSD", "BCHUSDT", "BCH-USD", "BCH/USD", "BCH_USD"],
    "ADAUSD": ["ADAUSD", "ADAUSDT", "ADA-USD", "ADA/USD", "ADA_USD"],
    "XLMUSD": ["XLMUSD", "XLMUSDT", "XLM-USD", "XLM/USD", "XLM_USD"],
    "DOGEUSD": ["DOGEUSD", "DOGEUSDT", "DOGE-USD", "DOGE/USD", "DOGE_USD"],
    "LINKUSD": ["LINKUSD", "LINKUSDT", "LINK-USD", "LINK/USD", "LINK_USD"],
    "LTCUSD": ["LTCUSD", "LTCUSDT", "LTC-USD", "LTC/USD", "LTC_USD"],
    "XRPUSD": ["XRPUSD", "XRPUSDT", "XRP-USD", "XRP/USD", "XRP_USD"],

    # 贵金属映射
    "XAUUSD": ["XAUUSD", "XAU-USD", "XAU/USD", "GOLD", "GOLDUSD"],

    # 外汇映射
    "GBPUSD": ["GBPUSD", "GBP-USD", "GBP/USD", "GBP_USD"],
    "GBPJPY": ["GBPJPY", "GBP-JPY", "GBP/JPY", "GBP_JPY"],
}

# 反向映射字典 (用于快速查找)
REVERSE_SYMBOL_MAPPING = {}
for standard_symbol, variants in SYMBOL_MAPPING.items():
    for variant in variants:
        REVERSE_SYMBOL_MAPPING[variant.upper()] = standard_symbol

def normalize_symbol(symbol):
    """
    标准化交易对符号

    Args:
        symbol: 原始交易对符号

    Returns:
        标准化的交易对符号
    """
    symbol_upper = symbol.upper().strip()
    return REVERSE_SYMBOL_MAPPING.get(symbol_upper, symbol_upper)

# 使用示例
# normalize_symbol("SOLUSDT") -> "SOLUSD"
# normalize_symbol("ETH-USD") -> "ETHUSD"
# normalize_symbol("btcusdt") -> "BTCUSD"
```

#### 4.3 专业下单系统设计

参考成熟的交易系统下单命令和订单填充方式，系统采用以下下单机制：

```python
# 专业交易系统的下单参数结构
class ProfessionalOrderParams:
    def __init__(self):
        self.symbol = ""           # 交易对
        self.action = ""           # BUY 或 SELL
        self.volume = 0.0          # 手数
        self.price = 0.0           # 价格 (市价单为0)
        self.sl = 0.0              # 止损价格
        self.tp = 0.0              # 止盈价格
        self.sl_usd = 0.0          # 止损金额(USD)
        self.tp_usd = 0.0          # 止盈金额(USD)
        self.deviation = 10        # 滑点
        self.magic = 12345         # 魔术数字
        self.comment = ""          # 订单备注
        self.type_time = mt5.ORDER_TIME_GTC  # 订单有效期
        self.type_filling = mt5.ORDER_FILLING_IOC  # 订单填充方式

# 专业交易系统的下单函数
def place_professional_order(symbol, direction, lot_size, sl_usd=0, tp_usd=0, comment=""):
    """
    使用专业交易系统下单

    Args:
        symbol: 标准化后的交易对
        direction: "BUY" 或 "SELL"
        lot_size: 手数
        sl_usd: 止损金额(USD)
        tp_usd: 止盈金额(USD)
        comment: 订单备注

    Returns:
        订单结果
    """
    try:
        # 1. 获取当前价格
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            raise Exception(f"无法获取 {symbol} 的价格信息")

        # 2. 确定开仓价格
        if direction == "BUY":
            price = tick.ask
            order_type = mt5.ORDER_TYPE_BUY
        else:
            price = tick.bid
            order_type = mt5.ORDER_TYPE_SELL

        # 3. 计算止损止盈价格
        sl_price = 0.0
        tp_price = 0.0

        if sl_usd > 0 or tp_usd > 0:
            sl_price, tp_price = calculate_sl_tp_from_usd(
                symbol, direction, price, lot_size, sl_usd, tp_usd
            )

        # 4. 构建专业交易系统的订单请求
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": lot_size,
            "type": order_type,
            "price": price,
            "sl": sl_price,
            "tp": tp_price,
            "deviation": get_symbol_deviation(symbol),  # 根据交易对设置滑点
            "magic": get_magic_number(),
            "comment": f"TradingBot_{comment}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": get_filling_mode(symbol),  # 根据交易对选择填充方式
        }

        # 5. 发送订单
        result = mt5.order_send(request)

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            raise Exception(f"下单失败: {result.comment}")

        # 6. 记录订单信息
        order_info = {
            "ticket": result.order,
            "symbol": symbol,
            "direction": direction,
            "volume": lot_size,
            "open_price": result.price,
            "sl_price": sl_price,
            "tp_price": tp_price,
            "sl_usd": sl_usd,
            "tp_usd": tp_usd,
            "comment": comment,
            "magic": request["magic"],
            "open_time": datetime.now()
        }

        return {
            "success": True,
            "order_info": order_info,
            "mt5_result": result
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# 智能订单填充方式选择
def get_filling_mode(symbol):
    """
    根据交易对选择合适的订单填充方式
    采用专业交易系统的填充策略
    """
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        return mt5.ORDER_FILLING_IOC

    # 检查支持的填充方式
    filling_modes = symbol_info.filling_mode

    # 智能优先级: FOK > IOC > RETURN
    if filling_modes & mt5.SYMBOL_FILLING_FOK:
        return mt5.ORDER_FILLING_FOK
    elif filling_modes & mt5.SYMBOL_FILLING_IOC:
        return mt5.ORDER_FILLING_IOC
    else:
        return mt5.ORDER_FILLING_RETURN

# 动态滑点设置
def get_symbol_deviation(symbol):
    """
    根据交易对设置滑点
    采用专业交易系统的滑点策略
    """
    if symbol.startswith(("BTC", "ETH")):
        return 50  # 加密货币较大滑点
    elif symbol.startswith("XAU"):
        return 30  # 黄金中等滑点
    else:
        return 10  # 外汇较小滑点

# 智能魔术数字管理
def get_magic_number():
    """
    生成智能魔术数字
    """
    base_magic = 12345  # 基础魔术数字
    return base_magic + random.randint(1, 999)
```

#### 4.4 交易对配置结构
每个交易对都需要独立配置以下参数：

```json
{
  "XAUUSD": {
    "enabled": true,
    "lot_size": 0.01,
    "stop_loss_usd": 50.0,
    "take_profit_usd": 100.0,
    "signal_timeout": 180,
    "category": "precious_metals"
  },
  "ETHUSD": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 30.0,
    "take_profit_usd": 60.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "BTCUSD": {
    "enabled": true,
    "lot_size": 0.01,
    "stop_loss_usd": 100.0,
    "take_profit_usd": 200.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "SOLUSD": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 25.0,
    "take_profit_usd": 50.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "GBPUSD": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 40.0,
    "take_profit_usd": 80.0,
    "signal_timeout": 180,
    "category": "forex"
  },
  "GBPJPY": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 45.0,
    "take_profit_usd": 90.0,
    "signal_timeout": 180,
    "category": "forex"
  },
  "BCHUSD": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 20.0,
    "take_profit_usd": 40.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "ADAUSD": {
    "enabled": true,
    "lot_size": 1.0,
    "stop_loss_usd": 15.0,
    "take_profit_usd": 30.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "XLMUSD": {
    "enabled": true,
    "lot_size": 1.0,
    "stop_loss_usd": 10.0,
    "take_profit_usd": 20.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "DOGEUSD": {
    "enabled": true,
    "lot_size": 10.0,
    "stop_loss_usd": 15.0,
    "take_profit_usd": 30.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "LINKUSD": {
    "enabled": true,
    "lot_size": 0.5,
    "stop_loss_usd": 20.0,
    "take_profit_usd": 40.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "LTCUSD": {
    "enabled": true,
    "lot_size": 0.1,
    "stop_loss_usd": 25.0,
    "take_profit_usd": 50.0,
    "signal_timeout": 180,
    "category": "crypto"
  },
  "XRPUSD": {
    "enabled": true,
    "lot_size": 5.0,
    "stop_loss_usd": 12.0,
    "take_profit_usd": 24.0,
    "signal_timeout": 180,
    "category": "crypto"
  }
}
```

#### 4.3 全局止盈止损配置
全局止盈止损是针对**当前所有持仓订单的总盈亏金额**进行控制：

```json
{
  "global_portfolio_settings": {
    "enabled": true,
    "global_stop_loss_usd": 500.0,
    "global_take_profit_usd": 1000.0,
    "check_interval_seconds": 30,
    "description": "所有持仓订单总盈亏达到此金额时触发平仓"
  },
  "individual_override": {
    "enabled": true,
    "allow_per_symbol_config": true
  }
}
```

#### 4.4 通知配置
参考专业交易系统设计，增加平仓结果和定时余额通知：

```json
{
  "bark_notifications": {
    "trade_close_notification": {
      "enabled": true,
      "include_profit_loss": true,
      "include_balance_change": true,
      "template": "订单平仓通知"
    },
    "balance_notification": {
      "enabled": true,
      "interval_minutes": 60,
      "include_equity": true,
      "include_margin": true,
      "include_free_margin": true,
      "template": "定时余额报告"
    },
    "portfolio_sl_tp_notification": {
      "enabled": true,
      "template": "全局止盈止损触发"
    }
  }
}
```

#### 4.4 系统配置
```json
{
  "trading_cooldown": 300,
  "max_concurrent_trades": 5,
  "bark_forward_enabled": true,
  "emergency_stop": false,
  "max_daily_loss_usd": 500.0,
  "max_daily_profit_usd": 1000.0
}
```
5、每个交易的订单发送给bark（使用官方api，有两个bark设备，需要同时转发），web页面可以配置bark的参数（（请求参数
支持的参数列表，具体效果可在APP内预览。
参数	说明
title	推送标题
subtitle	推送副标题
body	推送内容
device_key	设备key
device_keys	key 数组，用于批量推送
level	推送中断级别。
critical: 重要警告, 在静音模式下也会响铃
active：默认值，系统会立即亮屏显示通知
timeSensitive：时效性通知，可在专注状态下显示通知。
passive：仅将通知添加到通知列表，不会亮屏提醒。
volume	重要警告的通知音量，取值范围：0-10，不传默认值为5
badge	推送角标，可以是任意数字
call	传"1"时，通知铃声重复播放
autoCopy	传"1"时， iOS14.5以下自动复制推送内容，iOS14.5以上需手动长按推送或下拉推送
copy	复制推送时，指定复制的内容，不传此参数将复制整个推送内容。
sound	可以为推送设置不同的铃声
icon	为推送设置自定义图标，设置的图标将替换默认Bark图标。
图标会自动缓存在本机，相同的图标 URL 仅下载一次。
group	对消息进行分组，推送将按group分组显示在通知中心中。
也可在历史消息列表中选择查看不同的群组。
ciphertext	加密推送的密文
isArchive	传 1 保存推送，传其他的不保存推送，不传按APP内设置来决定是否保存。
url	点击推送时，跳转的URL ，支持URL Scheme 和 Universal Link
action	传 "none" 时，点击推送不会弹窗
id	使用相同的ID值时，将更新对应推送的通知内容
需 Bark v1.5.2, bark-server v2.2.5 以上，Json传参需使用字符串类型
delete	传 "1" 时，将从系统通知中心和APP内历史记录中删除通知，需搭配 id 参数使用
需在设置里开启”后台App刷新“，否则无效））

6、可以直接转发收到的综合警报给bark（功能可以开启或者关闭，两个bark都要）

7、需要设置交易冷却功能（比如，间隔XX时间内，可以再进行交易，web页面可以配置）

## 详细技术设计

### 5. Bark通知系统设计

#### 5.1 通知场景
- 交易订单开仓通知
- 交易订单平仓通知
- 警报转发通知（可选）
- 系统状态通知

#### 5.2 Bark配置管理
Web页面需要提供以下Bark参数的配置界面：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| device_keys | 两个设备的key数组 | [] |
| title | 推送标题模板 | "交易通知" |
| level | 推送级别 | "active" |
| volume | 通知音量 | 5 |
| group | 消息分组 | "trading" |

#### 5.3 通知模板设计
```json
{
  "开仓通知": {
    "title": "🔥 新订单开仓",
    "body": "交易对: {{symbol}}\n方向: {{direction}}\n手数: {{lot_size}}\n价格: {{price}}\n时间: {{timestamp}}"
  },
  "平仓通知": {
    "title": "✅ 订单平仓",
    "body": "交易对: {{symbol}}\n方向: {{direction}}\n平仓原因: {{reason}}\n开仓价: {{open_price}}\n平仓价: {{close_price}}\n盈亏: {{profit_usd}} USD\n持仓时间: {{duration}}\n账户余额变化: {{balance_change}} USD\n当前余额: {{current_balance}} USD\n时间: {{timestamp}}"
  },
  "定时余额报告": {
    "title": "💰 账户余额报告",
    "body": "账户余额: {{balance}} USD\n净值: {{equity}} USD\n已用保证金: {{margin}} USD\n可用保证金: {{free_margin}} USD\n当前持仓: {{open_positions}}个\n今日盈亏: {{daily_pnl}} USD\n报告时间: {{timestamp}}"
  },
  "全局止盈止损触发": {
    "title": "⚠️ 全局止盈止损触发",
    "body": "触发类型: {{trigger_type}}\n总盈亏: {{total_pnl}} USD\n触发阈值: {{threshold}} USD\n影响订单: {{affected_orders}}个\n执行时间: {{timestamp}}"
  },
  "组合盈亏警告": {
    "title": "📊 组合盈亏提醒",
    "body": "当前总盈亏: {{total_pnl}} USD\n距离止损: {{distance_to_sl}} USD\n距离止盈: {{distance_to_tp}} USD\n持仓订单: {{position_count}}个\n时间: {{timestamp}}"
  }
}
```

### 6. 数据库设计

#### 6.1 系统管理员账户表 (admin_users)
```sql
CREATE TABLE admin_users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(32) NOT NULL,
    email VARCHAR(100),
    last_login DATETIME,
    login_attempts INTEGER DEFAULT 0,
    locked_until DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.2 登录会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id)
);
```

#### 6.3 登录日志表 (login_logs)
```sql
CREATE TABLE login_logs (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_result VARCHAR(20), -- SUCCESS, FAILED, LOCKED
    failure_reason VARCHAR(100),
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.4 警报记录表 (alerts)
```sql
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY,
    alert_id VARCHAR(50) UNIQUE,
    symbol VARCHAR(20),
    timeframe VARCHAR(10),
    signal_direction VARCHAR(10),
    signal_strength VARCHAR(10),
    price DECIMAL(10,5),
    timestamp DATETIME,
    processed BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.5 交易记录表 (trades)
```sql
CREATE TABLE trades (
    id INTEGER PRIMARY KEY,
    alert_id VARCHAR(50),
    symbol VARCHAR(20),
    direction VARCHAR(10),
    lot_size DECIMAL(10,4),
    open_price DECIMAL(10,5),
    close_price DECIMAL(10,5),
    stop_loss_price DECIMAL(10,5),
    take_profit_price DECIMAL(10,5),
    stop_loss_usd DECIMAL(10,2),
    take_profit_usd DECIMAL(10,2),
    actual_stop_loss_usd DECIMAL(10,2),
    actual_take_profit_usd DECIMAL(10,2),
    status VARCHAR(20),
    open_time DATETIME,
    close_time DATETIME,
    close_reason VARCHAR(100),
    profit_usd DECIMAL(10,2),
    mt5_ticket INTEGER,
    category VARCHAR(20)
);
```

#### 6.6 交易对配置表 (symbol_config)
```sql
CREATE TABLE symbol_config (
    id INTEGER PRIMARY KEY,
    symbol VARCHAR(20) UNIQUE,
    enabled BOOLEAN DEFAULT TRUE,
    lot_size DECIMAL(10,4),
    stop_loss_usd DECIMAL(10,2),
    take_profit_usd DECIMAL(10,2),
    signal_timeout INTEGER DEFAULT 180,
    category VARCHAR(20),
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.7 配置表 (config)
```sql
CREATE TABLE config (
    id INTEGER PRIMARY KEY,
    key VARCHAR(50) UNIQUE,
    value TEXT,
    description VARCHAR(200),
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.8 全局组合止盈止损配置表 (portfolio_sl_tp)
```sql
CREATE TABLE portfolio_sl_tp (
    id INTEGER PRIMARY KEY,
    enabled BOOLEAN DEFAULT FALSE,
    global_stop_loss_usd DECIMAL(10,2),
    global_take_profit_usd DECIMAL(10,2),
    check_interval_seconds INTEGER DEFAULT 30,
    last_check_time DATETIME,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.9 余额记录表 (balance_history)
```sql
CREATE TABLE balance_history (
    id INTEGER PRIMARY KEY,
    balance DECIMAL(15,2),
    equity DECIMAL(15,2),
    margin DECIMAL(15,2),
    free_margin DECIMAL(15,2),
    daily_pnl DECIMAL(10,2),
    open_positions INTEGER,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.10 通知记录表 (notifications)
```sql
CREATE TABLE notifications (
    id INTEGER PRIMARY KEY,
    notification_type VARCHAR(50),
    title VARCHAR(200),
    body TEXT,
    bark_device_1_sent BOOLEAN DEFAULT FALSE,
    bark_device_2_sent BOOLEAN DEFAULT FALSE,
    sent_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 7. 用户认证和会话管理

#### 7.1 用户认证系统
```python
import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, session

class AuthManager:
    def __init__(self, db_manager):
        self.db = db_manager
        self.jwt_secret = secrets.token_hex(32)
        self.session_timeout = 24 * 60 * 60  # 24小时
        self.max_login_attempts = 5
        self.lockout_duration = 30 * 60  # 30分钟

    def hash_password(self, password, salt=None):
        """密码哈希加密"""
        if salt is None:
            salt = secrets.token_hex(16)

        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        return password_hash.hex(), salt

    def verify_password(self, password, stored_hash, salt):
        """验证密码"""
        password_hash, _ = self.hash_password(password, salt)
        return password_hash == stored_hash

    def create_admin_user(self, username, password, email=None):
        """创建管理员用户"""
        try:
            # 检查用户名是否已存在
            existing_user = self.db.execute_query(
                "SELECT id FROM admin_users WHERE username = ?",
                (username,)
            )
            if existing_user:
                return {"success": False, "message": "用户名已存在"}

            # 加密密码
            password_hash, salt = self.hash_password(password)

            # 插入用户
            self.db.execute_query(
                """INSERT INTO admin_users (username, password_hash, salt, email)
                   VALUES (?, ?, ?, ?)""",
                (username, password_hash, salt, email)
            )

            return {"success": True, "message": "管理员用户创建成功"}

        except Exception as e:
            return {"success": False, "message": f"创建用户失败: {str(e)}"}

    def authenticate_user(self, username, password, ip_address, user_agent):
        """用户认证"""
        try:
            # 检查用户是否被锁定
            user = self.db.execute_query(
                """SELECT id, username, password_hash, salt, login_attempts, locked_until
                   FROM admin_users WHERE username = ?""",
                (username,)
            )

            if not user:
                self.log_login_attempt(username, ip_address, user_agent, "FAILED", "用户不存在")
                return {"success": False, "message": "用户名或密码错误"}

            user = user[0]

            # 检查账户锁定状态
            if user[5] and datetime.now() < datetime.fromisoformat(user[5]):
                self.log_login_attempt(username, ip_address, user_agent, "LOCKED", "账户被锁定")
                return {"success": False, "message": "账户已被锁定，请稍后再试"}

            # 验证密码
            if not self.verify_password(password, user[2], user[3]):
                # 增加失败次数
                new_attempts = user[4] + 1
                locked_until = None

                if new_attempts >= self.max_login_attempts:
                    locked_until = datetime.now() + timedelta(seconds=self.lockout_duration)

                self.db.execute_query(
                    "UPDATE admin_users SET login_attempts = ?, locked_until = ? WHERE id = ?",
                    (new_attempts, locked_until, user[0])
                )

                self.log_login_attempt(username, ip_address, user_agent, "FAILED", "密码错误")
                return {"success": False, "message": "用户名或密码错误"}

            # 登录成功，重置失败次数
            self.db.execute_query(
                "UPDATE admin_users SET login_attempts = 0, locked_until = NULL, last_login = ? WHERE id = ?",
                (datetime.now(), user[0])
            )

            # 创建会话
            session_token = self.create_session(user[0], ip_address, user_agent)

            self.log_login_attempt(username, ip_address, user_agent, "SUCCESS", None)

            return {
                "success": True,
                "message": "登录成功",
                "session_token": session_token,
                "user_id": user[0],
                "username": user[1]
            }

        except Exception as e:
            self.log_login_attempt(username, ip_address, user_agent, "FAILED", f"系统错误: {str(e)}")
            return {"success": False, "message": "登录失败，请稍后再试"}

    def create_session(self, user_id, ip_address, user_agent):
        """创建用户会话"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(seconds=self.session_timeout)

        self.db.execute_query(
            """INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at)
               VALUES (?, ?, ?, ?, ?)""",
            (user_id, session_token, ip_address, user_agent, expires_at)
        )

        return session_token

    def validate_session(self, session_token):
        """验证会话有效性"""
        try:
            session = self.db.execute_query(
                """SELECT s.user_id, s.expires_at, u.username
                   FROM user_sessions s
                   JOIN admin_users u ON s.user_id = u.id
                   WHERE s.session_token = ?""",
                (session_token,)
            )

            if not session:
                return {"valid": False, "message": "会话不存在"}

            session = session[0]

            if datetime.now() > datetime.fromisoformat(session[1]):
                # 会话过期，删除
                self.db.execute_query(
                    "DELETE FROM user_sessions WHERE session_token = ?",
                    (session_token,)
                )
                return {"valid": False, "message": "会话已过期"}

            return {
                "valid": True,
                "user_id": session[0],
                "username": session[2]
            }

        except Exception as e:
            return {"valid": False, "message": f"会话验证失败: {str(e)}"}

    def logout_user(self, session_token):
        """用户登出"""
        try:
            self.db.execute_query(
                "DELETE FROM user_sessions WHERE session_token = ?",
                (session_token,)
            )
            return {"success": True, "message": "登出成功"}
        except Exception as e:
            return {"success": False, "message": f"登出失败: {str(e)}"}

    def log_login_attempt(self, username, ip_address, user_agent, result, failure_reason):
        """记录登录尝试"""
        self.db.execute_query(
            """INSERT INTO login_logs (username, ip_address, user_agent, login_result, failure_reason)
               VALUES (?, ?, ?, ?, ?)""",
            (username, ip_address, user_agent, result, failure_reason)
        )

    def cleanup_expired_sessions(self):
        """清理过期会话"""
        self.db.execute_query(
            "DELETE FROM user_sessions WHERE expires_at < ?",
            (datetime.now(),)
        )

# 认证装饰器
def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        session_token = request.headers.get('Authorization')
        if not session_token:
            session_token = request.cookies.get('session_token')

        if not session_token:
            return jsonify({"error": "未提供认证信息"}), 401

        # 移除 Bearer 前缀（如果存在）
        if session_token.startswith('Bearer '):
            session_token = session_token[7:]

        auth_result = auth_manager.validate_session(session_token)
        if not auth_result["valid"]:
            return jsonify({"error": auth_result["message"]}), 401

        # 将用户信息添加到请求上下文
        request.current_user = {
            "user_id": auth_result["user_id"],
            "username": auth_result["username"]
        }

        return f(*args, **kwargs)
    return decorated_function
```

#### 7.2 登录页面HTML
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin-top: 10px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
            padding: 12px 15px;
            font-size: 16px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            margin-top: 20px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>🚀 交易系统登录</h2>
            <p>TradingView自动交易管理系统</p>
        </div>

        <div id="alertContainer"></div>

        <form id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input type="text" class="form-control" id="username" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input type="password" class="form-control" id="password" required>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    记住登录状态
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-login">
                <span id="loginSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                <span id="loginText">登录</span>
            </button>
        </form>

        <div class="text-center mt-3">
            <small class="text-muted">
                © 2024 TradingView自动交易系统
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class LoginManager {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.alertContainer = document.getElementById('alertContainer');
                this.init();
            }

            init() {
                this.form.addEventListener('submit', (e) => this.handleLogin(e));

                // 检查是否已登录
                this.checkExistingSession();
            }

            async checkExistingSession() {
                const sessionToken = localStorage.getItem('session_token') ||
                                   sessionStorage.getItem('session_token');

                if (sessionToken) {
                    try {
                        const response = await fetch('/api/auth/validate', {
                            headers: {
                                'Authorization': `Bearer ${sessionToken}`
                            }
                        });

                        if (response.ok) {
                            // 已登录，跳转到主页
                            window.location.href = '/';
                        }
                    } catch (error) {
                        // 会话无效，清除本地存储
                        localStorage.removeItem('session_token');
                        sessionStorage.removeItem('session_token');
                    }
                }
            }

            async handleLogin(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;

                this.setLoading(true);
                this.clearAlert();

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存会话令牌
                        const storage = rememberMe ? localStorage : sessionStorage;
                        storage.setItem('session_token', result.session_token);

                        this.showAlert('success', '登录成功，正在跳转...');

                        // 跳转到主页
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);

                    } else {
                        this.showAlert('danger', result.message);
                    }

                } catch (error) {
                    this.showAlert('danger', '登录失败，请检查网络连接');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                const spinner = document.getElementById('loginSpinner');
                const text = document.getElementById('loginText');
                const submitBtn = this.form.querySelector('button[type="submit"]');

                if (loading) {
                    spinner.classList.remove('d-none');
                    text.textContent = '登录中...';
                    submitBtn.disabled = true;
                } else {
                    spinner.classList.add('d-none');
                    text.textContent = '登录';
                    submitBtn.disabled = false;
                }
            }

            showAlert(type, message) {
                this.alertContainer.innerHTML = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
            }

            clearAlert() {
                this.alertContainer.innerHTML = '';
            }
        }

        // 初始化登录管理器
        document.addEventListener('DOMContentLoaded', () => {
            new LoginManager();
        });
    </script>
</body>
</html>
```

### 8. API接口设计

#### 8.1 认证相关API

```python
# 认证API路由
@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.json
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({"success": False, "message": "用户名和密码不能为空"}), 400

    # 获取客户端信息
    ip_address = request.remote_addr
    user_agent = request.headers.get('User-Agent', '')

    # 认证用户
    result = auth_manager.authenticate_user(username, password, ip_address, user_agent)

    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@app.route('/api/auth/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出"""
    session_token = request.headers.get('Authorization', '').replace('Bearer ', '')
    result = auth_manager.logout_user(session_token)
    return jsonify(result), 200

@app.route('/api/auth/validate', methods=['GET'])
@require_auth
def validate_session():
    """验证会话有效性"""
    return jsonify({
        "valid": True,
        "user": request.current_user
    }), 200

@app.route('/api/auth/change-password', methods=['POST'])
@require_auth
def change_password():
    """修改密码"""
    data = request.json
    current_password = data.get('current_password')
    new_password = data.get('new_password')

    if not current_password or not new_password:
        return jsonify({"success": False, "message": "当前密码和新密码不能为空"}), 400

    if len(new_password) < 8:
        return jsonify({"success": False, "message": "新密码长度不能少于8位"}), 400

    try:
        # 验证当前密码
        user = db_manager.execute_query(
            "SELECT password_hash, salt FROM admin_users WHERE id = ?",
            (request.current_user["user_id"],)
        )[0]

        if not auth_manager.verify_password(current_password, user[0], user[1]):
            return jsonify({"success": False, "message": "当前密码错误"}), 400

        # 更新密码
        new_hash, new_salt = auth_manager.hash_password(new_password)
        db_manager.execute_query(
            "UPDATE admin_users SET password_hash = ?, salt = ? WHERE id = ?",
            (new_hash, new_salt, request.current_user["user_id"])
        )

        return jsonify({"success": True, "message": "密码修改成功"}), 200

    except Exception as e:
        return jsonify({"success": False, "message": f"密码修改失败: {str(e)}"}), 500

@app.route('/api/auth/login-logs', methods=['GET'])
@require_auth
def get_login_logs():
    """获取登录日志"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        offset = (page - 1) * limit

        logs = db_manager.execute_query(
            """SELECT username, ip_address, login_result, failure_reason, login_time
               FROM login_logs
               ORDER BY login_time DESC
               LIMIT ? OFFSET ?""",
            (limit, offset)
        )

        total = db_manager.execute_query("SELECT COUNT(*) FROM login_logs")[0][0]

        return jsonify({
            "success": True,
            "data": {
                "logs": logs,
                "total": total,
                "page": page,
                "limit": limit
            }
        }), 200

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500
```

#### 8.2 Webhook接收接口和完整策略执行

```python
# Webhook接收端点
@app.route('/webhook/tradingview', methods=['POST'])
def receive_tradingview_alert():
    """
    接收TradingView警报并执行交易策略
    """
    try:
        alert_data = request.json
        logger.info(f"收到TradingView警报: {alert_data}")

        # 处理警报并执行策略
        result = process_tradingview_alert(alert_data)

        return jsonify(result), 200 if result["success"] else 400

    except Exception as e:
        logger.error(f"Webhook处理失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 完整的警报处理和策略执行流程
def process_tradingview_alert(alert_data):
    """
    处理TradingView警报，包含完整的策略执行逻辑
    """
    try:
        # 1. 标准化交易对符号
        original_symbol = alert_data.get("symbol", "")
        standard_symbol = normalize_symbol(original_symbol)

        # 2. 标准化信号方向
        original_signal = alert_data.get("signal_direction", "")
        standard_signal = normalize_signal_direction(original_signal)

        # 3. 记录映射信息
        logger.info(f"交易对映射: {original_symbol} -> {standard_symbol}")
        logger.info(f"信号映射: {original_signal} -> {standard_signal}")

        # 4. 验证数据完整性
        if not all([standard_symbol, standard_signal, alert_data.get("timeframe")]):
            raise Exception("警报数据不完整")

        # 5. 检查交易对是否支持
        if standard_symbol not in SUPPORTED_SYMBOLS:
            raise Exception(f"不支持的交易对: {original_symbol} (映射为: {standard_symbol})")

        # 6. 获取交易对配置
        symbol_config = get_symbol_config(standard_symbol)
        if not symbol_config.get("enabled", False):
            raise Exception(f"交易对 {standard_symbol} 已禁用")

        # 7. 构建标准化警报数据
        processed_alert = {
            "alert_type": alert_data.get("alert_type"),
            "original_symbol": original_symbol,
            "standard_symbol": standard_symbol,
            "original_signal": original_signal,
            "standard_signal": standard_signal,
            "timeframe": alert_data.get("timeframe"),
            "price": alert_data.get("price"),
            "timestamp": alert_data.get("timestamp"),
            "alert_id": alert_data.get("alert_id"),
            "processed_time": datetime.now().isoformat(),
            "signal_strength": alert_data.get("signal_strength", "中")
        }

        # 8. 执行交易策略
        return execute_comprehensive_trading_strategy(processed_alert, symbol_config)

    except Exception as e:
        logger.error(f"处理警报失败: {e}")
        return {"success": False, "error": str(e)}

# 综合交易策略执行器
def execute_comprehensive_trading_strategy(alert, symbol_config):
    """
    执行综合交易策略：下单、持有、平仓决策
    """
    try:
        symbol = alert["standard_symbol"]
        signal = alert["standard_signal"]
        timeframe = alert["timeframe"]

        # 1. 检查交易冷却时间
        if not check_trading_cooldown(symbol):
            return {"success": False, "reason": "交易冷却时间未到"}

        # 2. 检查现有持仓
        existing_position = get_existing_position(symbol)

        if existing_position is None:
            # 情况1: 无持仓 - 执行开仓策略
            return execute_open_position_strategy(alert, symbol_config)
        else:
            # 情况2: 有持仓 - 执行持仓管理策略
            return execute_position_management_strategy(alert, symbol_config, existing_position)

    except Exception as e:
        logger.error(f"策略执行失败: {e}")
        return {"success": False, "error": str(e)}

# 开仓策略执行
def execute_open_position_strategy(alert, symbol_config):
    """
    执行开仓策略 - 第一次收到信号时下单
    """
    try:
        symbol = alert["standard_symbol"]
        signal = alert["standard_signal"]

        logger.info(f"执行开仓策略: {symbol} {signal}")

        # 1. 风险检查
        if not perform_risk_checks(symbol, symbol_config):
            return {"success": False, "reason": "风险检查未通过"}

        # 2. 执行下单
        order_result = place_professional_order(
            symbol=symbol,
            direction=signal,
            lot_size=symbol_config["lot_size"],
            sl_usd=symbol_config["stop_loss_usd"],
            tp_usd=symbol_config["take_profit_usd"],
            comment=f"TradingView_{alert['alert_id']}"
        )

        if order_result["success"]:
            # 3. 记录订单信息
            order_info = order_result["order_info"]
            save_order_to_database(order_info, alert)

            # 4. 启动持仓监控
            start_position_monitoring(order_info["ticket"], symbol_config["signal_timeout"])

            # 5. 发送开仓通知
            send_open_position_notification(order_info, alert)

            return {
                "success": True,
                "action": "OPEN_POSITION",
                "order_ticket": order_info["ticket"],
                "symbol": symbol,
                "direction": signal,
                "lot_size": symbol_config["lot_size"]
            }
        else:
            return {
                "success": False,
                "reason": f"下单失败: {order_result['error']}"
            }

    except Exception as e:
        logger.error(f"开仓策略执行失败: {e}")
        return {"success": False, "error": str(e)}

# 持仓管理策略执行
def execute_position_management_strategy(alert, symbol_config, existing_position):
    """
    执行持仓管理策略 - 后续信号的持有/平仓决策
    """
    try:
        symbol = alert["standard_symbol"]
        signal = alert["standard_signal"]
        position_direction = existing_position["direction"]

        logger.info(f"执行持仓管理策略: {symbol} 持仓方向:{position_direction} 新信号:{signal}")

        # 1. 检查信号方向一致性
        if signal == position_direction:
            # 信号方向一致 - 继续持有
            return execute_hold_position_strategy(alert, symbol_config, existing_position)
        else:
            # 信号方向相反 - 平仓并开新仓
            return execute_reverse_position_strategy(alert, symbol_config, existing_position)

    except Exception as e:
        logger.error(f"持仓管理策略执行失败: {e}")
        return {"success": False, "error": str(e)}

# 持有持仓策略
def execute_hold_position_strategy(alert, symbol_config, existing_position):
    """
    继续持有策略 - 重置超时计时器
    """
    try:
        ticket = existing_position["ticket"]
        symbol = alert["standard_symbol"]

        logger.info(f"继续持有策略: {symbol} 订单#{ticket}")

        # 1. 重置超时计时器
        reset_position_timeout(ticket, symbol_config["signal_timeout"])

        # 2. 更新最后信号时间
        update_last_signal_time(ticket, alert["processed_time"])

        # 3. 记录信号确认
        record_signal_confirmation(ticket, alert)

        return {
            "success": True,
            "action": "HOLD_POSITION",
            "order_ticket": ticket,
            "symbol": symbol,
            "reason": "收到同方向确认信号，继续持有"
        }

    except Exception as e:
        logger.error(f"持有策略执行失败: {e}")
        return {"success": False, "error": str(e)}

# 反向持仓策略
def execute_reverse_position_strategy(alert, symbol_config, existing_position):
    """
    反向信号策略 - 平仓现有持仓并开新仓
    """
    try:
        old_ticket = existing_position["ticket"]
        symbol = alert["standard_symbol"]
        new_signal = alert["standard_signal"]

        logger.info(f"反向持仓策略: {symbol} 平仓#{old_ticket} 开新仓{new_signal}")

        # 1. 平仓现有持仓
        close_result = close_position_by_ticket(
            old_ticket,
            reason="收到反向信号，执行平仓"
        )

        if not close_result["success"]:
            return {
                "success": False,
                "reason": f"平仓失败: {close_result['error']}"
            }

        # 2. 等待平仓完成
        time.sleep(1)

        # 3. 开新仓
        new_order_result = execute_open_position_strategy(alert, symbol_config)

        if new_order_result["success"]:
            return {
                "success": True,
                "action": "REVERSE_POSITION",
                "closed_ticket": old_ticket,
                "new_ticket": new_order_result["order_ticket"],
                "symbol": symbol,
                "new_direction": new_signal
            }
        else:
            return {
                "success": False,
                "reason": f"开新仓失败: {new_order_result['reason']}"
            }

    except Exception as e:
        logger.error(f"反向持仓策略执行失败: {e}")
        return {"success": False, "error": str(e)}

# 支持的交易对列表
SUPPORTED_SYMBOLS = [
    "XAUUSD", "ETHUSD", "BTCUSD", "SOLUSD", "BCHUSD",
    "ADAUSD", "XLMUSD", "DOGEUSD", "LINKUSD", "LTCUSD",
    "XRPUSD", "GBPUSD", "GBPJPY"
]
```

#### 7.2 配置管理接口
```
GET /api/config/symbols              # 获取所有交易对配置
GET /api/config/symbols/{symbol}     # 获取单个交易对配置
PUT /api/config/symbols/{symbol}     # 更新交易对配置
POST /api/config/symbols/batch       # 批量更新交易对配置

GET /api/config/global-sl-tp         # 获取全局止盈止损配置
PUT /api/config/global-sl-tp         # 更新全局止盈止损配置

GET /api/config/system               # 获取系统配置
PUT /api/config/system               # 更新系统配置
```

#### 7.2.1 交易对配置API示例
```json
// PUT /api/config/symbols/XAUUSD
{
  "enabled": true,
  "lot_size": 0.01,
  "stop_loss_usd": 50.0,
  "take_profit_usd": 100.0,
  "signal_timeout": 180
}

// POST /api/config/symbols/batch
{
  "symbols": {
    "XAUUSD": {
      "stop_loss_usd": 60.0,
      "take_profit_usd": 120.0
    },
    "BTCUSD": {
      "stop_loss_usd": 150.0,
      "take_profit_usd": 300.0
    }
  }
}

// PUT /api/config/global-sl-tp
{
  "enabled": true,
  "global_stop_loss_usd": 50.0,
  "global_take_profit_usd": 100.0,
  "override_individual": false
}
```

#### 7.3 交易监控接口
```
GET /api/trades                    # 获取交易列表
GET /api/trades/{id}               # 获取交易详情
POST /api/trades/{id}/close        # 手动平仓单个订单
GET /api/alerts                    # 获取警报历史
```

#### 7.4 订单管理接口（参考专业交易系统设计）
```
GET /api/positions                 # 获取当前持仓列表
POST /api/positions/close-all      # 平仓所有订单
POST /api/positions/close-profit   # 平仓所有盈利订单
POST /api/positions/close-loss     # 平仓所有亏损订单
POST /api/positions/close-symbol   # 平仓指定交易对的所有订单
POST /api/positions/{ticket}/close # 平仓指定订单
PUT /api/positions/{ticket}/modify # 修改订单止损止盈

# 批量操作接口
POST /api/positions/batch-close    # 批量平仓选中的订单
POST /api/positions/batch-modify   # 批量修改止损止盈
```

#### 7.5 订单管理API示例
```json
// POST /api/positions/close-all
{
  "reason": "手动平仓所有订单",
  "send_notification": true
}

// POST /api/positions/close-profit
{
  "min_profit_usd": 10.0,
  "reason": "平仓盈利订单",
  "send_notification": true
}

// POST /api/positions/close-loss
{
  "max_loss_usd": -50.0,
  "reason": "平仓亏损订单",
  "send_notification": true
}

// POST /api/positions/close-symbol
{
  "symbol": "XAUUSD",
  "reason": "平仓XAUUSD所有订单",
  "send_notification": true
}

// PUT /api/positions/{ticket}/modify
{
  "stop_loss": 1.0800,
  "take_profit": 1.0900,
  "stop_loss_usd": 50.0,
  "take_profit_usd": 100.0
}

// POST /api/positions/batch-close
{
  "tickets": [12345, 12346, 12347],
  "reason": "批量手动平仓",
  "send_notification": true
}
```

### 8. 风险控制设计

#### 8.1 交易限制
- 最大同时持仓数量限制
- 单个交易对最大手数限制
- 日交易次数限制
- 每日最大亏损限制（美元）
- 每日最大盈利限制（美元）
- 单笔交易最大风险金额

#### 8.2 止盈止损计算逻辑
参考专业交易系统设计，支持以美元为单位的止盈止损：

```python
def calculate_sl_tp_prices(symbol, direction, entry_price, sl_usd, tp_usd, lot_size):
    """
    计算单个订单的止损止盈价格

    Args:
        symbol: 交易对
        direction: 交易方向 (BUY/SELL)
        entry_price: 入场价格
        sl_usd: 止损金额(美元)
        tp_usd: 止盈金额(美元)
        lot_size: 交易手数

    Returns:
        (stop_loss_price, take_profit_price)
    """
    # 获取合约规格
    contract_size = get_contract_size(symbol)
    pip_value = get_pip_value(symbol, lot_size)

    # 计算止损止盈点数
    sl_pips = sl_usd / pip_value
    tp_pips = tp_usd / pip_value

    if direction == "BUY":
        sl_price = entry_price - (sl_pips * get_pip_size(symbol))
        tp_price = entry_price + (tp_pips * get_pip_size(symbol))
    else:  # SELL
        sl_price = entry_price + (sl_pips * get_pip_size(symbol))
        tp_price = entry_price - (tp_pips * get_pip_size(symbol))

    return sl_price, tp_price

def calculate_portfolio_pnl():
    """
    计算当前所有持仓的总盈亏

    Returns:
        total_pnl_usd: 总盈亏金额(美元)
    """
    open_positions = mt5.positions_get()
    total_pnl = 0.0

    for position in open_positions:
        total_pnl += position.profit

    return total_pnl

def check_portfolio_sl_tp():
    """
    检查组合止盈止损

    Returns:
        action: None, 'stop_loss', 'take_profit'
    """
    config = get_portfolio_config()
    if not config['enabled']:
        return None

    total_pnl = calculate_portfolio_pnl()

    if total_pnl <= -config['global_stop_loss_usd']:
        return 'stop_loss'
    elif total_pnl >= config['global_take_profit_usd']:
        return 'take_profit'

    return None
```

#### 8.3 组合风险管理逻辑
1. **组合止盈止损**: 监控所有持仓订单的总盈亏，达到阈值时全部平仓
2. **单个订单止盈止损**: 每个订单独立的止盈止损设置
3. **双重保护**: 组合和单个订单的止盈止损同时生效
4. **定时检查**: 每30秒检查一次组合盈亏状态

#### 8.4 通知发送逻辑
```python
def send_trade_close_notification(trade_info):
    """
    发送订单平仓通知
    """
    # 获取账户余额变化
    current_balance = mt5.account_info().balance
    balance_change = trade_info['profit_usd']

    notification_data = {
        'symbol': trade_info['symbol'],
        'direction': trade_info['direction'],
        'reason': trade_info['close_reason'],
        'open_price': trade_info['open_price'],
        'close_price': trade_info['close_price'],
        'profit_usd': trade_info['profit_usd'],
        'duration': calculate_duration(trade_info['open_time'], trade_info['close_time']),
        'balance_change': balance_change,
        'current_balance': current_balance,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    send_bark_notification('平仓通知', notification_data)

def send_balance_notification():
    """
    发送定时余额通知
    """
    account_info = mt5.account_info()
    positions = mt5.positions_get()

    # 计算今日盈亏
    daily_pnl = calculate_daily_pnl()

    notification_data = {
        'balance': account_info.balance,
        'equity': account_info.equity,
        'margin': account_info.margin,
        'free_margin': account_info.margin_free,
        'open_positions': len(positions),
        'daily_pnl': daily_pnl,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    send_bark_notification('定时余额报告', notification_data)
```

#### 8.4 紧急控制
- 紧急停止开关（停止所有新交易）
- 一键平仓功能
- 异常情况自动停止交易
- 达到日亏损限制自动停止

#### 8.5 监控告警
- 连续亏损告警
- 系统异常告警
- 网络连接异常告警
- 日亏损接近限制告警
- 单个交易对异常告警

### 9. 持仓监控和自动平仓系统

```python
# 持仓监控系统
class PositionMonitor:
    def __init__(self):
        self.monitored_positions = {}
        self.monitor_thread = None
        self.running = False

    def start_position_monitoring(self, ticket, timeout_seconds):
        """
        启动持仓监控
        """
        self.monitored_positions[ticket] = {
            "ticket": ticket,
            "start_time": datetime.now(),
            "timeout_seconds": timeout_seconds,
            "last_signal_time": datetime.now(),
            "status": "MONITORING"
        }

        logger.info(f"启动持仓监控: 订单#{ticket}, 超时时间:{timeout_seconds}秒")

        if not self.running:
            self.start_monitor_thread()

    def reset_position_timeout(self, ticket, timeout_seconds):
        """
        重置持仓超时时间
        """
        if ticket in self.monitored_positions:
            self.monitored_positions[ticket]["last_signal_time"] = datetime.now()
            self.monitored_positions[ticket]["timeout_seconds"] = timeout_seconds
            logger.info(f"重置持仓超时: 订单#{ticket}")

    def start_monitor_thread(self):
        """
        启动监控线程
        """
        self.running = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("持仓监控线程已启动")

    def monitor_loop(self):
        """
        监控循环
        """
        while self.running:
            try:
                current_time = datetime.now()
                positions_to_close = []

                for ticket, monitor_info in self.monitored_positions.items():
                    if monitor_info["status"] != "MONITORING":
                        continue

                    # 检查是否超时
                    time_since_last_signal = (current_time - monitor_info["last_signal_time"]).total_seconds()

                    if time_since_last_signal >= monitor_info["timeout_seconds"]:
                        positions_to_close.append(ticket)
                        logger.warning(f"持仓超时: 订单#{ticket}, 超时{time_since_last_signal}秒")

                # 执行超时平仓
                for ticket in positions_to_close:
                    self.execute_timeout_close(ticket)

                # 清理已关闭的持仓
                self.cleanup_closed_positions()

                time.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"持仓监控循环错误: {e}")
                time.sleep(30)

    def execute_timeout_close(self, ticket):
        """
        执行超时平仓
        """
        try:
            # 获取持仓信息
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.warning(f"订单#{ticket}已不存在，移除监控")
                self.monitored_positions[ticket]["status"] = "CLOSED"
                return

            position = position[0]

            # 执行平仓
            close_result = close_position_by_ticket(
                ticket,
                reason=f"{self.monitored_positions[ticket]['timeout_seconds']}秒内未收到综合警报"
            )

            if close_result["success"]:
                logger.info(f"超时平仓成功: 订单#{ticket}")
                self.monitored_positions[ticket]["status"] = "CLOSED"

                # 发送平仓通知
                send_timeout_close_notification(position, close_result)
            else:
                logger.error(f"超时平仓失败: 订单#{ticket}, 错误:{close_result['error']}")

        except Exception as e:
            logger.error(f"执行超时平仓失败: 订单#{ticket}, 错误:{e}")

    def cleanup_closed_positions(self):
        """
        清理已关闭的持仓监控
        """
        closed_tickets = [
            ticket for ticket, info in self.monitored_positions.items()
            if info["status"] == "CLOSED"
        ]

        for ticket in closed_tickets:
            del self.monitored_positions[ticket]
            logger.info(f"清理已关闭持仓监控: 订单#{ticket}")

# 全局持仓监控器实例
position_monitor = PositionMonitor()

# 辅助函数
def start_position_monitoring(ticket, timeout_seconds):
    """启动持仓监控"""
    position_monitor.start_position_monitoring(ticket, timeout_seconds)

def reset_position_timeout(ticket, timeout_seconds):
    """重置持仓超时"""
    position_monitor.reset_position_timeout(ticket, timeout_seconds)

def update_last_signal_time(ticket, signal_time):
    """更新最后信号时间"""
    if ticket in position_monitor.monitored_positions:
        position_monitor.monitored_positions[ticket]["last_signal_time"] = datetime.fromisoformat(signal_time)

def record_signal_confirmation(ticket, alert):
    """记录信号确认"""
    try:
        # 保存到数据库
        signal_confirmation = {
            "ticket": ticket,
            "alert_id": alert["alert_id"],
            "signal_direction": alert["standard_signal"],
            "confirmation_time": alert["processed_time"],
            "symbol": alert["standard_symbol"]
        }

        save_signal_confirmation_to_database(signal_confirmation)
        logger.info(f"记录信号确认: 订单#{ticket}")

    except Exception as e:
        logger.error(f"记录信号确认失败: {e}")
```

### 10. 部署和运维

#### 10.1 系统要求
- Windows 11
- Python 3.8+
- MetaTrader5
- 稳定网络连接

#### 10.2 服务管理
- 系统服务自启动
- 日志轮转管理
- 性能监控
- 自动重启机制

#### 10.3 备份策略
- 数据库定期备份
- 配置文件备份
- 交易日志备份

### 11. 定时任务设计

#### 11.1 组合监控任务
```python
# 每30秒执行一次组合盈亏检查
@scheduler.task('interval', seconds=30)
def portfolio_monitor_task():
    action = check_portfolio_sl_tp()
    if action:
        close_all_positions(reason=f'组合{action}触发')
        send_portfolio_notification(action)

# 每5分钟检查一次组合盈亏状态（预警提醒）
@scheduler.task('interval', minutes=5)
def portfolio_status_check():
    """
    组合预警检查：当总盈亏接近止损/止盈阈值的80%时发送预警通知
    例如：止损线-500USD，当亏损达到-400USD时发送预警
    """
    total_pnl = calculate_portfolio_pnl()
    config = get_portfolio_config()

    # 检查是否接近止损线（80%阈值）
    if total_pnl <= -(config['global_stop_loss_usd'] * 0.8):
        send_portfolio_warning('接近止损', total_pnl, config['global_stop_loss_usd'])

    # 检查是否接近止盈线（80%阈值）
    elif total_pnl >= (config['global_take_profit_usd'] * 0.8):
        send_portfolio_warning('接近止盈', total_pnl, config['global_take_profit_usd'])
```

#### 11.2 定时余额报告任务
```python
# 每小时发送余额报告
@scheduler.task('interval', hours=1)
def hourly_balance_report():
    send_balance_notification()

# 每日汇总报告（每天晚上23:00）
@scheduler.task('cron', hour=23, minute=0)
def daily_summary_report():
    send_daily_summary_notification()
```

#### 11.3 系统维护任务
```python
# 每天清理过期数据
@scheduler.task('cron', hour=2, minute=0)
def cleanup_old_data():
    cleanup_old_alerts(days=30)
    cleanup_old_notifications(days=7)
    cleanup_old_balance_history(days=90)
```

### 12. 开发计划

#### 12.1 第一阶段 - 核心功能
- [ ] Webhook接收服务
- [ ] 基础数据库设计
- [ ] MetaTrader5集成
- [ ] 基本交易执行逻辑
- [ ] 组合盈亏监控

#### 12.2 第二阶段 - 通知系统
- [ ] Bark通知集成
- [ ] 平仓结果通知
- [ ] 定时余额报告
- [ ] 组合风险通知

#### 12.3 第三阶段 - 管理界面
- [ ] Web Dashboard开发
- [ ] 配置管理界面
- [ ] 交易监控面板
- [ ] 历史记录查询

#### 12.4 第四阶段 - 优化和部署
- [ ] 风险控制完善
- [ ] 性能优化
- [ ] 错误处理增强
- [ ] 生产环境部署

### 12. 专业下单系统集成和交易对配置

#### 12.1 专业下单方式详细实现

```python
# 专业交易系统的完整下单系统
class ProfessionalTradingSystem:
    def __init__(self):
        self.magic_base = 12345
        self.default_deviation = 10
        self.order_timeout = 30  # 秒

    def execute_professional_order(self, alert_data, symbol_config):
        """
        执行专业交易系统的下单
        """
        try:
            # 1. 标准化交易对
            standard_symbol = normalize_symbol(alert_data["symbol"])

            # 2. 检查交易条件
            if not self.check_trading_conditions(standard_symbol):
                return {"success": False, "reason": "交易条件不满足"}

            # 3. 构建专业订单参数
            order_params = self.build_professional_order_params(
                standard_symbol,
                alert_data["signal_direction"],
                symbol_config
            )

            # 4. 执行下单
            result = self.place_professional_market_order(order_params)

            # 5. 后处理
            if result["success"]:
                self.post_order_processing(result["order_info"])

            return result

        except Exception as e:
            logger.error(f"专业下单失败: {e}")
            return {"success": False, "error": str(e)}

    def build_professional_order_params(self, symbol, direction, config):
        """
        构建专业交易系统的订单参数
        """
        # 获取当前价格
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            raise Exception(f"无法获取{symbol}价格")

        # 确定订单类型和价格
        if direction.upper() in ["BUY", "做多"]:
            order_type = mt5.ORDER_TYPE_BUY
            price = tick.ask
        else:
            order_type = mt5.ORDER_TYPE_SELL
            price = tick.bid

        # 计算止损止盈
        sl_price, tp_price = self.calculate_professional_sl_tp(
            symbol, direction, price,
            config["lot_size"],
            config["stop_loss_usd"],
            config["take_profit_usd"]
        )

        return {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": config["lot_size"],
            "type": order_type,
            "price": price,
            "sl": sl_price,
            "tp": tp_price,
            "deviation": self.get_professional_deviation(symbol),
            "magic": self.generate_professional_magic(),
            "comment": f"TradingBot_{datetime.now().strftime('%H%M%S')}",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": self.get_professional_filling_mode(symbol),
        }

    def place_professional_market_order(self, order_params):
        """
        执行专业交易系统的市价单
        """
        try:
            # 发送订单
            result = mt5.order_send(order_params)

            # 检查结果
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                # 专业交易系统的错误处理
                error_msg = self.get_professional_error_message(result.retcode)
                return {
                    "success": False,
                    "error": error_msg,
                    "retcode": result.retcode
                }

            # 构建订单信息
            order_info = {
                "ticket": result.order,
                "symbol": order_params["symbol"],
                "direction": "BUY" if order_params["type"] == mt5.ORDER_TYPE_BUY else "SELL",
                "volume": order_params["volume"],
                "open_price": result.price,
                "sl_price": order_params["sl"],
                "tp_price": order_params["tp"],
                "magic": order_params["magic"],
                "comment": order_params["comment"],
                "open_time": datetime.now(),
                "filling_mode": order_params["type_filling"],
                "deviation": order_params["deviation"]
            }

            return {
                "success": True,
                "order_info": order_info,
                "mt5_result": result
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_professional_filling_mode(self, symbol):
        """
        专业交易系统的填充模式选择
        """
        symbol_info = mt5.symbol_info(symbol)
        if not symbol_info:
            return mt5.ORDER_FILLING_IOC

        filling_modes = symbol_info.filling_mode

        # 智能优先级策略
        if symbol.startswith(("BTC", "ETH", "SOL")):
            # 加密货币优先使用IOC
            if filling_modes & mt5.SYMBOL_FILLING_IOC:
                return mt5.ORDER_FILLING_IOC
            elif filling_modes & mt5.SYMBOL_FILLING_FOK:
                return mt5.ORDER_FILLING_FOK
            else:
                return mt5.ORDER_FILLING_RETURN
        else:
            # 外汇和贵金属优先使用FOK
            if filling_modes & mt5.SYMBOL_FILLING_FOK:
                return mt5.ORDER_FILLING_FOK
            elif filling_modes & mt5.SYMBOL_FILLING_IOC:
                return mt5.ORDER_FILLING_IOC
            else:
                return mt5.ORDER_FILLING_RETURN

    def get_professional_deviation(self, symbol):
        """
        专业交易系统的滑点设置
        """
        if symbol.startswith(("BTC", "ETH")):
            return 100  # 主要加密货币
        elif symbol.startswith(("SOL", "ADA", "LINK", "LTC")):
            return 50   # 次要加密货币
        elif symbol.startswith(("DOGE", "XLM", "XRP")):
            return 30   # 小市值加密货币
        elif symbol.startswith("XAU"):
            return 30   # 黄金
        else:
            return 10   # 外汇

    def generate_professional_magic(self):
        """
        生成专业交易系统的魔术数字
        """
        timestamp = int(datetime.now().timestamp()) % 10000
        return self.magic_base + timestamp

    def get_professional_error_message(self, retcode):
        """
        专业交易系统的错误信息
        """
        error_messages = {
            mt5.TRADE_RETCODE_REQUOTE: "价格重新报价",
            mt5.TRADE_RETCODE_REJECT: "请求被拒绝",
            mt5.TRADE_RETCODE_CANCEL: "请求被取消",
            mt5.TRADE_RETCODE_PLACED: "订单已放置",
            mt5.TRADE_RETCODE_DONE: "请求完成",
            mt5.TRADE_RETCODE_DONE_PARTIAL: "请求部分完成",
            mt5.TRADE_RETCODE_ERROR: "请求处理错误",
            mt5.TRADE_RETCODE_TIMEOUT: "请求超时",
            mt5.TRADE_RETCODE_INVALID: "无效请求",
            mt5.TRADE_RETCODE_INVALID_VOLUME: "无效交易量",
            mt5.TRADE_RETCODE_INVALID_PRICE: "无效价格",
            mt5.TRADE_RETCODE_INVALID_STOPS: "无效止损/止盈",
            mt5.TRADE_RETCODE_TRADE_DISABLED: "交易被禁用",
            mt5.TRADE_RETCODE_MARKET_CLOSED: "市场关闭",
            mt5.TRADE_RETCODE_NO_MONEY: "资金不足",
            mt5.TRADE_RETCODE_PRICE_CHANGED: "价格已变化",
            mt5.TRADE_RETCODE_PRICE_OFF: "价格偏离",
            mt5.TRADE_RETCODE_INVALID_EXPIRATION: "无效到期时间",
            mt5.TRADE_RETCODE_ORDER_CHANGED: "订单状态已变化",
            mt5.TRADE_RETCODE_TOO_MANY_REQUESTS: "请求过多",
            mt5.TRADE_RETCODE_NO_CHANGES: "无变化",
            mt5.TRADE_RETCODE_SERVER_DISABLES_AT: "服务器禁用自动交易",
            mt5.TRADE_RETCODE_CLIENT_DISABLES_AT: "客户端禁用自动交易",
            mt5.TRADE_RETCODE_LOCKED: "请求被锁定",
            mt5.TRADE_RETCODE_FROZEN: "订单或持仓被冻结",
            mt5.TRADE_RETCODE_INVALID_FILL: "无效填充类型",
            mt5.TRADE_RETCODE_CONNECTION: "连接问题",
            mt5.TRADE_RETCODE_ONLY_REAL: "仅允许真实账户",
            mt5.TRADE_RETCODE_LIMIT_ORDERS: "订单数量限制",
            mt5.TRADE_RETCODE_LIMIT_VOLUME: "交易量限制",
        }
        return error_messages.get(retcode, f"未知错误代码: {retcode}")
```

#### 12.2 交易对配置参考表（含映射信息）

| 标准交易对 | 映射变体 | 分类 | 推荐手数 | 止损(USD) | 止盈(USD) | 智能滑点 | 备注 |
|-----------|----------|------|----------|-----------|-----------|----------|------|
| XAUUSD | XAU-USD, GOLD | 贵金属 | 0.01 | 50 | 100 | 30 | 黄金，波动较大 |
| BTCUSD | BTCUSDT, BTC-USD | 加密货币 | 0.01 | 100 | 200 | 100 | 比特币，高波动 |
| ETHUSD | ETHUSDT, ETH-USD | 加密货币 | 0.1 | 30 | 60 | 100 | 以太坊 |
| SOLUSD | SOLUSDT, SOL-USD | 加密货币 | 0.1 | 25 | 50 | 50 | Solana |
| GBPUSD | GBP-USD, GBP/USD | 外汇 | 0.1 | 40 | 80 | 10 | 英镑美元 |
| GBPJPY | GBP-JPY, GBP/JPY | 外汇 | 0.1 | 45 | 90 | 10 | 英镑日元 |
| BCHUSD | BCHUSDT, BCH-USD | 加密货币 | 0.1 | 20 | 40 | 50 | 比特币现金 |
| ADAUSD | ADAUSDT, ADA-USD | 加密货币 | 1.0 | 15 | 30 | 50 | Cardano |
| XLMUSD | XLMUSDT, XLM-USD | 加密货币 | 1.0 | 10 | 20 | 30 | Stellar |
| DOGEUSD | DOGEUSDT, DOGE-USD | 加密货币 | 10.0 | 15 | 30 | 30 | 狗狗币 |
| LINKUSD | LINKUSDT, LINK-USD | 加密货币 | 0.5 | 20 | 40 | 50 | Chainlink |
| LTCUSD | LTCUSDT, LTC-USD | 加密货币 | 0.1 | 25 | 50 | 50 | 莱特币 |
| XRPUSD | XRPUSDT, XRP-USD | 加密货币 | 5.0 | 12 | 24 | 30 | 瑞波币 |

#### 12.2 配置说明
- **手数**: 根据资金规模和风险承受能力调整
- **止损**: 建议设置为账户资金的1-2%
- **止盈**: 建议设置为止损的1.5-2倍
- **信号超时**: 统一设置为180秒（3分钟）

#### 12.3 风险管理建议
- 总持仓不超过账户资金的10%
- 单个交易对风险不超过账户资金的2%
- 每日最大亏损不超过账户资金的5%
- 连续亏损3次后暂停交易

### 13. Web管理页面模块化设计

#### 13.1 认证保护和会话管理
Web管理系统需要登录认证才能访问，包含以下安全特性：

```javascript
// 全局认证管理
class AuthManager {
    constructor() {
        this.sessionToken = this.getSessionToken();
        this.init();
    }

    init() {
        // 检查登录状态
        this.checkAuthStatus();

        // 设置请求拦截器
        this.setupRequestInterceptor();

        // 监听存储变化
        window.addEventListener('storage', (e) => {
            if (e.key === 'session_token' && !e.newValue) {
                this.logout();
            }
        });
    }

    getSessionToken() {
        return localStorage.getItem('session_token') ||
               sessionStorage.getItem('session_token');
    }

    async checkAuthStatus() {
        if (!this.sessionToken) {
            this.redirectToLogin();
            return;
        }

        try {
            const response = await fetch('/api/auth/validate', {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (!response.ok) {
                this.logout();
            } else {
                const result = await response.json();
                this.updateUserInfo(result.user);
            }
        } catch (error) {
            this.logout();
        }
    }

    setupRequestInterceptor() {
        // 拦截所有fetch请求，自动添加认证头
        const originalFetch = window.fetch;
        window.fetch = async (url, options = {}) => {
            if (this.sessionToken && !url.includes('/api/auth/login')) {
                options.headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${this.sessionToken}`
                };
            }

            const response = await originalFetch(url, options);

            // 如果返回401，自动登出
            if (response.status === 401) {
                this.logout();
            }

            return response;
        };
    }

    updateUserInfo(user) {
        // 更新页面上的用户信息
        const userElements = document.querySelectorAll('.user-name');
        userElements.forEach(el => el.textContent = user.username);

        // 显示登出按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.style.display = 'block';
            logoutBtn.onclick = () => this.logout();
        }
    }

    async logout() {
        try {
            if (this.sessionToken) {
                await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.sessionToken}`
                    }
                });
            }
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 清除本地存储
            localStorage.removeItem('session_token');
            sessionStorage.removeItem('session_token');

            // 跳转到登录页
            this.redirectToLogin();
        }
    }

    redirectToLogin() {
        window.location.href = '/login';
    }
}

// 初始化认证管理器
const authManager = new AuthManager();
```

#### 13.2 页面结构和导航
Web管理系统采用模块化设计，分为以下主要页面：

```html
<!-- 主导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="#">TradingBot 管理系统</a>
    <div class="navbar-nav">
      <a class="nav-link" href="#dashboard" onclick="showPage('dashboard')">📊 仪表盘</a>
      <a class="nav-link" href="#positions" onclick="showPage('positions')">📈 订单管理</a>
      <a class="nav-link" href="#config" onclick="showPage('config')">⚙️ 配置管理</a>
      <a class="nav-link" href="#history" onclick="showPage('history')">📋 交易历史</a>
      <a class="nav-link" href="#alerts" onclick="showPage('alerts')">🔔 警报记录</a>
      <a class="nav-link" href="#logs" onclick="showPage('logs')">📝 系统日志</a>
    </div>
    <div class="navbar-text d-flex align-items-center">
      <span id="connectionStatus" class="badge badge-success me-3">🟢 已连接</span>
      <button class="btn btn-danger btn-sm me-2" id="emergencyStop">🚨 紧急停止</button>
      <div class="dropdown">
        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
          👤 <span class="user-name">管理员</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
          <li><a class="dropdown-item" href="#" onclick="showChangePasswordModal()">🔑 修改密码</a></li>
          <li><a class="dropdown-item" href="#" onclick="showCreateAdminModal()">👥 创建管理员</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="#" id="logoutBtn" onclick="authManager.logout()">🚪 登出</a></li>
        </ul>
      </div>
    </div>
  </div>
</nav>
```

#### 13.2 仪表盘页面设计
实时显示账户信息和系统状态：

```html
<!-- 仪表盘页面 -->
<div id="dashboardPage" class="page-content">
  <!-- 账户信息卡片 -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <h5 class="card-title">💰 账户余额</h5>
          <h3 id="accountBalance">$0.00</h3>
          <small>变化: <span id="balanceChange">+$0.00</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <h5 class="card-title">📊 净值</h5>
          <h3 id="accountEquity">$0.00</h3>
          <small>浮动盈亏: <span id="floatingPnL">+$0.00</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <h5 class="card-title">🔒 已用保证金</h5>
          <h3 id="usedMargin">$0.00</h3>
          <small>保证金比例: <span id="marginLevel">0%</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <h5 class="card-title">💵 可用保证金</h5>
          <h3 id="freeMargin">$0.00</h3>
          <small>持仓数: <span id="positionCount">0</span></small>
        </div>
      </div>
    </div>
  </div>

  <!-- 今日统计 -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">📈 今日盈亏</div>
        <div class="card-body">
          <h4 id="dailyPnL" class="text-success">+$0.00</h4>
          <div class="progress">
            <div id="dailyPnLProgress" class="progress-bar" style="width: 50%"></div>
          </div>
          <small>目标: <span id="dailyTarget">$100.00</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">🎯 今日交易</div>
        <div class="card-body">
          <h4 id="dailyTrades">0</h4>
          <small>成功率: <span id="successRate">0%</span></small><br>
          <small>盈利订单: <span id="profitTrades">0</span></small><br>
          <small>亏损订单: <span id="lossTrades">0</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">⚡ 系统状态</div>
        <div class="card-body">
          <div>MT5连接: <span id="mt5Status" class="badge badge-success">正常</span></div>
          <div>Webhook: <span id="webhookStatus" class="badge badge-success">运行中</span></div>
          <div>Bark通知: <span id="barkStatus" class="badge badge-success">正常</span></div>
          <div>紧急停止: <span id="emergencyStatus" class="badge badge-secondary">关闭</span></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 组合风险监控 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">🎯 组合风险监控</div>
        <div class="card-body">
          <div class="row">
            <div class="col-6">
              <label>总盈亏:</label>
              <h5 id="portfolioPnL" class="text-success">+$0.00</h5>
            </div>
            <div class="col-6">
              <label>风险比例:</label>
              <h5 id="riskRatio">0%</h5>
            </div>
          </div>
          <div class="progress mb-2">
            <div id="portfolioProgress" class="progress-bar bg-success" style="width: 30%"></div>
          </div>
          <small>止损线: <span id="portfolioStopLoss">-$500.00</span> | 止盈线: <span id="portfolioTakeProfit">+$1000.00</span></small>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">📊 持仓分布</div>
        <div class="card-body">
          <canvas id="positionChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- 最近警报 -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">🔔 最近警报 (最新5条)</div>
        <div class="card-body">
          <div id="recentAlerts" class="list-group">
            <!-- 动态加载最近警报 -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 13.3 订单管理页面设计
专门的订单管理页面，包含完整的订单操作功能：

```html
<!-- 订单管理页面 -->
<div id="positionsPage" class="page-content" style="display: none;">
  <!-- 页面标题和统计 -->
  <div class="row mb-3">
    <div class="col-md-8">
      <h3>📈 订单管理</h3>
    </div>
    <div class="col-md-4 text-right">
      <div class="btn-group">
        <button class="btn btn-outline-info btn-sm" onclick="refreshPositions()">
          🔄 刷新 (<span id="autoRefreshCountdown">30</span>s)
        </button>
        <button class="btn btn-outline-secondary btn-sm" onclick="toggleAutoRefresh()">
          <span id="autoRefreshToggle">⏸️ 暂停自动刷新</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 实时统计卡片 -->
  <div class="row mb-4">
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">持仓数量</h6>
          <h4 id="totalPositions" class="text-primary">0</h4>
        </div>
      </div>
    </div>
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">总盈亏</h6>
          <h4 id="totalPnL" class="text-success">+$0.00</h4>
        </div>
      </div>
    </div>
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">盈利订单</h6>
          <h4 id="profitPositions" class="text-success">0</h4>
        </div>
      </div>
    </div>
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">亏损订单</h6>
          <h4 id="lossPositions" class="text-danger">0</h4>
        </div>
      </div>
    </div>
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">最大盈利</h6>
          <h4 id="maxProfit" class="text-success">+$0.00</h4>
        </div>
      </div>
    </div>
    <div class="col-md-2">
      <div class="card text-center">
        <div class="card-body p-2">
          <h6 class="card-title">最大亏损</h6>
          <h4 id="maxLoss" class="text-danger">-$0.00</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- 快速操作按钮区 -->
  <div class="card mb-3">
    <div class="card-header">⚡ 快速操作</div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <button class="btn btn-danger btn-block" onclick="closeAllPositions()">
            🚫 平仓所有订单
          </button>
        </div>
        <div class="col-md-3">
          <button class="btn btn-success btn-block" onclick="closeProfitPositions()">
            💰 平仓盈利订单
          </button>
        </div>
        <div class="col-md-3">
          <button class="btn btn-warning btn-block" onclick="closeLossPositions()">
            📉 平仓亏损订单
          </button>
        </div>
        <div class="col-md-3">
          <button class="btn btn-info btn-block" onclick="showBatchModifyModal()">
            ✏️ 批量修改止盈止损
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 批量操作和过滤区 -->
  <div class="card mb-3">
    <div class="card-header">🔧 批量操作和过滤</div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <div class="btn-group" role="group">
            <button class="btn btn-outline-secondary" onclick="selectAllPositions()">
              ☑️ 全选
            </button>
            <button class="btn btn-outline-success" onclick="selectProfitPositions()">
              💚 选择盈利
            </button>
            <button class="btn btn-outline-danger" onclick="selectLossPositions()">
              🔴 选择亏损
            </button>
            <button class="btn btn-danger" onclick="closeBatchPositions()">
              🗑️ 批量平仓
            </button>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-4">
              <select class="form-control form-control-sm" id="symbolFilter" onchange="filterPositions()">
                <option value="">所有交易对</option>
                <option value="XAUUSD">XAUUSD</option>
                <option value="BTCUSD">BTCUSD</option>
                <option value="ETHUSD">ETHUSD</option>
                <!-- 其他交易对选项 -->
              </select>
            </div>
            <div class="col-md-4">
              <select class="form-control form-control-sm" id="directionFilter" onchange="filterPositions()">
                <option value="">所有方向</option>
                <option value="BUY">做多</option>
                <option value="SELL">做空</option>
              </select>
            </div>
            <div class="col-md-4">
              <input type="number" class="form-control form-control-sm" id="minProfitFilter"
                     placeholder="最小盈利(USD)" onchange="filterPositions()">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 13.4 持仓订单表格设计
```html
  <!-- 持仓订单表格 -->
  <div class="card">
    <div class="card-header">
      📋 持仓订单列表
      <small class="text-muted">(<span id="filteredCount">0</span>/<span id="totalCount">0</span>)</small>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-striped table-hover mb-0" id="positionsTable">
          <thead class="thead-dark">
            <tr>
              <th width="40"><input type="checkbox" id="selectAll"></th>
              <th>订单号</th>
              <th>交易对</th>
              <th>方向</th>
              <th>手数</th>
              <th>开仓价</th>
              <th>当前价</th>
              <th>止损价</th>
              <th>止盈价</th>
              <th>止损金额</th>
              <th>止盈金额</th>
              <th>盈亏(USD)</th>
              <th>持仓时间</th>
              <th width="200">操作</th>
            </tr>
          </thead>
          <tbody id="positionsTableBody">
            <!-- 动态生成的订单行 -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- 单个订单行模板 -->
<template id="positionRowTemplate">
  <tr class="position-row" data-ticket="{ticket}" data-symbol="{symbol}" data-direction="{direction}" data-profit="{profit_usd}">
    <td><input type="checkbox" class="position-checkbox" value="{ticket}"></td>
    <td class="ticket font-weight-bold">{ticket}</td>
    <td class="symbol">
      <span class="badge badge-secondary">{symbol}</span>
    </td>
    <td class="direction">
      <span class="badge {directionClass}">{direction}</span>
    </td>
    <td class="lot-size">{lot_size}</td>
    <td class="open-price">{open_price}</td>
    <td class="current-price font-weight-bold" id="currentPrice_{ticket}">{current_price}</td>
    <td class="stop-loss">
      <span class="editable-field" data-field="stop_loss" data-ticket="{ticket}">{stop_loss}</span>
    </td>
    <td class="take-profit">
      <span class="editable-field" data-field="take_profit" data-ticket="{ticket}">{take_profit}</span>
    </td>
    <td class="stop-loss-usd">
      <span class="editable-field" data-field="stop_loss_usd" data-ticket="{ticket}">${stop_loss_usd}</span>
    </td>
    <td class="take-profit-usd">
      <span class="editable-field" data-field="take_profit_usd" data-ticket="{ticket}">${take_profit_usd}</span>
    </td>
    <td class="profit {profitClass}" id="profit_{ticket}">
      <strong>{profit_usd}</strong>
    </td>
    <td class="duration" id="duration_{ticket}">{duration}</td>
    <td class="actions">
      <div class="btn-group btn-group-sm">
        <button class="btn btn-danger" onclick="closePosition({ticket})" title="平仓">
          🗑️
        </button>
        <button class="btn btn-info" onclick="showModifyModal({ticket})" title="修改">
          ✏️
        </button>
        <button class="btn btn-warning" onclick="showQuickModifyModal({ticket})" title="快速修改金额">
          💰
        </button>
      </div>
    </td>
  </tr>
</template>
```

#### 13.5 订单修改弹窗设计
```html
<!-- 详细修改订单弹窗 -->
<div class="modal fade" id="modifyPositionModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          ✏️ 修改订单 #<span id="modifyTicket"></span>
          <small class="text-muted">(<span id="modifySymbol"></span>)</small>
        </h5>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 当前订单信息 -->
        <div class="card mb-3">
          <div class="card-header">📊 当前订单信息</div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <strong>方向:</strong> <span id="currentDirection"></span>
              </div>
              <div class="col-md-3">
                <strong>手数:</strong> <span id="currentLotSize"></span>
              </div>
              <div class="col-md-3">
                <strong>开仓价:</strong> <span id="currentOpenPrice"></span>
              </div>
              <div class="col-md-3">
                <strong>当前盈亏:</strong> <span id="currentProfit"></span>
              </div>
            </div>
          </div>
        </div>

        <form id="modifyPositionForm">
          <!-- 止损设置 -->
          <div class="card mb-3">
            <div class="card-header">🛡️ 止损设置</div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <label>止损价格:</label>
                  <input type="number" class="form-control" id="modifyStopLoss" step="0.00001"
                         onchange="calculateStopLossUSD()">
                  <small class="text-muted">当前: <span id="currentStopLoss"></span></small>
                </div>
                <div class="col-md-6">
                  <label>止损金额(USD):</label>
                  <input type="number" class="form-control" id="modifyStopLossUSD" step="0.01"
                         onchange="calculateStopLossPrice()">
                  <small class="text-muted">当前: <span id="currentStopLossUSD"></span></small>
                </div>
              </div>
            </div>
          </div>

          <!-- 止盈设置 -->
          <div class="card mb-3">
            <div class="card-header">🎯 止盈设置</div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <label>止盈价格:</label>
                  <input type="number" class="form-control" id="modifyTakeProfit" step="0.00001"
                         onchange="calculateTakeProfitUSD()">
                  <small class="text-muted">当前: <span id="currentTakeProfit"></span></small>
                </div>
                <div class="col-md-6">
                  <label>止盈金额(USD):</label>
                  <input type="number" class="form-control" id="modifyTakeProfitUSD" step="0.01"
                         onchange="calculateTakeProfitPrice()">
                  <small class="text-muted">当前: <span id="currentTakeProfitUSD"></span></small>
                </div>
              </div>
            </div>
          </div>

          <!-- 预览计算 -->
          <div class="card">
            <div class="card-header">📈 修改预览</div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <strong>新止损风险:</strong> <span id="previewStopLossRisk" class="text-danger"></span>
                </div>
                <div class="col-md-6">
                  <strong>新止盈收益:</strong> <span id="previewTakeProfitGain" class="text-success"></span>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-md-6">
                  <strong>风险回报比:</strong> <span id="riskRewardRatio"></span>
                </div>
                <div class="col-md-6">
                  <strong>距离当前价:</strong> <span id="distanceFromCurrent"></span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-warning" onclick="resetToOriginal()">重置</button>
        <button type="button" class="btn btn-primary" onclick="savePositionModify()">保存修改</button>
      </div>
    </div>
  </div>
</div>

<!-- 快速修改金额弹窗 -->
<div class="modal fade" id="quickModifyModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">💰 快速修改止盈止损金额</h5>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-6">
            <label>止损金额(USD):</label>
            <input type="number" class="form-control" id="quickStopLossUSD" step="0.01">
          </div>
          <div class="col-md-6">
            <label>止盈金额(USD):</label>
            <input type="number" class="form-control" id="quickTakeProfitUSD" step="0.01">
          </div>
        </div>
        <div class="mt-3">
          <div class="btn-group btn-group-sm w-100">
            <button class="btn btn-outline-secondary" onclick="setQuickAmount(25, 50)">25/50</button>
            <button class="btn btn-outline-secondary" onclick="setQuickAmount(50, 100)">50/100</button>
            <button class="btn btn-outline-secondary" onclick="setQuickAmount(100, 200)">100/200</button>
            <button class="btn btn-outline-secondary" onclick="setQuickAmount(200, 400)">200/400</button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="applyQuickModify()">应用</button>
      </div>
    </div>
  </div>
</div>

<!-- 批量修改弹窗 -->
<div class="modal fade" id="batchModifyModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">🔧 批量修改止盈止损</h5>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label>应用范围:</label>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="batchScope" id="batchSelected" value="selected" checked>
            <label class="form-check-label" for="batchSelected">选中的订单</label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="batchScope" id="batchAll" value="all">
            <label class="form-check-label" for="batchAll">所有订单</label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="batchScope" id="batchSymbol" value="symbol">
            <label class="form-check-label" for="batchSymbol">指定交易对</label>
            <select class="form-control mt-1" id="batchSymbolSelect" disabled>
              <option value="">选择交易对</option>
            </select>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <label>统一止损金额(USD):</label>
            <input type="number" class="form-control" id="batchStopLossUSD" step="0.01">
          </div>
          <div class="col-md-6">
            <label>统一止盈金额(USD):</label>
            <input type="number" class="form-control" id="batchTakeProfitUSD" step="0.01">
          </div>
        </div>

        <div class="mt-3">
          <small class="text-muted">
            ⚠️ 批量修改将覆盖选中订单的当前止盈止损设置
          </small>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="applyBatchModify()">批量应用</button>
      </div>
    </div>
  </div>
</div>
```

#### 13.6 实时更新JavaScript实现
```javascript
// 实时数据管理类
class RealTimeDataManager {
  constructor() {
    this.websocket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.autoRefreshInterval = null;
    this.autoRefreshEnabled = true;
    this.currentPage = 'dashboard';
    this.init();
  }

  init() {
    this.connectWebSocket();
    this.startAutoRefresh();
    this.bindEvents();
  }

  // WebSocket连接
  connectWebSocket() {
    try {
      this.websocket = new WebSocket('ws://localhost:7001/ws');

      this.websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        this.reconnectAttempts = 0;
        this.updateConnectionStatus(true);
      };

      this.websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleRealtimeData(data);
      };

      this.websocket.onclose = () => {
        console.log('WebSocket连接已断开');
        this.updateConnectionStatus(false);
        this.attemptReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        this.updateConnectionStatus(false);
      };
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      this.updateConnectionStatus(false);
    }
  }

  // 处理实时数据
  handleRealtimeData(data) {
    switch (data.type) {
      case 'account_update':
        this.updateAccountInfo(data.data);
        break;
      case 'position_update':
        this.updatePositionData(data.data);
        break;
      case 'price_update':
        this.updatePrices(data.data);
        break;
      case 'alert_received':
        this.handleNewAlert(data.data);
        break;
      case 'system_status':
        this.updateSystemStatus(data.data);
        break;
    }
  }

  // 更新账户信息
  updateAccountInfo(accountData) {
    if (this.currentPage === 'dashboard') {
      document.getElementById('accountBalance').textContent = `$${accountData.balance.toFixed(2)}`;
      document.getElementById('accountEquity').textContent = `$${accountData.equity.toFixed(2)}`;
      document.getElementById('usedMargin').textContent = `$${accountData.margin.toFixed(2)}`;
      document.getElementById('freeMargin').textContent = `$${accountData.margin_free.toFixed(2)}`;

      // 更新浮动盈亏
      const floatingPnL = accountData.equity - accountData.balance;
      const floatingElement = document.getElementById('floatingPnL');
      floatingElement.textContent = `${floatingPnL >= 0 ? '+' : ''}$${floatingPnL.toFixed(2)}`;
      floatingElement.className = floatingPnL >= 0 ? 'text-success' : 'text-danger';

      // 更新保证金比例
      const marginLevel = accountData.margin > 0 ? (accountData.equity / accountData.margin * 100) : 0;
      document.getElementById('marginLevel').textContent = `${marginLevel.toFixed(1)}%`;
    }
  }

  // 更新持仓数据
  updatePositionData(positions) {
    if (this.currentPage === 'positions') {
      // 更新统计卡片
      const totalPositions = positions.length;
      const profitPositions = positions.filter(p => p.profit > 0).length;
      const lossPositions = positions.filter(p => p.profit < 0).length;
      const totalPnL = positions.reduce((sum, p) => sum + p.profit, 0);
      const maxProfit = Math.max(...positions.map(p => p.profit), 0);
      const maxLoss = Math.min(...positions.map(p => p.profit), 0);

      document.getElementById('totalPositions').textContent = totalPositions;
      document.getElementById('profitPositions').textContent = profitPositions;
      document.getElementById('lossPositions').textContent = lossPositions;

      const totalPnLElement = document.getElementById('totalPnL');
      totalPnLElement.textContent = `${totalPnL >= 0 ? '+' : ''}$${totalPnL.toFixed(2)}`;
      totalPnLElement.className = totalPnL >= 0 ? 'text-success' : 'text-danger';

      document.getElementById('maxProfit').textContent = `+$${maxProfit.toFixed(2)}`;
      document.getElementById('maxLoss').textContent = `$${maxLoss.toFixed(2)}`;

      // 更新表格中的实时数据
      positions.forEach(position => {
        this.updatePositionRow(position);
      });
    }
  }

  // 更新单个持仓行
  updatePositionRow(position) {
    const currentPriceElement = document.getElementById(`currentPrice_${position.ticket}`);
    const profitElement = document.getElementById(`profit_${position.ticket}`);
    const durationElement = document.getElementById(`duration_${position.ticket}`);

    if (currentPriceElement) {
      currentPriceElement.textContent = position.current_price.toFixed(5);
    }

    if (profitElement) {
      profitElement.innerHTML = `<strong>${position.profit >= 0 ? '+' : ''}$${position.profit.toFixed(2)}</strong>`;
      profitElement.className = `profit ${position.profit >= 0 ? 'text-success' : 'text-danger'}`;
    }

    if (durationElement) {
      durationElement.textContent = this.calculateDuration(position.open_time);
    }
  }

  // 计算持仓时间
  calculateDuration(openTime) {
    const now = new Date();
    const open = new Date(openTime);
    const diff = now - open;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}天${hours % 24}时`;
    } else if (hours > 0) {
      return `${hours}时${minutes}分`;
    } else {
      return `${minutes}分钟`;
    }
  }

  // 自动刷新
  startAutoRefresh() {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
    }

    let countdown = 30;
    this.autoRefreshInterval = setInterval(() => {
      if (this.autoRefreshEnabled) {
        countdown--;
        const countdownElement = document.getElementById('autoRefreshCountdown');
        if (countdownElement) {
          countdownElement.textContent = countdown;
        }

        if (countdown <= 0) {
          this.refreshCurrentPageData();
          countdown = 30;
        }
      }
    }, 1000);
  }

  // 刷新当前页面数据
  async refreshCurrentPageData() {
    try {
      if (this.currentPage === 'dashboard') {
        await this.refreshDashboardData();
      } else if (this.currentPage === 'positions') {
        await this.refreshPositionsData();
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  }

  // 刷新仪表盘数据
  async refreshDashboardData() {
    const [accountResponse, statsResponse] = await Promise.all([
      fetch('/api/account/info'),
      fetch('/api/stats/daily')
    ]);

    const accountData = await accountResponse.json();
    const statsData = await statsResponse.json();

    this.updateAccountInfo(accountData);
    this.updateDailyStats(statsData);
  }

  // 刷新持仓数据
  async refreshPositionsData() {
    const response = await fetch('/api/positions');
    const positions = await response.json();
    this.updatePositionData(positions);
  }
}

// 订单管理类
class OrderManager extends RealTimeDataManager {
  constructor() {
    super();
    this.selectedPositions = new Set();
    this.positions = [];
    this.currentModifyingTicket = null;
  }

  // 平仓所有订单
  async closeAllPositions() {
    if (!confirm('确定要平仓所有订单吗？')) return;

    try {
      const response = await fetch('/api/positions/close-all', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          reason: '手动平仓所有订单',
          send_notification: true
        })
      });

      if (response.ok) {
        this.showSuccess('所有订单已平仓');
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('平仓失败: ' + error.message);
    }
  }

  // 平仓盈利订单
  async closeProfitPositions() {
    const minProfit = prompt('请输入最小盈利金额(USD):', '10');
    if (!minProfit) return;

    try {
      const response = await fetch('/api/positions/close-profit', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          min_profit_usd: parseFloat(minProfit),
          reason: '平仓盈利订单',
          send_notification: true
        })
      });

      if (response.ok) {
        this.showSuccess('盈利订单已平仓');
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('平仓失败: ' + error.message);
    }
  }

  // 平仓亏损订单
  async closeLossPositions() {
    const maxLoss = prompt('请输入最大亏损金额(USD):', '-50');
    if (!maxLoss) return;

    try {
      const response = await fetch('/api/positions/close-loss', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          max_loss_usd: parseFloat(maxLoss),
          reason: '平仓亏损订单',
          send_notification: true
        })
      });

      if (response.ok) {
        this.showSuccess('亏损订单已平仓');
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('平仓失败: ' + error.message);
    }
  }

  // 平仓单个订单
  async closePosition(ticket) {
    if (!confirm(`确定要平仓订单 #${ticket} 吗？`)) return;

    try {
      const response = await fetch(`/api/positions/${ticket}/close`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          reason: '手动平仓',
          send_notification: true
        })
      });

      if (response.ok) {
        this.showSuccess(`订单 #${ticket} 已平仓`);
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('平仓失败: ' + error.message);
    }
  }

  // 批量平仓选中订单
  async closeBatchPositions() {
    if (this.selectedPositions.size === 0) {
      this.showWarning('请先选择要平仓的订单');
      return;
    }

    if (!confirm(`确定要平仓选中的 ${this.selectedPositions.size} 个订单吗？`)) return;

    try {
      const response = await fetch('/api/positions/batch-close', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          tickets: Array.from(this.selectedPositions),
          reason: '批量手动平仓',
          send_notification: true
        })
      });

      if (response.ok) {
        this.showSuccess('选中订单已平仓');
        this.selectedPositions.clear();
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('批量平仓失败: ' + error.message);
    }
  }

  // 刷新持仓列表
  async refreshPositions() {
    try {
      const response = await fetch('/api/positions');
      this.positions = await response.json();
      this.renderPositionsTable();
    } catch (error) {
      this.showError('刷新失败: ' + error.message);
    }
  }

  // 渲染持仓表格
  renderPositionsTable() {
    const tbody = document.getElementById('positionsTableBody');
    tbody.innerHTML = '';

    this.positions.forEach(position => {
      const row = this.createPositionRow(position);
      tbody.appendChild(row);
    });
  }
}

  // 快速修改止盈止损金额
  showQuickModifyModal(ticket) {
    this.currentModifyingTicket = ticket;
    const position = this.positions.find(p => p.ticket === ticket);

    if (position) {
      document.getElementById('quickStopLossUSD').value = position.stop_loss_usd || '';
      document.getElementById('quickTakeProfitUSD').value = position.take_profit_usd || '';
      $('#quickModifyModal').modal('show');
    }
  }

  // 应用快速修改
  async applyQuickModify() {
    const stopLossUSD = parseFloat(document.getElementById('quickStopLossUSD').value);
    const takeProfitUSD = parseFloat(document.getElementById('quickTakeProfitUSD').value);

    if (!stopLossUSD || !takeProfitUSD) {
      this.showError('请输入有效的止损和止盈金额');
      return;
    }

    try {
      const response = await fetch(`/api/positions/${this.currentModifyingTicket}/modify`, {
        method: 'PUT',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          stop_loss_usd: stopLossUSD,
          take_profit_usd: takeProfitUSD,
          modify_type: 'amount_only'
        })
      });

      if (response.ok) {
        this.showSuccess('止盈止损金额已更新');
        $('#quickModifyModal').modal('hide');
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('修改失败: ' + error.message);
    }
  }

  // 批量修改止盈止损
  async applyBatchModify() {
    const scope = document.querySelector('input[name="batchScope"]:checked').value;
    const stopLossUSD = parseFloat(document.getElementById('batchStopLossUSD').value);
    const takeProfitUSD = parseFloat(document.getElementById('batchTakeProfitUSD').value);

    if (!stopLossUSD || !takeProfitUSD) {
      this.showError('请输入有效的止损和止盈金额');
      return;
    }

    let tickets = [];
    if (scope === 'selected') {
      tickets = Array.from(this.selectedPositions);
      if (tickets.length === 0) {
        this.showError('请先选择要修改的订单');
        return;
      }
    } else if (scope === 'symbol') {
      const symbol = document.getElementById('batchSymbolSelect').value;
      if (!symbol) {
        this.showError('请选择交易对');
        return;
      }
      tickets = this.positions.filter(p => p.symbol === symbol).map(p => p.ticket);
    } else {
      tickets = this.positions.map(p => p.ticket);
    }

    try {
      const response = await fetch('/api/positions/batch-modify', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          tickets: tickets,
          stop_loss_usd: stopLossUSD,
          take_profit_usd: takeProfitUSD,
          reason: '批量修改止盈止损'
        })
      });

      if (response.ok) {
        this.showSuccess(`已批量修改 ${tickets.length} 个订单的止盈止损`);
        $('#batchModifyModal').modal('hide');
        this.refreshPositions();
      }
    } catch (error) {
      this.showError('批量修改失败: ' + error.message);
    }
  }
}

// 页面管理类
class PageManager {
  constructor() {
    this.currentPage = 'dashboard';
    this.orderManager = new OrderManager();
    this.init();
  }

  init() {
    // 绑定导航事件
    this.bindNavigationEvents();
    // 显示默认页面
    this.showPage('dashboard');
    // 启动实时更新
    this.orderManager.init();
  }

  bindNavigationEvents() {
    // 导航链接点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.getAttribute('href').substring(1);
        this.showPage(page);
      });
    });

    // 紧急停止按钮
    document.getElementById('emergencyStop').addEventListener('click', () => {
      this.toggleEmergencyStop();
    });
  }

  showPage(pageName) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(page => {
      page.style.display = 'none';
    });

    // 显示目标页面
    const targetPage = document.getElementById(pageName + 'Page');
    if (targetPage) {
      targetPage.style.display = 'block';
      this.currentPage = pageName;
      this.orderManager.currentPage = pageName;

      // 更新导航状态
      document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
      });
      document.querySelector(`[href="#${pageName}"]`).classList.add('active');

      // 加载页面数据
      this.loadPageData(pageName);
    }
  }

  async loadPageData(pageName) {
    try {
      switch (pageName) {
        case 'dashboard':
          await this.orderManager.refreshDashboardData();
          break;
        case 'positions':
          await this.orderManager.refreshPositionsData();
          break;
        case 'config':
          await this.loadConfigData();
          break;
        case 'history':
          await this.loadHistoryData();
          break;
        case 'alerts':
          await this.loadAlertsData();
          break;
        case 'logs':
          await this.loadLogsData();
          break;
      }
    } catch (error) {
      console.error(`加载${pageName}页面数据失败:`, error);
    }
  }

  async toggleEmergencyStop() {
    try {
      const response = await fetch('/api/system/emergency-stop', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
      });

      const result = await response.json();

      if (response.ok) {
        const button = document.getElementById('emergencyStop');
        const status = document.getElementById('emergencyStatus');

        if (result.enabled) {
          button.textContent = '🟢 恢复交易';
          button.className = 'btn btn-success btn-sm ml-2';
          status.textContent = '启用';
          status.className = 'badge badge-danger';
        } else {
          button.textContent = '🚨 紧急停止';
          button.className = 'btn btn-danger btn-sm ml-2';
          status.textContent = '关闭';
          status.className = 'badge badge-secondary';
        }
      }
    } catch (error) {
      console.error('切换紧急停止状态失败:', error);
    }
  }
}

// 初始化页面管理器
document.addEventListener('DOMContentLoaded', () => {
  window.pageManager = new PageManager();
});

// 全局函数（供HTML调用）
function showPage(pageName) {
  window.pageManager.showPage(pageName);
}

function closeAllPositions() {
  window.pageManager.orderManager.closeAllPositions();
}

function closeProfitPositions() {
  window.pageManager.orderManager.closeProfitPositions();
}

function closeLossPositions() {
  window.pageManager.orderManager.closeLossPositions();
}

function closePosition(ticket) {
  window.pageManager.orderManager.closePosition(ticket);
}

function showModifyModal(ticket) {
  window.pageManager.orderManager.showModifyModal(ticket);
}

function showQuickModifyModal(ticket) {
  window.pageManager.orderManager.showQuickModifyModal(ticket);
}

function showBatchModifyModal() {
  $('#batchModifyModal').modal('show');
}
```

#### 16.5 认证相关JavaScript函数

```javascript
// 显示修改密码模态框
function showChangePasswordModal() {
    document.getElementById('changePasswordForm').reset();
    $('#changePasswordModal').modal('show');
}

// 修改密码
async function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('请填写所有字段');
        return;
    }

    if (newPassword !== confirmPassword) {
        alert('新密码和确认密码不匹配');
        return;
    }

    if (newPassword.length < 8) {
        alert('新密码长度不能少于8位');
        return;
    }

    try {
        const response = await fetch('/api/auth/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('密码修改成功');
            $('#changePasswordModal').modal('hide');
        } else {
            alert('密码修改失败: ' + result.message);
        }
    } catch (error) {
        alert('密码修改失败: ' + error.message);
    }
}

// 显示创建管理员模态框
function showCreateAdminModal() {
    document.getElementById('createAdminForm').reset();
    $('#createAdminModal').modal('show');
}

// 创建管理员账户
async function createAdmin() {
    const username = document.getElementById('adminUsername').value;
    const password = document.getElementById('adminPassword').value;
    const email = document.getElementById('adminEmail').value;

    if (!username || !password) {
        alert('用户名和密码不能为空');
        return;
    }

    if (password.length < 8) {
        alert('密码长度不能少于8位');
        return;
    }

    try {
        const response = await fetch('/api/auth/create-admin', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password,
                email: email || null
            })
        });

        const result = await response.json();

        if (result.success) {
            alert('管理员账户创建成功');
            $('#createAdminModal').modal('hide');
        } else {
            alert('创建失败: ' + result.message);
        }
    } catch (error) {
        alert('创建失败: ' + error.message);
    }
}
```

### 17. Web管理页面特性总结

#### 17.1 模块化设计
- ✅ **独立页面模块**: 仪表盘、订单管理、配置管理、交易历史、警报记录、系统日志
- ✅ **组件化开发**: 每个功能区域都是独立的组件
- ✅ **响应式布局**: 适配不同屏幕尺寸
- ✅ **统一导航**: 顶部导航栏快速切换页面

#### 17.2 实时更新功能
- ✅ **WebSocket连接**: 实时接收服务器数据推送
- ✅ **自动刷新机制**: 30秒自动刷新数据
- ✅ **实时账户信息**: 余额、净值、保证金实时更新
- ✅ **实时盈亏显示**: 持仓盈亏实时计算和显示
- ✅ **连接状态监控**: 显示WebSocket连接状态

#### 17.3 订单管理增强
- ✅ **多种修改方式**: 详细修改、快速修改金额、批量修改
- ✅ **实时统计**: 持仓数量、总盈亏、盈利/亏损订单统计
- ✅ **智能过滤**: 按交易对、方向、盈利金额过滤
- ✅ **批量操作**: 支持批量选择和批量平仓
- ✅ **预览计算**: 修改前预览风险回报比

#### 17.4 用户体验优化
- ✅ **直观的界面**: 卡片式布局，信息层次清晰
- ✅ **实时反馈**: 操作结果即时反馈
- ✅ **安全确认**: 危险操作多重确认
- ✅ **快捷操作**: 常用功能一键执行
- ✅ **状态指示**: 系统状态实时显示

这个完整的模块化Web管理系统提供了专业级的交易管理功能，实现了实时数据更新、灵活的订单管理和优秀的用户体验。
```

### 18. 配置管理页面设计

#### 18.1 响应式配置页面布局
```html
<!-- 配置管理页面 -->
<div id="configPage" class="page-content" style="display: none;">
  <!-- 页面标题 -->
  <div class="row mb-3">
    <div class="col-12">
      <h3>⚙️ 配置管理</h3>
      <p class="text-muted">所有配置修改后实时生效，无需重启程序</p>
    </div>
  </div>

  <!-- 移动端优化的配置导航 -->
  <div class="row mb-4">
    <div class="col-12">
      <!-- 桌面端标签导航 -->
      <ul class="nav nav-pills nav-fill d-none d-md-flex" id="configTabs" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="symbols-tab" data-toggle="pill" href="#symbols-config" role="tab">
            📊 交易对配置
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="global-tab" data-toggle="pill" href="#global-config" role="tab">
            🌐 全局配置
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="bark-tab" data-toggle="pill" href="#bark-config" role="tab">
            🔔 通知配置
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="risk-tab" data-toggle="pill" href="#risk-config" role="tab">
            🛡️ 风险控制
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="system-tab" data-toggle="pill" href="#system-config" role="tab">
            🔧 系统设置
          </a>
        </li>
      </ul>

      <!-- 移动端下拉选择 -->
      <select class="form-control d-md-none" id="mobileConfigSelect" onchange="switchConfigTab(this.value)">
        <option value="symbols-config">📊 交易对配置</option>
        <option value="global-config">🌐 全局配置</option>
        <option value="bark-config">🔔 通知配置</option>
        <option value="risk-config">🛡️ 风险控制</option>
        <option value="system-config">🔧 系统设置</option>
      </select>
    </div>
  </div>

  <!-- 配置内容区域 -->
  <div class="tab-content" id="configTabContent">

    <!-- 交易对配置 -->
    <div class="tab-pane fade show active" id="symbols-config" role="tabpanel">
      <div class="row">
        <!-- 移动端优化：在小屏幕上垂直排列 -->
        <div class="col-lg-8 col-md-12 mb-3">
          <!-- 交易对列表 -->
          <div class="card">
            <div class="card-header d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
              <h5 class="mb-2 mb-md-0">📊 交易对配置</h5>
              <div class="btn-group btn-group-sm w-100 w-md-auto">
                <button class="btn btn-success" onclick="enableAllSymbols()">全部启用</button>
                <button class="btn btn-warning" onclick="disableAllSymbols()">全部禁用</button>
                <button class="btn btn-info" onclick="resetToDefaults()">恢复默认</button>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- 移动端优化的表格 -->
              <div class="table-responsive">
                <table class="table table-hover mb-0" id="symbolsConfigTable">
                  <thead class="thead-light">
                    <tr>
                      <th class="d-none d-md-table-cell" width="80">启用</th>
                      <th>交易对</th>
                      <th class="d-none d-lg-table-cell">分类</th>
                      <th width="80">手数</th>
                      <th width="100">止损</th>
                      <th width="100">止盈</th>
                      <th class="d-none d-md-table-cell" width="80">超时</th>
                      <th width="60">操作</th>
                    </tr>
                  </thead>
                  <tbody id="symbolsConfigBody">
                    <!-- 动态生成配置行 -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div class="col-lg-4 col-md-12">
          <!-- 快速配置面板 -->
          <div class="card mb-3">
            <div class="card-header">⚡ 快速配置</div>
            <div class="card-body">
              <div class="form-group">
                <label>按分类批量设置:</label>
                <select class="form-control form-control-sm" id="categorySelect">
                  <option value="">选择分类</option>
                  <option value="crypto">加密货币</option>
                  <option value="forex">外汇</option>
                  <option value="precious_metals">贵金属</option>
                </select>
              </div>
              <div class="row">
                <div class="col-6">
                  <div class="form-group">
                    <label>手数:</label>
                    <input type="number" class="form-control form-control-sm" id="batchLotSize" step="0.01">
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-group">
                    <label>超时(秒):</label>
                    <input type="number" class="form-control form-control-sm" id="batchTimeout" value="180">
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-6">
                  <div class="form-group">
                    <label>止损(USD):</label>
                    <input type="number" class="form-control form-control-sm" id="batchStopLoss" step="1">
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-group">
                    <label>止盈(USD):</label>
                    <input type="number" class="form-control form-control-sm" id="batchTakeProfit" step="1">
                  </div>
                </div>
              </div>
              <button class="btn btn-primary btn-block btn-sm" onclick="applyBatchConfig()">
                应用批量配置
              </button>
            </div>
          </div>

          <!-- 配置模板 -->
          <div class="card">
            <div class="card-header">📋 配置模板</div>
            <div class="card-body">
              <div class="btn-group-vertical w-100">
                <button class="btn btn-outline-secondary btn-sm mb-1" onclick="applyTemplate('conservative')">
                  🛡️ 保守模式
                </button>
                <button class="btn btn-outline-secondary btn-sm mb-1" onclick="applyTemplate('balanced')">
                  ⚖️ 平衡模式
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="applyTemplate('aggressive')">
                  🚀 激进模式
                </button>
              </div>
              <small class="text-muted mt-2 d-block">
                模板会根据交易对特性自动设置合适的参数
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统设置 -->
    <div class="tab-pane fade" id="system-config" role="tabpanel">
      <div class="row">
        <div class="col-lg-6 col-md-12">
          <div class="card">
            <div class="card-header">🔧 系统运行参数</div>
            <div class="card-body">
              <div class="form-group">
                <label>Webhook监听端口:</label>
                <input type="number" class="form-control" id="webhookPort" min="1000" max="65535" value="7000">
                <small class="text-muted">接收TradingView警报的端口</small>
              </div>
              <div class="form-group">
                <label>Web管理端口:</label>
                <input type="number" class="form-control" id="webPort" min="1000" max="65535" value="7001">
                <small class="text-muted">Web管理界面访问端口</small>
              </div>
              <div class="form-group">
                <label>日志级别:</label>
                <select class="form-control" id="logLevel">
                  <option value="DEBUG">DEBUG - 详细调试</option>
                  <option value="INFO" selected>INFO - 一般信息</option>
                  <option value="WARNING">WARNING - 警告信息</option>
                  <option value="ERROR">ERROR - 错误信息</option>
                </select>
              </div>
              <div class="form-group">
                <label>数据库备份间隔(小时):</label>
                <select class="form-control" id="backupInterval">
                  <option value="6">6小时</option>
                  <option value="12" selected>12小时</option>
                  <option value="24">24小时</option>
                  <option value="168">7天</option>
                </select>
              </div>
              <button class="btn btn-primary" onclick="saveSystemConfig()">保存系统配置</button>
            </div>
          </div>
        </div>

        <div class="col-lg-6 col-md-12">
          <div class="card">
            <div class="card-header">📱 界面设置</div>
            <div class="card-body">
              <div class="form-group">
                <label>界面主题:</label>
                <select class="form-control" id="uiTheme">
                  <option value="light" selected>浅色主题</option>
                  <option value="dark">深色主题</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>
              <div class="form-group">
                <label>数据刷新间隔(秒):</label>
                <select class="form-control" id="refreshInterval">
                  <option value="10">10秒</option>
                  <option value="30" selected>30秒</option>
                  <option value="60">60秒</option>
                  <option value="120">2分钟</option>
                </select>
              </div>
              <div class="form-group">
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="soundEnabled" checked>
                  <label class="custom-control-label" for="soundEnabled">
                    启用声音提示
                  </label>
                </div>
              </div>
              <div class="form-group">
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="mobileOptimized" checked>
                  <label class="custom-control-label" for="mobileOptimized">
                    移动端优化
                  </label>
                </div>
              </div>
              <button class="btn btn-primary" onclick="saveUIConfig()">保存界面设置</button>
            </div>
          </div>

          <div class="card mt-3">
            <div class="card-header">🔗 系统信息</div>
            <div class="card-body">
              <div class="row">
                <div class="col-6">
                  <strong>Webhook地址:</strong>
                </div>
                <div class="col-6">
                  <code>http://localhost:7000/webhook/tradingview</code>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-6">
                  <strong>Web管理地址:</strong>
                </div>
                <div class="col-6">
                  <code>http://localhost:7001</code>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-6">
                  <strong>WebSocket地址:</strong>
                </div>
                <div class="col-6">
                  <code>ws://localhost:7001/ws</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 18.2 实时配置生效机制

```python
# 配置管理类
class ConfigManager:
    def __init__(self):
        self.config_cache = {}
        self.config_watchers = {}
        self.websocket_clients = set()

    def update_config(self, config_type, config_data):
        """更新配置并实时生效"""
        try:
            # 验证配置
            self.validate_config(config_type, config_data)

            # 保存到数据库
            self.save_config_to_db(config_type, config_data)

            # 更新缓存
            self.config_cache[config_type] = config_data

            # 通知相关组件
            self.notify_config_change(config_type, config_data)

            # 推送到Web客户端
            self.broadcast_config_update(config_type, config_data)

            return {'success': True, 'message': '配置已更新并生效'}

        except Exception as e:
            return {'success': False, 'message': str(e)}

    def notify_config_change(self, config_type, config_data):
        """通知相关组件配置变更"""
        if config_type == 'symbols':
            # 更新交易引擎配置
            trading_engine.update_symbol_configs(config_data)
        elif config_type == 'global_sl_tp':
            # 更新组合监控配置
            portfolio_monitor.update_config(config_data)
        elif config_type == 'bark':
            # 更新通知服务配置
            notification_service.update_bark_config(config_data)
        elif config_type == 'risk':
            # 更新风险控制配置
            risk_manager.update_config(config_data)
        elif config_type == 'system':
            # 更新系统配置
            system_manager.update_config(config_data)

# 配置API端点
@app.route('/api/config/<config_type>', methods=['PUT'])
def update_config_api(config_type):
    config_data = request.json
    result = config_manager.update_config(config_type, config_data)

    if result['success']:
        return jsonify(result), 200
    else:
        return jsonify(result), 400

# WebSocket配置更新推送
def broadcast_config_update(config_type, config_data):
    message = {
        'type': 'config_update',
        'config_type': config_type,
        'data': config_data,
        'timestamp': datetime.now().isoformat()
    }

    for client in websocket_clients:
        try:
            client.send(json.dumps(message))
        except:
            websocket_clients.discard(client)
```

#### 18.3 移动端响应式优化

```css
/* 移动端优化样式 */
@media (max-width: 768px) {
  /* 导航优化 */
  .navbar-brand {
    font-size: 1rem;
  }

  /* 卡片间距优化 */
  .card {
    margin-bottom: 1rem;
  }

  /* 表格优化 */
  .table-responsive {
    font-size: 0.875rem;
  }

  /* 按钮组优化 */
  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-bottom: 0.25rem;
  }

  /* 表单优化 */
  .form-control-sm {
    font-size: 0.875rem;
  }

  /* 模态框优化 */
  .modal-dialog {
    margin: 0.5rem;
  }

  /* 统计卡片优化 */
  .card-body h3, .card-body h4 {
    font-size: 1.25rem;
  }
}

/* 超小屏幕优化 */
@media (max-width: 576px) {
  .container-fluid {
    padding: 0.5rem;
  }

  .card-header {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .card-body {
    padding: 0.75rem;
  }

  /* 隐藏非关键列 */
  .d-none-xs {
    display: none !important;
  }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    padding: 0.5rem 1rem;
  }

  .form-control {
    min-height: 44px;
  }

  .table td, .table th {
    padding: 0.75rem 0.5rem;
  }
}
```

#### 18.4 main.py 统一启动管理

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingView自动交易系统主启动程序
统一管理所有程序组件和功能
"""

import asyncio
import threading
import signal
import sys
import logging
from datetime import datetime
from pathlib import Path

# 导入各个模块
from src.webhook_server import WebhookServer
from src.web_server import WebServer
from src.trading_engine import TradingEngine
from src.portfolio_monitor import PortfolioMonitor
from src.notification_service import NotificationService
from src.config_manager import ConfigManager
from src.database_manager import DatabaseManager
from src.risk_manager import RiskManager
from src.mt5_connector import MT5Connector

class TradingBotManager:
    """交易机器人主管理器"""

    def __init__(self):
        self.logger = self.setup_logging()
        self.components = {}
        self.running = False
        self.shutdown_event = threading.Event()

    def setup_logging(self):
        """设置日志系统"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"trading_bot_{datetime.now().strftime('%Y%m%d')}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)

    def initialize_components(self):
        """初始化所有组件"""
        try:
            self.logger.info("🚀 开始初始化交易机器人系统...")

            # 1. 数据库管理器
            self.logger.info("📊 初始化数据库管理器...")
            self.components['database'] = DatabaseManager()
            self.components['database'].initialize()

            # 1.1 认证管理器
            self.logger.info("🔐 初始化认证管理器...")
            self.components['auth'] = AuthManager(self.components['database'])

            # 1.2 创建默认管理员账户（如果不存在）
            self.create_default_admin()

            # 2. 配置管理器
            self.logger.info("⚙️ 初始化配置管理器...")
            self.components['config'] = ConfigManager()

            # 3. MT5连接器
            self.logger.info("🔌 初始化MT5连接器...")
            self.components['mt5'] = MT5Connector()
            if not self.components['mt5'].connect():
                raise Exception("MT5连接失败")

            # 4. 风险管理器
            self.logger.info("🛡️ 初始化风险管理器...")
            self.components['risk'] = RiskManager(self.components['config'])

            # 5. 交易引擎
            self.logger.info("⚡ 初始化交易引擎...")
            self.components['trading'] = TradingEngine(
                self.components['mt5'],
                self.components['config'],
                self.components['risk']
            )

            # 6. 组合监控器
            self.logger.info("📈 初始化组合监控器...")
            self.components['portfolio'] = PortfolioMonitor(
                self.components['mt5'],
                self.components['config'],
                self.components['trading']
            )

            # 7. 通知服务
            self.logger.info("🔔 初始化通知服务...")
            self.components['notification'] = NotificationService(
                self.components['config']
            )

            # 8. Webhook服务器
            self.logger.info("🌐 初始化Webhook服务器...")
            self.components['webhook'] = WebhookServer(
                self.components['trading'],
                self.components['notification'],
                port=7000  # Webhook服务端口
            )

            # 9. Web管理服务器
            self.logger.info("💻 初始化Web管理服务器...")
            self.components['web'] = WebServer(
                self.components['trading'],
                self.components['config'],
                self.components['portfolio'],
                self.components['notification'],
                port=7001  # Web管理服务端口
            )

            self.logger.info("✅ 所有组件初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 组件初始化失败: {e}")
            return False

    def create_default_admin(self):
        """创建默认管理员账户"""
        try:
            # 检查是否已有管理员账户
            existing_admin = self.components['database'].execute_query(
                "SELECT COUNT(*) FROM admin_users"
            )[0][0]

            if existing_admin == 0:
                # 创建默认管理员账户
                default_username = "admin"
                default_password = "admin123456"  # 建议首次登录后修改

                result = self.components['auth'].create_admin_user(
                    default_username,
                    default_password,
                    "<EMAIL>"
                )

                if result["success"]:
                    self.logger.info(f"✅ 默认管理员账户已创建: {default_username}")
                    self.logger.warning("⚠️ 请首次登录后立即修改默认密码！")
                else:
                    self.logger.error(f"❌ 创建默认管理员失败: {result['message']}")
            else:
                self.logger.info("ℹ️ 管理员账户已存在，跳过创建")

        except Exception as e:
            self.logger.error(f"❌ 创建默认管理员账户失败: {e}")

    def start_services(self):
        """启动所有服务"""
        try:
            self.logger.info("🚀 启动所有服务...")

            # 启动组合监控器（后台任务）
            self.components['portfolio'].start()

            # 启动通知服务定时任务
            self.components['notification'].start_scheduled_tasks()

            # 启动Webhook服务器
            webhook_thread = threading.Thread(
                target=self.components['webhook'].run,
                daemon=True
            )
            webhook_thread.start()

            # 启动Web管理服务器
            web_thread = threading.Thread(
                target=self.components['web'].run,
                daemon=True
            )
            web_thread.start()

            self.running = True
            self.logger.info("✅ 所有服务启动完成")

            # 发送启动通知
            self.components['notification'].send_system_notification(
                "🚀 交易机器人系统启动",
                f"系统于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 成功启动\n"
                f"Webhook端口: 7000\n"
                f"Web管理端口: 7001\n"
                f"访问地址: http://localhost:7001"
            )

            return True

        except Exception as e:
            self.logger.error(f"❌ 服务启动失败: {e}")
            return False

    def stop_services(self):
        """停止所有服务"""
        try:
            self.logger.info("🛑 开始停止所有服务...")

            self.running = False
            self.shutdown_event.set()

            # 停止各个组件
            for name, component in self.components.items():
                try:
                    if hasattr(component, 'stop'):
                        component.stop()
                    self.logger.info(f"✅ {name} 组件已停止")
                except Exception as e:
                    self.logger.error(f"❌ 停止 {name} 组件失败: {e}")

            # 发送停止通知
            if 'notification' in self.components:
                self.components['notification'].send_system_notification(
                    "🛑 交易机器人系统停止",
                    f"系统于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 安全停止"
                )

            self.logger.info("✅ 所有服务已停止")

        except Exception as e:
            self.logger.error(f"❌ 停止服务时出错: {e}")

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"📡 接收到信号 {signum}，开始优雅关闭...")
        self.stop_services()
        sys.exit(0)

    def run(self):
        """主运行方法"""
        try:
            # 注册信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)

            # 初始化组件
            if not self.initialize_components():
                return False

            # 启动服务
            if not self.start_services():
                return False

            # 主循环
            self.logger.info("🔄 进入主循环，系统正在运行...")
            while self.running:
                try:
                    # 检查系统状态
                    self.check_system_health()

                    # 等待一段时间或直到收到停止信号
                    if self.shutdown_event.wait(timeout=60):
                        break

                except KeyboardInterrupt:
                    self.logger.info("📡 接收到键盘中断信号")
                    break
                except Exception as e:
                    self.logger.error(f"❌ 主循环出错: {e}")

            return True

        except Exception as e:
            self.logger.error(f"❌ 系统运行失败: {e}")
            return False
        finally:
            self.stop_services()

    def check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查MT5连接
            if not self.components['mt5'].is_connected():
                self.logger.warning("⚠️ MT5连接断开，尝试重连...")
                self.components['mt5'].reconnect()

            # 检查其他组件状态
            # 这里可以添加更多健康检查逻辑

        except Exception as e:
            self.logger.error(f"❌ 系统健康检查失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 TradingView自动交易系统")
    print("📅 启动时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)

    # 创建并运行交易机器人管理器
    bot_manager = TradingBotManager()

    try:
        success = bot_manager.run()
        if success:
            print("✅ 系统正常退出")
        else:
            print("❌ 系统异常退出")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### 19. 技术栈选择

#### 19.1 后端技术
- **Python 3.8+**: 主要开发语言
- **Flask/FastAPI**: Web框架
- **SQLAlchemy**: ORM框架
- **MetaTrader5**: 交易API
- **APScheduler**: 任务调度
- **WebSocket**: 实时通信

#### 19.2 前端技术
- **HTML5/CSS3/JavaScript**: 基础技术
- **Bootstrap 4/5**: 响应式UI框架
- **Chart.js**: 图表展示
- **WebSocket**: 实时通信
- **PWA**: 渐进式Web应用

#### 19.3 数据存储
- **SQLite**: 轻量级数据库
- **Redis**: 缓存和会话存储（可选）

#### 19.4 部署工具
- **NSSM**: Windows服务管理
- **Nginx**: 反向代理（可选）
- **Supervisor**: 进程管理（可选）

### 20. 系统特性总结

#### 20.1 核心功能特性
- ✅ **TradingView集成**: 接收Pine脚本警报并自动执行交易
- ✅ **13个主要交易对**: XAUUSD, ETHUSD, BTCUSD, SOLUSD, GBPUSD, GBPJPY, BCHUSD, ADAUSD, XLMUSD, DOGEUSD, LINKUSD, LTCUSD, XRPUSD
- ✅ **美元金额止盈止损**: 支持以美元为单位设置止盈止损
- ✅ **组合风险管理**: 监控所有持仓的总盈亏
- ✅ **智能信号确认**: 基于信号延续的持仓管理
- ✅ **双重Bark通知**: 同时推送到两个设备

#### 20.2 Web管理功能
- ✅ **安全认证系统**: 用户名密码登录，会话管理，自动登出
- ✅ **模块化页面设计**: 6个独立功能页面
- ✅ **响应式布局**: 完美支持PC和移动端访问
- ✅ **实时数据更新**: WebSocket实时推送数据
- ✅ **完整订单管理**: 平仓、修改、批量操作
- ✅ **灵活配置管理**: 所有参数可在线配置
- ✅ **多管理员支持**: 支持创建多个管理员账户
- ✅ **登录安全保护**: 失败次数限制，账户锁定机制
- ✅ **实时配置生效**: 修改后无需重启程序

#### 20.3 配置管理特性
- ✅ **交易对独立配置**: 每个交易对单独设置参数
- ✅ **批量配置操作**: 按分类批量设置参数
- ✅ **配置模板**: 保守、平衡、激进三种模式
- ✅ **全局止盈止损**: 组合级别的风险控制
- ✅ **通知计划管理**: 灵活的通知时间设置
- ✅ **风险控制参数**: 多层次的安全保护

#### 20.4 移动端优化
- ✅ **响应式设计**: 自适应不同屏幕尺寸
- ✅ **触摸优化**: 适合触摸操作的按钮尺寸
- ✅ **移动端导航**: 下拉选择替代标签导航
- ✅ **表格优化**: 隐藏非关键列节省空间
- ✅ **PWA支持**: 可安装为手机应用

#### 20.5 实时通信特性
- ✅ **WebSocket连接**: 双向实时通信
- ✅ **自动重连机制**: 保证连接稳定性
- ✅ **配置实时推送**: 配置变更立即生效
- ✅ **状态实时同步**: 系统状态实时更新
- ✅ **多客户端支持**: 支持多个浏览器同时访问

#### 20.6 统一管理特性
- ✅ **main.py统一启动**: 一个命令启动所有服务
- ✅ **组件化架构**: 模块化的系统设计
- ✅ **健康监控**: 自动检查系统状态
- ✅ **优雅关闭**: 安全停止所有服务
- ✅ **日志管理**: 完整的日志记录系统

#### 20.7 安全控制特性
- ✅ **多重确认**: 危险操作需要确认
- ✅ **权限控制**: 基于角色的访问控制
- ✅ **操作日志**: 完整的操作审计
- ✅ **紧急停止**: 一键停止所有交易
- ✅ **风险限制**: 多层次的风险控制

#### 20.8 通知系统特性
- ✅ **多种通知类型**: 开仓、平仓、余额、警报
- ✅ **定时余额报告**: 参考专业交易系统的定时通知
- ✅ **通知模板**: 丰富的通知内容模板
- ✅ **通知历史**: 完整的通知记录
- ✅ **测试功能**: 通知配置测试

### 21. 部署和使用

#### 21.1 系统要求
- **操作系统**: Windows 11
- **Python版本**: 3.8+
- **MetaTrader5**: 已安装并配置
- **网络要求**: 稳定的互联网连接

#### 21.2 启动命令
```bash
# 安装依赖
pip install -r requirements.txt

# 启动系统
python main.py
```

#### 21.3 访问地址
- **Web管理界面**: http://localhost:7001
- **Webhook接收**: http://localhost:7000/webhook/tradingview
- **API文档**: http://localhost:7001/api/docs

#### 21.4 默认登录信息
- **用户名**: admin
- **密码**: admin123456
- **⚠️ 重要**: 首次登录后请立即修改默认密码！

#### 21.5 移动端访问
- 在手机浏览器中访问: http://[服务器IP]:7001
- 支持添加到主屏幕作为PWA应用
- 自动适配移动端界面
- 登录状态在移动端和PC端同步

### 22. 交易对映射和专业下单系统集成总结

#### 22.1 交易对映射机制
- ✅ **智能识别**: 自动识别USDT/USD后缀变体
- ✅ **标准化处理**: 统一映射为USD后缀标准格式
- ✅ **多格式支持**: 支持"-"、"/"、"_"等分隔符
- ✅ **映射日志**: 记录所有映射转换过程
- ✅ **配置统一**: 所有配置基于标准化符号

#### 22.2 专业下单系统特性
- ✅ **成熟稳定**: 采用专业交易系统的下单方式和参数结构
- ✅ **智能填充**: 根据交易对类型选择最佳填充模式
- ✅ **动态滑点**: 不同资产类别使用不同滑点设置
- ✅ **魔术数字**: 智能魔术数字生成机制
- ✅ **错误处理**: 详细的MT5错误代码处理
- ✅ **订单追踪**: 完整的订单生命周期管理

#### 22.3 开发实现要点

**交易对映射实现**:
```python
# 核心映射函数（参考上面的详细实现）
# normalize_symbol(symbol) - 支持大小写和多种格式
# 警报处理中的使用
standard_symbol = normalize_symbol(alert_data["symbol"])
```

**专业下单实现**:
```python
# 核心下单函数（参考上面的详细实现）
# place_professional_order(symbol, direction, lot_size, sl_usd, tp_usd)
# 1. 获取价格和构建参数
# 2. 选择填充模式和设置滑点
# 3. 发送订单并处理结果
# 4. 记录订单信息和发送通知
```

**配置管理实现**:
```python
# 实时配置更新
def update_config(config_type, config_data):
    # 1. 验证配置
    # 2. 保存到数据库
    # 3. 更新缓存
    # 4. 通知相关组件
    # 5. WebSocket推送
```

#### 22.4 系统优势

1. **无缝兼容**: 支持各种TradingView交易对格式
2. **专业标准**: 采用成熟的专业交易系统下单机制
3. **实时生效**: 所有配置修改立即生效
4. **移动友好**: 完美支持手机端操作
5. **统一管理**: main.py一键启动所有服务

#### 22.5 部署和使用指南

**系统启动**:
```bash
# 一键启动所有服务
python main.py
```

**访问地址**:
- Web管理: http://localhost:7001
- 移动端: http://[IP]:7001 (自动适配)
- Webhook: http://localhost:7000/webhook/tradingview

**端口配置说明**:
- **7000端口**: Webhook服务器，接收TradingView警报
- **7001端口**: Web管理界面，提供配置和监控功能
- **WebSocket**: 使用7001端口的ws://localhost:7001/ws进行实时通信

**TradingView配置**:
```javascript
// Pine脚本警报消息格式
{
  "alert_type": "综合信号",
  "symbol": "{{ticker}}",  // 支持任何格式: SOLUSDT, SOL-USD等
  "timeframe": "{{interval}}",
  "signal_direction": "做多", // 或"做空"
  "price": {{close}},
  "timestamp": "{{time}}",
  "alert_id": "{{strategy.order.id}}"
}
```

### 23. TradingView信号与交易策略完整关联

#### 23.1 信号格式支持
系统完全支持您要求的信号格式，自动处理以下映射（支持大小写）：

**信号方向映射**:
- `BUY` / `buy` / `做多` / `综合做多信号` → 标准化为 `BUY`
- `SELL` / `sell` / `做空` / `综合做空信号` → 标准化为 `SELL`
- `LONG` / `long` / `多` → 标准化为 `BUY`
- `SHORT` / `short` / `空` → 标准化为 `SELL`

**交易对映射**:
- `SOLUSDT` / `SOL-USD` / `SOL/USD` → 标准化为 `SOLUSD`
- `ETHUSD` / `ETHUSDT` / `ETH-USD` → 标准化为 `ETHUSD`

#### 23.2 完整策略执行逻辑

**第一次收到信号 (无持仓)**:
```
TradingView发送: {"signal_direction": "buy", "symbol": "SOLUSDT"}
↓
系统处理: 标准化为 BUY + SOLUSD
↓
策略执行: 开仓做多SOLUSD
↓
结果: 下单成功，启动持仓监控
```

**后续收到同方向信号 (有持仓)**:
```
TradingView发送: {"signal_direction": "BUY", "symbol": "SOLUSDT"}
↓
系统处理: 检测到已有SOLUSD做多持仓
↓
策略执行: 重置超时计时器，继续持有
↓
结果: 持仓继续，延长监控时间
```

**收到反向信号 (有持仓)**:
```
TradingView发送: {"signal_direction": "sell", "symbol": "SOLUSDT"}
↓
系统处理: 检测到已有SOLUSD做多持仓，新信号为做空
↓
策略执行: 平仓做多持仓，开新的做空持仓
↓
结果: 完成持仓反向操作
```

**超时自动平仓**:
```
持仓监控: 3分钟内未收到新的同方向信号
↓
系统处理: 触发超时平仓逻辑
↓
策略执行: 自动平仓，记录平仓原因
↓
结果: 持仓关闭，发送通知
```

#### 23.3 TradingView Pine脚本警报配置示例

```javascript
// Pine脚本警报消息配置 - 大写格式
alertMessage = '{"alert_type": "综合信号", "symbol": "' + syminfo.ticker + '", "timeframe": "' + timeframe.period + '", "signal_direction": "' + (longCondition ? 'BUY' : 'SELL') + '", "price": ' + str.tostring(close) + ', "timestamp": "' + str.tostring(time) + '", "alert_id": "' + str.tostring(time) + '_' + syminfo.ticker + '"}'

// Pine脚本警报消息配置 - 小写格式（同样支持）
alertMessage = '{"alert_type": "综合信号", "symbol": "' + syminfo.ticker + '", "timeframe": "' + timeframe.period + '", "signal_direction": "' + (longCondition ? 'buy' : 'sell') + '", "price": ' + str.tostring(close) + ', "timestamp": "' + str.tostring(time) + '", "alert_id": "' + str.tostring(time) + '_' + syminfo.ticker + '"}'

// 或者使用中文格式
alertMessage = '{"alert_type": "综合信号", "symbol": "' + syminfo.ticker + '", "timeframe": "' + timeframe.period + '", "signal_direction": "' + (longCondition ? '综合做多信号' : '综合做空信号') + '", "price": ' + str.tostring(close) + ', "timestamp": "' + str.tostring(time) + '", "alert_id": "' + str.tostring(time) + '_' + syminfo.ticker + '"}'

// 或者使用LONG/SHORT格式
alertMessage = '{"alert_type": "综合信号", "symbol": "' + syminfo.ticker + '", "timeframe": "' + timeframe.period + '", "signal_direction": "' + (longCondition ? 'long' : 'short') + '", "price": ' + str.tostring(close) + ', "timestamp": "' + str.tostring(time) + '", "alert_id": "' + str.tostring(time) + '_' + syminfo.ticker + '"}'
```

#### 23.3.1 信号格式兼容性测试

系统支持以下所有格式的信号，都会正确执行交易：

```python
# 测试用例 - 所有格式都会正确映射
test_signals = [
    # 大写英文
    {"signal_direction": "BUY"},     # → BUY
    {"signal_direction": "SELL"},    # → SELL
    {"signal_direction": "LONG"},    # → BUY
    {"signal_direction": "SHORT"},   # → SELL

    # 小写英文
    {"signal_direction": "buy"},     # → BUY
    {"signal_direction": "sell"},    # → SELL
    {"signal_direction": "long"},    # → BUY
    {"signal_direction": "short"},   # → SELL

    # 中文格式
    {"signal_direction": "做多"},     # → BUY
    {"signal_direction": "做空"},     # → SELL
    {"signal_direction": "买入"},     # → BUY
    {"signal_direction": "卖出"},     # → SELL
    {"signal_direction": "多"},       # → BUY
    {"signal_direction": "空"},       # → SELL

    # 综合信号格式
    {"signal_direction": "综合做多信号"},  # → BUY
    {"signal_direction": "综合做空信号"},  # → SELL
]

# 处理函数测试
for test_signal in test_signals:
    original = test_signal["signal_direction"]
    normalized = normalize_signal_direction(original)
    print(f"{original} → {normalized}")
```

#### 23.4 系统响应示例

**成功处理响应**:
```json
{
  "success": true,
  "action": "OPEN_POSITION",
  "order_ticket": 12345678,
  "symbol": "SOLUSD",
  "direction": "BUY",
  "lot_size": 0.1,
  "message": "订单已成功执行"
}
```

**持有持仓响应**:
```json
{
  "success": true,
  "action": "HOLD_POSITION",
  "order_ticket": 12345678,
  "symbol": "SOLUSD",
  "reason": "收到同方向确认信号，继续持有"
}
```

#### 23.5 关键特性总结

- ✅ **完整信号支持**: 支持BUY/buy/SELL/sell和中文做多/做空格式
- ✅ **大小写兼容**: 自动处理大小写，buy和BUY都能正确执行
- ✅ **多格式支持**: 支持BUY/SELL、LONG/SHORT、做多/做空等多种格式
- ✅ **智能策略执行**: 自动判断开仓/持有/平仓/反向操作
- ✅ **持仓监控**: 自动监控持仓超时并执行平仓
- ✅ **信号确认机制**: 后续同方向信号作为持仓确认
- ✅ **反向信号处理**: 自动处理信号方向变化
- ✅ **完整日志记录**: 记录所有信号处理和策略执行过程

### 24. 交易执行逻辑总结

**核心交易执行逻辑**：系统接收TradingView发送的JSON警报后，首先进行交易对和信号方向的标准化映射（如SOLUSDT→SOLUSD，buy→BUY），然后根据当前持仓状态执行不同策略：**无持仓时直接下单开仓并启动180秒超时监控**；**有同方向持仓时重置超时计时器继续持有**；**有反方向持仓时先平仓再开新的反方向仓位**；**超时未收到确认信号时自动平仓**。同时系统运行双重风险控制：单个订单的美元止损止盈和组合级别的总盈亏监控（默认-500USD止损/+1000USD止盈），所有配置可通过安全的Web管理界面实时修改无需重启（默认登录：admin/admin123456），支持PC和移动端访问，具备完整的用户认证和会话管理机制。

这个完整的TradingView自动交易系统提供了专业级的功能，完美实现了您要求的信号处理和策略执行逻辑，支持PC和移动端访问，具备智能交易对映射、专业下单机制、实时配置管理和统一启动管理，完全满足您的所有需求。

### 15. 订单管理安全控制

#### 15.1 操作权限控制
```python
# 订单管理权限装饰器
def require_trading_permission(operation_type):
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 检查用户权限
            if not current_user.has_permission(operation_type):
                return {'error': '权限不足'}, 403

            # 检查系统状态
            if is_emergency_stop_enabled():
                return {'error': '系统紧急停止中，禁止操作'}, 423

            # 记录操作日志
            log_trading_operation(current_user.id, operation_type, request.json)

            return func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@require_trading_permission('close_all_positions')
def close_all_positions():
    # 平仓所有订单的实现
    pass
```

#### 15.2 操作确认机制
```javascript
// 多重确认机制
class SafetyConfirmation {
  static async confirmDangerousOperation(operation, details) {
    // 第一层确认
    const firstConfirm = confirm(`确定要执行 ${operation} 吗？\n${details}`);
    if (!firstConfirm) return false;

    // 第二层确认（输入验证码）
    const verificationCode = Math.floor(Math.random() * 9000) + 1000;
    const userInput = prompt(`请输入验证码确认操作: ${verificationCode}`);
    if (userInput !== verificationCode.toString()) {
      alert('验证码错误，操作取消');
      return false;
    }

    // 第三层确认（等待时间）
    const countdown = await this.showCountdownConfirm(operation, 5);
    return countdown;
  }

  static showCountdownConfirm(operation, seconds) {
    return new Promise((resolve) => {
      let remaining = seconds;
      const modal = this.createCountdownModal(operation, remaining);

      const timer = setInterval(() => {
        remaining--;
        modal.updateCountdown(remaining);

        if (remaining <= 0) {
          clearInterval(timer);
          modal.enableConfirmButton();
        }
      }, 1000);

      modal.onConfirm = () => {
        clearInterval(timer);
        modal.close();
        resolve(true);
      };

      modal.onCancel = () => {
        clearInterval(timer);
        modal.close();
        resolve(false);
      };
    });
  }
}
```

#### 15.3 操作日志记录
```sql
-- 操作日志表
CREATE TABLE operation_logs (
    id INTEGER PRIMARY KEY,
    user_id VARCHAR(50),
    operation_type VARCHAR(50),
    operation_details TEXT,
    affected_positions TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    result VARCHAR(20),
    error_message TEXT
);
```

#### 15.4 风险控制规则
```python
class TradingRiskControl:
    @staticmethod
    def validate_close_operation(operation_type, params):
        """验证平仓操作的风险控制"""

        # 检查操作频率限制
        if TradingRiskControl.check_operation_frequency(operation_type):
            raise Exception('操作过于频繁，请稍后再试')

        # 检查市场时间
        if not TradingRiskControl.is_market_open():
            raise Exception('市场未开放，无法执行操作')

        # 检查最大平仓数量限制
        if operation_type == 'close_all':
            position_count = len(mt5.positions_get())
            if position_count > 50:
                raise Exception('持仓数量过多，请分批平仓')

        # 检查盈亏阈值
        if operation_type == 'close_profit':
            min_profit = params.get('min_profit_usd', 0)
            if min_profit < 5:
                raise Exception('最小盈利金额不能低于5美元')

        return True

    @staticmethod
    def check_operation_frequency(operation_type):
        """检查操作频率"""
        # 实现操作频率检查逻辑
        pass

    @staticmethod
    def is_market_open():
        """检查市场是否开放"""
        # 实现市场开放时间检查
        pass
```

#### 15.5 紧急停止机制
```python
class EmergencyControl:
    @staticmethod
    def enable_emergency_stop(reason):
        """启用紧急停止"""
        config = get_system_config()
        config['emergency_stop'] = True
        config['emergency_reason'] = reason
        config['emergency_time'] = datetime.now()
        save_system_config(config)

        # 发送紧急停止通知
        send_emergency_notification(reason)

        # 记录日志
        log_emergency_action('STOP_ENABLED', reason)

    @staticmethod
    def disable_emergency_stop():
        """禁用紧急停止"""
        config = get_system_config()
        config['emergency_stop'] = False
        config['emergency_reason'] = None
        save_system_config(config)

        # 发送恢复通知
        send_emergency_notification('系统恢复正常')

        # 记录日志
        log_emergency_action('STOP_DISABLED', '手动恢复')
```

### 16. 订单管理功能特性总结

#### 16.1 核心功能
- ✅ **平仓所有订单**: 一键平仓所有持仓
- ✅ **平仓盈利订单**: 平仓所有盈利超过指定金额的订单
- ✅ **平仓亏损订单**: 平仓所有亏损超过指定金额的订单
- ✅ **单个订单平仓**: 平仓指定的单个订单
- ✅ **批量选择平仓**: 选择多个订单进行批量平仓
- ✅ **按交易对平仓**: 平仓指定交易对的所有订单

#### 16.2 高级功能
- ✅ **订单修改**: 修改订单的止损止盈
- ✅ **实时更新**: WebSocket实时更新价格和盈亏
- ✅ **过滤搜索**: 按交易对、方向、盈亏等条件过滤
- ✅ **批量操作**: 支持批量选择和操作
- ✅ **操作确认**: 多重确认机制防止误操作

#### 16.3 安全特性
- ✅ **权限控制**: 基于角色的操作权限管理
- ✅ **操作日志**: 完整的操作记录和审计
- ✅ **风险控制**: 操作频率限制和风险检查
- ✅ **紧急停止**: 紧急情况下的快速停止机制
- ✅ **多重确认**: 危险操作的多层确认机制

这个完整的Web订单管理系统参考了专业交易系统的设计，提供了全面的订单管理功能，同时具备完善的安全控制机制。

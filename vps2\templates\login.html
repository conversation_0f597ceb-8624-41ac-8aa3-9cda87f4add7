<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>MT5交易系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 0.75rem;
            --border-radius-sm: 0.5rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 1rem;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            text-align: center;
            padding: 2rem 1.5rem;
            border: none;
        }

        .brand-logo {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .brand-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .brand-subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
            margin: 0;
        }

        .form-signin {
            padding: 2rem 1.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            background: white;
        }

        .form-floating > label {
            color: var(--secondary-color);
            font-weight: 500;
        }

        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 600;
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .alert {
            border: none;
            border-radius: var(--border-radius-sm);
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .card-footer {
            background: var(--light-color);
            border: none;
            padding: 1.5rem;
            text-align: center;
            color: var(--secondary-color);
            font-size: 0.875rem;
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            body {
                padding: 0.5rem;
            }

            .card-header {
                padding: 1.5rem 1rem;
            }

            .brand-logo {
                font-size: 2.5rem;
            }

            .brand-title {
                font-size: 1.25rem;
            }

            .form-signin {
                padding: 1.5rem 1rem;
            }

            .card-footer {
                padding: 1rem;
            }
        }

        /* 触控优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn:active {
                transform: scale(0.98);
            }
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .loading::after {
            content: '';
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: rotate 1s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 输入框图标 */
        .input-group-text {
            background: white;
            border: 2px solid var(--border-color);
            border-right: none;
            color: var(--secondary-color);
        }

        .input-group .form-control {
            border-left: none;
        }

        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
        }

        /* 验证码样式 */
        .captcha-container {
            display: inline-block;
        }

        .captcha-container img {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background: white;
            transition: all 0.2s ease;
        }

        .captcha-container img:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .captcha-container .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            line-height: 1;
        }

        #captcha {
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
            text-align: center;
        }

        /* 触控优化 */
        @media (hover: none) and (pointer: coarse) {
            .captcha-container img:active {
                transform: scale(0.95);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card">
            <div class="card-header">
                <div class="brand-logo">📊</div>
                <h1 class="brand-title">MT5交易系统</h1>
                <p class="brand-subtitle">智能交易管理平台</p>
            </div>
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}">
                                {% if category == 'danger' %}
                                    <i class="bi bi-exclamation-triangle-fill"></i>
                                {% elif category == 'success' %}
                                    <i class="bi bi-check-circle-fill"></i>
                                {% else %}
                                    <i class="bi bi-info-circle-fill"></i>
                                {% endif %}
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form class="form-signin" method="post" id="loginForm">
                    <div class="form-floating mb-3">
                        <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" required autofocus>
                        <label for="username">
                            <i class="bi bi-person me-2"></i>用户名
                        </label>
                    </div>

                    <div class="form-floating mb-4">
                        <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" required>
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>

                    {% if require_captcha %}
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="bi bi-shield-check me-2"></i>验证码
                            <small class="text-muted">(密码错误次数过多，需要验证码)</small>
                        </label>
                        <div class="row g-2">
                            <div class="col-7">
                                <input type="text" id="captcha" name="captcha" class="form-control" placeholder="请输入验证码" required maxlength="4" style="text-transform: uppercase;">
                            </div>
                            <div class="col-5">
                                <div class="captcha-container position-relative">
                                    <img id="captchaImage" src="{{ url_for('captcha') }}" alt="验证码" class="img-fluid border rounded" style="height: 48px; cursor: pointer;" onclick="refreshCaptcha()">
                                    <button type="button" class="btn btn-outline-secondary btn-sm position-absolute top-0 end-0 mt-1 me-1" onclick="refreshCaptcha()" title="刷新验证码">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">点击图片或刷新按钮可更换验证码</small>
                    </div>
                    {% endif %}

                    <div class="d-grid">
                        <button class="btn btn-primary btn-lg" type="submit" id="loginBtn">
                            <i class="bi bi-box-arrow-in-right"></i>
                            <span id="loginBtnText">登录系统</span>
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <i class="bi bi-shield-check me-1"></i>
                            安全登录 · 数据加密传输
                        </small>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small>&copy; 2025 MT5交易系统</small>
                    <small>
                        <i class="bi bi-globe me-1"></i>
                        <span id="currentTime"></span>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 更新当前时间
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });
                document.getElementById('currentTime').textContent = timeString;
            }

            updateTime();
            setInterval(updateTime, 1000);

            // 登录表单处理
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');

            loginForm.addEventListener('submit', function(e) {
                // 添加加载状态
                loginBtn.disabled = true;
                loginBtnText.innerHTML = '<span class="loading">登录中</span>';

                // 如果有错误，恢复按钮状态
                setTimeout(() => {
                    if (loginBtn.disabled) {
                        loginBtn.disabled = false;
                        loginBtnText.textContent = '登录系统';
                    }
                }, 5000);
            });

            // 验证码刷新功能
            window.refreshCaptcha = function() {
                const captchaImage = document.getElementById('captchaImage');
                if (captchaImage) {
                    // 添加时间戳防止缓存
                    captchaImage.src = '{{ url_for("captcha") }}?' + new Date().getTime();

                    // 清空验证码输入框
                    const captchaInput = document.getElementById('captcha');
                    if (captchaInput) {
                        captchaInput.value = '';
                        captchaInput.focus();
                    }
                }
            };

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Enter键快速登录
                if (e.key === 'Enter' && (e.target.id === 'username' || e.target.id === 'password' || e.target.id === 'captcha')) {
                    e.preventDefault();
                    loginForm.submit();
                }

                // F5键刷新验证码
                if (e.key === 'F5' && document.getElementById('captchaImage')) {
                    e.preventDefault();
                    refreshCaptcha();
                }
            });

            // 验证码输入框自动转换为大写
            const captchaInput = document.getElementById('captcha');
            if (captchaInput) {
                captchaInput.addEventListener('input', function(e) {
                    e.target.value = e.target.value.toUpperCase();
                });
            }

            // 输入框焦点效果
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // 检测网络状态
            function updateNetworkStatus() {
                const statusElement = document.createElement('div');
                statusElement.className = 'alert alert-' + (navigator.onLine ? 'success' : 'danger');
                statusElement.innerHTML = `
                    <i class="bi bi-${navigator.onLine ? 'wifi' : 'wifi-off'}"></i>
                    ${navigator.onLine ? '网络连接正常' : '网络连接断开'}
                `;

                // 如果网络状态改变，显示提示
                if (!navigator.onLine) {
                    document.querySelector('.card-body').insertBefore(statusElement, document.querySelector('.form-signin'));
                    setTimeout(() => statusElement.remove(), 3000);
                }
            }

            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
        });
    </script>
</body>
</html>

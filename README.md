# TradingView自动交易系统

一个专业的MetaTrader5自动交易系统，支持接收TradingView警报并自动执行交易策略。

## 🚀 主要功能

### 核心交易功能
- **TradingView警报接收**: 通过Webhook接收TradingView的交易信号
- **智能交易对映射**: 自动识别和转换各种格式的交易对符号
- **信号方向标准化**: 支持中英文信号方向自动识别
- **专业下单系统**: 基于美元金额的止盈止损设置
- **持仓监控**: 信号超时自动平仓机制
- **风险控制**: 全局止盈止损和日交易限制

### 管理和监控
- **Web管理界面**: 直观的交易监控和配置管理
- **实时通知**: Bark推送通知支持
- **交易历史**: 完整的交易记录和统计
- **风险监控**: 实时账户风险评估
- **配置管理**: 灵活的交易对和系统配置

### 安全特性
- **用户认证**: 安全的登录和会话管理
- **紧急停止**: 一键停止所有交易
- **数据加密**: 敏感信息加密存储
- **操作日志**: 完整的操作审计记录

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8或更高版本
- **MetaTrader5**: 已安装并可正常登录
- **网络**: 稳定的互联网连接

## 🛠️ 安装步骤

### 1. 下载系统
```bash
git clone <repository-url>
cd trading-system
```

### 2. 运行安装脚本
```bash
deploy\install.bat
```

### 3. 配置系统
编辑 `.env` 文件，填入以下配置：

```env
# MT5账户信息
MT5_LOGIN=your_mt5_account_number
MT5_PASSWORD=your_mt5_password
MT5_SERVER=your_mt5_server

# Bark通知密钥
BARK_DEVICE_1_KEY=your_bark_device_1_key
BARK_DEVICE_2_KEY=your_bark_device_2_key

# 其他配置...
```

### 4. 启动系统
```bash
start.bat
```

## 🎯 快速开始

### 1. 首次登录
- 访问 http://localhost:5000
- 使用默认账户登录：
  - 用户名: `admin`
  - 密码: `admin123`

### 2. 配置交易对
在Web界面中配置您要交易的交易对：
- 启用/禁用交易对
- 设置手数大小
- 配置止盈止损金额
- 设置信号超时时间

### 3. 设置TradingView警报
在TradingView中创建警报，Webhook URL设置为：
```
http://your-server-ip:5000/webhook/tradingview
```

警报消息格式：
```json
{
    "symbol": "XAUUSD",
    "signal_direction": "BUY",
    "timeframe": "1m",
    "price": 2000.50,
    "signal_strength": "强"
}
```

## 📊 支持的交易对

### 贵金属
- XAUUSD (黄金)

### 加密货币
- ETHUSD, BTCUSD, SOLUSD, BCHUSD
- ADAUSD, XLMUSD, DOGEUSD, LINKUSD
- LTCUSD, XRPUSD

### 外汇
- GBPUSD, GBPJPY

## 🔧 配置说明

### 交易对配置
每个交易对可以独立配置：
- `enabled`: 是否启用交易
- `lot_size`: 交易手数
- `stop_loss_usd`: 止损金额(美元)
- `take_profit_usd`: 止盈金额(美元)
- `signal_timeout`: 信号超时时间(秒)

### 系统配置
- `trading_cooldown`: 交易冷却时间
- `max_concurrent_trades`: 最大并发交易数
- `max_daily_loss_usd`: 日最大亏损限制
- `max_daily_profit_usd`: 日最大盈利限制

### 全局止盈止损
- `global_stop_loss_usd`: 全局止损金额
- `global_take_profit_usd`: 全局止盈金额
- `check_interval_seconds`: 检查间隔

## 📱 Bark通知配置

1. 在iPhone上安装Bark应用
2. 获取设备密钥
3. 在`.env`文件中配置密钥
4. 系统将自动发送以下通知：
   - 开仓通知
   - 平仓通知
   - 余额报告
   - 风险警告
   - 系统状态

## 🔒 安全建议

1. **修改默认密码**: 首次登录后立即修改管理员密码
2. **使用强密钥**: 在生产环境中使用强SECRET_KEY
3. **网络安全**: 建议使用VPN或防火墙保护系统
4. **定期备份**: 定期备份数据库和配置文件
5. **监控日志**: 定期检查系统日志

## 📈 交易策略

### 信号处理逻辑
1. **无持仓时**: 收到信号直接开仓
2. **有持仓时**:
   - 同方向信号: 重置超时计时器，继续持有
   - 反方向信号: 平仓并开新仓

### 风险控制
- 交易冷却时间防止频繁交易
- 最大并发交易数限制
- 日盈亏限制
- 全局止盈止损
- 信号超时自动平仓

## 🛡️ 故障排除

### 常见问题

**Q: MT5连接失败**
A: 检查MT5账户信息、网络连接和MT5终端是否正常运行

**Q: 收不到TradingView警报**
A: 检查Webhook URL是否正确，防火墙是否阻止端口

**Q: Bark通知发送失败**
A: 检查设备密钥是否正确，网络是否正常

**Q: 交易执行失败**
A: 检查账户余额、保证金和交易对是否可交易

### 日志查看
系统日志位于 `logs/trading_system.log`，包含详细的运行信息。

## 📚 API文档

系统提供完整的REST API，详细文档请参考 `docs/API.md`。

主要API端点：
- `/api/system/status` - 系统状态
- `/api/positions` - 当前持仓
- `/api/config/symbols` - 交易对配置
- `/webhook/tradingview` - TradingView警报接收

## 🔄 更新和维护

### 系统更新
```bash
git pull origin main
pip install -r requirements.txt
```

### 数据库备份
```bash
copy trading_system.db trading_system_backup.db
```

### 日志清理
定期清理日志文件以节省磁盘空间。

## 📞 技术支持

如遇到问题，请：
1. 查看系统日志
2. 检查配置文件
3. 参考故障排除指南
4. 联系技术支持

## ⚠️ 免责声明

本系统仅供学习和研究使用。使用本系统进行实盘交易存在风险，可能导致资金损失。用户应当：

1. 充分了解交易风险
2. 在模拟环境中充分测试
3. 合理设置风险参数
4. 定期监控系统运行
5. 自行承担交易风险

开发者不对使用本系统造成的任何损失承担责任。

## 📄 许可证

本项目采用MIT许可证，详情请参考LICENSE文件。

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Bark通知功能
"""

import sys
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_bark_notifications():
    """测试所有Bark通知功能"""
    print("=" * 50)
    print("测试Bark通知功能")
    print("=" * 50)
    
    try:
        # 导入模块
        import bark_notifier
        
        # 测试1: 基本通知功能
        print("\n1. 测试基本通知功能...")
        result1 = bark_notifier.send_bark_notification(
            "测试通知", 
            "这是一个测试通知，用于验证Bark功能是否正常工作。"
        )
        print(f"   基本通知结果: {'成功' if result1 else '失败'}")
        
        # 测试2: 交易执行通知
        print("\n2. 测试交易执行通知...")
        test_trade_info = {
            'ticket': 123456,
            'symbol': 'ETHUSD',
            'operation': 'buy',
            'volume': 0.1,
            'price': 3500.0,
            'sl': 3400.0,
            'tp': 3600.0
        }
        result2 = bark_notifier.notify_trade_execution(test_trade_info)
        print(f"   交易执行通知结果: {'成功' if result2 else '失败'}")
        
        # 测试3: 平仓通知
        print("\n3. 测试平仓通知...")
        test_close_info = {
            'ticket': 123456,
            'symbol': 'ETHUSD',
            'operation': 'buy',
            'volume': 0.1,
            'open_price': 3500.0,
            'close_price': 3550.0,
            'profit': 50.0,
            'points': 50
        }
        result3 = bark_notifier.notify_trade_closed(test_close_info)
        print(f"   平仓通知结果: {'成功' if result3 else '失败'}")
        
        # 测试4: 错误通知
        print("\n4. 测试错误通知...")
        result4 = bark_notifier.notify_error("这是一个测试错误消息")
        print(f"   错误通知结果: {'成功' if result4 else '失败'}")
        
        # 总结
        print("\n" + "=" * 50)
        print("测试总结:")
        all_success = all([result1, result2, result3, result4])
        print(f"所有测试: {'全部成功' if all_success else '部分失败'}")
        
        if all_success:
            print("✅ Bark通知功能正常工作！")
            print("✅ 下单和平仓时都会发送通知到两个Bark设备")
        else:
            print("❌ 部分Bark通知功能异常，请检查配置")
            
        return all_success
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def check_bark_config():
    """检查Bark配置"""
    print("\n检查Bark配置...")
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        bark_url = config.get('bark_url', '')
        bark_device_key = config.get('bark_device_key', '')
        bark_device_key_2 = config.get('bark_device_key_2', '')
        
        print(f"Bark URL: {bark_url}")
        print(f"设备1密钥: {'已配置' if bark_device_key else '未配置'}")
        print(f"设备2密钥: {'已配置' if bark_device_key_2 else '未配置'}")
        
        if not bark_url:
            print("❌ Bark URL未配置")
            return False
        
        if not bark_device_key and not bark_device_key_2:
            print("❌ 没有配置任何Bark设备密钥")
            return False
        
        print("✅ Bark配置检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查配置时发生错误: {e}")
        return False

def main():
    """主函数"""
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查配置
    config_ok = check_bark_config()
    if not config_ok:
        print("\n❌ 配置检查失败，请先检查config.json中的Bark配置")
        return
    
    # 测试通知功能
    test_ok = test_bark_notifications()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if test_ok:
        print("\n🎉 所有测试通过！Bark通知功能已修复，下单和平仓时都会发送通知。")
    else:
        print("\n⚠️ 测试未完全通过，请检查日志了解详细错误信息。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进功能：
1. 系统通知关闭
2. 余额不足原因显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from notifications.bark import bark_manager
from trading.config_manager import config_manager
import json

def test_system_notification_disabled():
    """测试系统通知是否已关闭"""
    print("=== 测试系统通知状态 ===")
    
    # 检查系统状态通知是否已禁用
    is_enabled = bark_manager._is_notification_type_enabled("系统状态通知")
    print(f"系统状态通知启用状态: {is_enabled}")
    
    if not is_enabled:
        print("✅ 系统状态通知已成功关闭")
    else:
        print("❌ 系统状态通知仍然启用")
    
    # 检查配置文件内容
    try:
        with open('config/bark_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"配置文件中system_status设置: {config.get('system_status', 'undefined')}")
    except Exception as e:
        print(f"读取配置文件失败: {e}")

def test_balance_insufficient_reason():
    """测试余额不足原因"""
    print("\n=== 测试余额不足原因 ===")
    
    # 模拟一个警报数据
    test_alert = {
        "standard_symbol": "BTCUSD",
        "standard_signal": "BUY",
        "alert_id": "test_balance_check"
    }
    
    # 模拟交易对配置
    test_symbol_config = {
        "enabled": True,
        "lot_size": 0.01,
        "stop_loss_usd": 50.0,
        "take_profit_usd": 100.0,
        "signal_timeout": 180,
        "category": "crypto"
    }
    
    print("模拟测试数据:")
    print(f"交易对: {test_alert['standard_symbol']}")
    print(f"信号: {test_alert['standard_signal']}")
    print(f"手数: {test_symbol_config['lot_size']}")
    
    # 测试风险检查函数
    try:
        from trading.engine import trading_strategy_engine
        risk_result = trading_strategy_engine._perform_risk_checks(
            test_alert["standard_symbol"], 
            test_symbol_config
        )
        
        print(f"\n风险检查结果: {risk_result}")
        
        if not risk_result["success"]:
            reason = risk_result["reason"]
            print(f"失败原因: {reason}")
            
            if reason == "余额不足":
                print("✅ 余额不足原因显示正确")
            else:
                print(f"⚠️  失败原因: {reason}")
        else:
            print("✅ 风险检查通过")
            
    except Exception as e:
        print(f"测试风险检查时出错: {e}")

def test_notification_types():
    """测试各种通知类型的状态"""
    print("\n=== 测试所有通知类型状态 ===")
    
    notification_types = [
        "开仓通知",
        "平仓通知", 
        "TradingView警报转发",
        "系统状态通知",
        "定时余额报告"
    ]
    
    for notification_type in notification_types:
        is_enabled = bark_manager._is_notification_type_enabled(notification_type)
        status = "✅ 启用" if is_enabled else "❌ 禁用"
        print(f"{notification_type}: {status}")

if __name__ == "__main__":
    print("开始测试改进功能...\n")
    
    test_system_notification_disabled()
    test_balance_insufficient_reason()
    test_notification_types()
    
    print("\n测试完成！")

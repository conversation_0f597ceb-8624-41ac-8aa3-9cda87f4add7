/**
 * 页面管理模块
 */

class PageManager {
    constructor() {
        this.currentPage = 'dashboard';
        this.countdownTimer = null;
        this.pages = {
            'dashboard': this.renderDashboard,
            'trades': this.renderTrades,
            'positions': this.renderPositions,
            'config': this.renderConfig,
            'alerts': this.renderAlerts,
            'logs': this.renderLogs,
            'settings': this.renderSettings
        };
    }

    /**
     * 显示指定页面
     */
    showPage(pageId) {
        // 清理定时器
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }

        // 隐藏所有页面
        document.querySelectorAll('.page-content').forEach(page => {
            page.style.display = 'none';
        });

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const navLink = document.getElementById(`nav-${pageId}`);
        if (navLink) {
            navLink.classList.add('active');
        }

        // 显示目标页面
        const targetPage = document.getElementById(`${pageId}Page`);
        if (targetPage) {
            targetPage.style.display = 'block';
            this.currentPage = pageId;

            // 渲染页面内容
            if (this.pages[pageId]) {
                this.pages[pageId].call(this);
            }
        }
    }

    /**
     * 渲染仪表板页面
     */
    renderDashboard() {
        // 仪表板内容已在HTML中定义，这里只需要刷新数据
        if (ui) {
            ui.loadInitialData();
        }
    }

    /**
     * 渲染交易记录页面
     */
    renderTrades() {
        const page = document.getElementById('tradesPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul"></i> 交易记录
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="pageManager.exportTrades()">
                            <i class="bi bi-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-secondary" onclick="pageManager.refreshTrades()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="tradesSymbolFilter">
                                <option value="">所有交易对</option>
                                <option value="XAUUSD">XAUUSD</option>
                                <option value="BTCUSD">BTCUSD</option>
                                <option value="ETHUSD">ETHUSD</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="tradesStatusFilter">
                                <option value="">所有状态</option>
                                <option value="open">持仓中</option>
                                <option value="closed">已平仓</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="tradesDateFilter">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary btn-sm w-100" onclick="pageManager.filterTrades()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                        </div>
                    </div>

                    <!-- 交易记录表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>交易对</th>
                                    <th>方向</th>
                                    <th>手数</th>
                                    <th>开仓价</th>
                                    <th>平仓价</th>
                                    <th>盈亏</th>
                                    <th>开仓时间</th>
                                    <th>平仓时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="tradesTableBody">
                                <tr>
                                    <td colspan="10" class="text-center text-muted">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav>
                        <ul class="pagination pagination-sm justify-content-center" id="tradesPagination">
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        this.loadTrades();
    }

    /**
     * 渲染持仓管理页面
     */
    renderPositions() {
        const page = document.getElementById('positionsPage');
        page.innerHTML = `
            <!-- 监控控制面板 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-shield-check"></i> 超时平仓监控
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <span class="me-3">监控状态:</span>
                                <span id="monitoringStatus" class="badge bg-secondary">检查中...</span>
                                <button id="toggleMonitoringBtn" class="btn btn-sm btn-outline-primary ms-3" onclick="pageManager.toggleMonitoring()">
                                    <i class="bi bi-play-circle"></i> 启动监控
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                监控中的订单: <span id="monitoredCount">0</span> 个
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 持仓列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart"></i> 当前持仓
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-danger" onclick="pageManager.closeAllPositions()">
                            <i class="bi bi-x-circle"></i> 全部平仓
                        </button>
                        <button class="btn btn-outline-secondary" onclick="pageManager.refreshPositions()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>交易对</th>
                                    <th>方向</th>
                                    <th>手数</th>
                                    <th>开仓价</th>
                                    <th>当前价</th>
                                    <th>浮动盈亏</th>
                                    <th>止损</th>
                                    <th>止盈</th>
                                    <th>持仓时间</th>
                                    <th>平仓倒计时</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="positionsDetailTableBody">
                                <tr>
                                    <td colspan="12" class="text-center text-muted">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        this.loadPositions();
        this.loadMonitoringStatus();

        // 启动定时刷新倒计时
        this.startCountdownTimer();
    }

    /**
     * 渲染配置管理页面
     */
    renderConfig() {
        const page = document.getElementById('configPage');
        page.innerHTML = `
            <!-- 页面标题 -->
            <div class="row mb-3">
                <div class="col-12">
                    <h3>⚙️ 配置管理</h3>
                    <p class="text-muted">所有配置修改后实时生效，无需重启程序</p>
                </div>
            </div>

            <!-- 配置导航 -->
            <div class="row mb-4">
                <div class="col-12">
                    <ul class="nav nav-pills nav-fill" id="configTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="symbols-tab" data-bs-toggle="pill" href="#symbols-config" role="tab">
                                📊 交易对配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="global-tab" data-bs-toggle="pill" href="#global-config" role="tab">
                                🌐 全局配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="bark-tab" data-bs-toggle="pill" href="#bark-config" role="tab">
                                🔔 通知配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="risk-tab" data-bs-toggle="pill" href="#risk-config" role="tab">
                                🛡️ 风险控制
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 配置内容 -->
            <div class="tab-content" id="configTabContent">
                <!-- 交易对配置 -->
                <div class="tab-pane fade show active" id="symbols-config" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8 col-md-12 mb-3">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">📊 交易对配置</h5>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-success" onclick="enableAllSymbols()">全部启用</button>
                                        <button class="btn btn-warning" onclick="disableAllSymbols()">全部禁用</button>
                                        <button class="btn btn-info" onclick="resetToDefaults()">恢复默认</button>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0" id="symbolsConfigTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="d-none d-md-table-cell" width="80">启用</th>
                                                    <th>交易对</th>
                                                    <th class="d-none d-lg-table-cell">分类</th>
                                                    <th width="80">手数</th>
                                                    <th width="100">止损</th>
                                                    <th width="100">止盈</th>
                                                    <th class="d-none d-md-table-cell" width="80">超时</th>
                                                    <th width="60">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="symbolsConfigBody">
                                                <!-- 动态生成 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-12">
                            <div class="card">
                                <div class="card-header">⚡ 快速配置</div>
                                <div class="card-body">
                                    <p class="text-muted">批量配置工具开发中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他配置标签页内容 -->
                <div class="tab-pane fade" id="global-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🌐 全局配置</div>
                        <div class="card-body">
                            <p class="text-muted">全局配置功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="bark-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🔔 通知配置</div>
                        <div class="card-body">
                            <p class="text-muted">通知配置功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="risk-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🛡️ 风险控制</div>
                        <div class="card-body">
                            <p class="text-muted">风险控制配置功能开发中...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 加载配置数据
        if (configManager) {
            configManager.loadAllConfigs();
        }
    }

    /**
     * 渲染警报历史页面
     */
    renderAlerts() {
        const page = document.getElementById('alertsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bell"></i> 警报历史
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">警报历史功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染系统日志页面
     */
    renderLogs() {
        const page = document.getElementById('logsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text"></i> 系统日志
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">系统日志功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染系统设置页面
     */
    renderSettings() {
        const page = document.getElementById('settingsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-sliders"></i> 系统设置
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">系统设置功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 加载交易记录
     */
    async loadTrades() {
        try {
            const response = await ui.apiCall('/api/trades');
            // 处理交易记录数据
            console.log('交易记录:', response.data);
        } catch (error) {
            console.error('加载交易记录失败:', error);
        }
    }

    /**
     * 加载持仓数据
     */
    async loadPositions() {
        try {
            const response = await ui.apiCall('/api/positions');
            this.renderPositionsTable(response.data);
        } catch (error) {
            console.error('加载持仓数据失败:', error);
            document.getElementById('positionsDetailTableBody').innerHTML = `
                <tr>
                    <td colspan="12" class="text-center text-danger">加载失败: ${error.message}</td>
                </tr>
            `;
        }
    }

    /**
     * 渲染持仓表格
     */
    renderPositionsTable(positions) {
        const tbody = document.getElementById('positionsDetailTableBody');

        if (!positions || positions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="12" class="text-center text-muted">暂无持仓</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = positions.map(position => {
            const profitClass = position.profit >= 0 ? 'text-success' : 'text-danger';
            const profitIcon = position.profit >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';

            // 格式化持仓时间
            const openTime = new Date(position.time);
            const holdingTime = this.formatHoldingTime(Date.now() - openTime.getTime());

            // 监控状态和倒计时
            const monitoring = position.monitoring || { enabled: false, remaining_seconds: 0 };
            const countdownDisplay = this.formatCountdown(monitoring);

            return `
                <tr>
                    <td><strong>#${position.ticket}</strong></td>
                    <td><span class="badge bg-primary">${position.symbol}</span></td>
                    <td>
                        <span class="badge ${position.type_str === 'BUY' ? 'bg-success' : 'bg-danger'}">
                            ${position.type_str === 'BUY' ? '买入' : '卖出'}
                        </span>
                    </td>
                    <td>${position.volume}</td>
                    <td>${position.price_open.toFixed(5)}</td>
                    <td>${position.price_current.toFixed(5)}</td>
                    <td class="${profitClass}">
                        <i class="bi ${profitIcon}"></i>
                        $${position.profit.toFixed(2)}
                    </td>
                    <td>${position.sl || '-'}</td>
                    <td>${position.tp || '-'}</td>
                    <td>${holdingTime}</td>
                    <td>${countdownDisplay}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${this.renderMonitoringButton(position)}
                            <button class="btn btn-outline-danger" onclick="pageManager.closePosition(${position.ticket})" title="平仓">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * 格式化倒计时显示
     */
    formatCountdown(monitoring) {
        if (!monitoring.enabled) {
            return '<span class="text-muted">未监控</span>';
        }

        const seconds = monitoring.remaining_seconds;
        if (seconds <= 0) {
            return '<span class="text-danger"><i class="bi bi-exclamation-triangle"></i> 已超时</span>';
        }

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeStr = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;

        let badgeClass = 'bg-success';
        if (seconds <= 30) {
            badgeClass = 'bg-danger';
        } else if (seconds <= 60) {
            badgeClass = 'bg-warning';
        }

        return `<span class="badge ${badgeClass}" data-ticket="${monitoring.ticket}" data-remaining="${seconds}">
                    <i class="bi bi-clock"></i> ${timeStr}
                </span>`;
    }

    /**
     * 渲染监控按钮
     */
    renderMonitoringButton(position) {
        const monitoring = position.monitoring || { enabled: false };

        if (monitoring.enabled) {
            return `
                <button class="btn btn-outline-warning" onclick="pageManager.removeMonitoring(${position.ticket})" title="停止监控">
                    <i class="bi bi-shield-slash"></i>
                </button>
            `;
        } else {
            return `
                <button class="btn btn-outline-success" onclick="pageManager.addMonitoring(${position.ticket})" title="添加监控">
                    <i class="bi bi-shield-plus"></i>
                </button>
            `;
        }
    }

    /**
     * 格式化持仓时间
     */
    formatHoldingTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}天${hours % 24}时`;
        } else if (hours > 0) {
            return `${hours}时${minutes % 60}分`;
        } else {
            return `${minutes}分${seconds % 60}秒`;
        }
    }

    /**
     * 加载监控状态
     */
    async loadMonitoringStatus() {
        try {
            const response = await ui.apiCall('/api/monitoring/status');
            this.updateMonitoringStatus(response.data);
        } catch (error) {
            console.error('加载监控状态失败:', error);
        }
    }

    /**
     * 更新监控状态显示
     */
    updateMonitoringStatus(status) {
        const statusElement = document.getElementById('monitoringStatus');
        const toggleBtn = document.getElementById('toggleMonitoringBtn');
        const countElement = document.getElementById('monitoredCount');

        if (status.is_running) {
            statusElement.className = 'badge bg-success';
            statusElement.innerHTML = '<i class="bi bi-shield-check"></i> 运行中';
            toggleBtn.innerHTML = '<i class="bi bi-pause-circle"></i> 暂停监控';
            toggleBtn.className = 'btn btn-sm btn-outline-warning ms-3';
        } else {
            statusElement.className = 'badge bg-danger';
            statusElement.innerHTML = '<i class="bi bi-shield-x"></i> 已停止';
            toggleBtn.innerHTML = '<i class="bi bi-play-circle"></i> 启动监控';
            toggleBtn.className = 'btn btn-sm btn-outline-primary ms-3';
        }

        countElement.textContent = status.monitored_positions || 0;
    }

    /**
     * 切换监控状态
     */
    async toggleMonitoring() {
        try {
            const response = await ui.apiCall('/api/monitoring/toggle', 'POST');
            this.updateMonitoringStatus({
                is_running: response.data.is_running,
                monitored_positions: 0
            });

            ui.showAlert(
                response.data.action === 'started' ? '监控已启动' : '监控已暂停',
                'success'
            );

            // 刷新持仓数据
            this.loadPositions();
        } catch (error) {
            console.error('切换监控状态失败:', error);
            ui.showAlert('操作失败: ' + error.message, 'danger');
        }
    }

    /**
     * 为订单添加监控
     */
    async addMonitoring(ticket) {
        try {
            // 弹出对话框询问超时时间
            const timeoutSeconds = prompt('请输入超时时间（秒）:', '180');
            if (!timeoutSeconds || isNaN(timeoutSeconds)) {
                return;
            }

            const response = await ui.apiCall(`/api/positions/${ticket}/monitoring`, 'POST', {
                timeout_seconds: parseInt(timeoutSeconds)
            });

            ui.showAlert(response.message, 'success');
            this.loadPositions();
            this.loadMonitoringStatus();
        } catch (error) {
            console.error('添加监控失败:', error);
            ui.showAlert('添加监控失败: ' + error.message, 'danger');
        }
    }

    /**
     * 移除订单监控
     */
    async removeMonitoring(ticket) {
        try {
            if (!confirm(`确定要停止订单#${ticket}的监控吗？`)) {
                return;
            }

            const response = await ui.apiCall(`/api/positions/${ticket}/monitoring`, 'DELETE');

            ui.showAlert(response.message, 'success');
            this.loadPositions();
            this.loadMonitoringStatus();
        } catch (error) {
            console.error('移除监控失败:', error);
            ui.showAlert('移除监控失败: ' + error.message, 'danger');
        }
    }

    /**
     * 平仓订单
     */
    async closePosition(ticket) {
        try {
            if (!confirm(`确定要平仓订单#${ticket}吗？`)) {
                return;
            }

            const response = await ui.apiCall(`/api/positions/${ticket}/close`, 'POST');

            ui.showAlert('平仓成功', 'success');
            this.loadPositions();
            this.loadMonitoringStatus();
        } catch (error) {
            console.error('平仓失败:', error);
            ui.showAlert('平仓失败: ' + error.message, 'danger');
        }
    }

    /**
     * 刷新持仓数据
     */
    refreshPositions() {
        this.loadPositions();
        this.loadMonitoringStatus();
    }

    /**
     * 全部平仓
     */
    async closeAllPositions() {
        try {
            if (!confirm('确定要平仓所有订单吗？此操作不可撤销！')) {
                return;
            }

            const response = await ui.apiCall('/api/positions');
            const positions = response.data;

            if (!positions || positions.length === 0) {
                ui.showAlert('当前无持仓', 'info');
                return;
            }

            let successCount = 0;
            let failCount = 0;

            for (const position of positions) {
                try {
                    await ui.apiCall(`/api/positions/${position.ticket}/close`, 'POST');
                    successCount++;
                } catch (error) {
                    console.error(`平仓订单#${position.ticket}失败:`, error);
                    failCount++;
                }
            }

            ui.showAlert(
                `平仓完成：成功${successCount}个，失败${failCount}个`,
                failCount === 0 ? 'success' : 'warning'
            );

            this.loadPositions();
            this.loadMonitoringStatus();
        } catch (error) {
            console.error('全部平仓失败:', error);
            ui.showAlert('全部平仓失败: ' + error.message, 'danger');
        }
    }

    /**
     * 启动倒计时定时器
     */
    startCountdownTimer() {
        // 清除现有定时器
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        // 每秒更新倒计时
        this.countdownTimer = setInterval(() => {
            this.updateCountdowns();
        }, 1000);
    }

    /**
     * 更新倒计时显示
     */
    updateCountdowns() {
        const countdownElements = document.querySelectorAll('[data-remaining]');

        countdownElements.forEach(element => {
            const remaining = parseInt(element.dataset.remaining) - 1;
            element.dataset.remaining = remaining;

            if (remaining <= 0) {
                element.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 已超时';
                element.className = 'badge bg-danger';
            } else {
                const minutes = Math.floor(remaining / 60);
                const seconds = remaining % 60;
                const timeStr = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                let badgeClass = 'bg-success';
                if (remaining <= 30) {
                    badgeClass = 'bg-danger';
                } else if (remaining <= 60) {
                    badgeClass = 'bg-warning';
                }

                element.innerHTML = `<i class="bi bi-clock"></i> ${timeStr}`;
                element.className = `badge ${badgeClass}`;
            }
        });
    }
}

// 全局页面管理器实例
let pageManager;

// 初始化页面管理器
document.addEventListener('DOMContentLoaded', function() {
    pageManager = new PageManager();
});

// 全局函数供HTML调用
function showPage(pageId) {
    if (pageManager) {
        pageManager.showPage(pageId);
    }
}

#!/usr/bin/env python3
"""
MT5连接测试脚本
"""
import sys
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mt5_import():
    """测试MT5模块导入"""
    print("🔍 测试MT5模块导入...")
    try:
        import MetaTrader5 as mt5
        print("✅ MetaTrader5模块导入成功")
        print(f"📦 MT5版本: {mt5.__version__ if hasattr(mt5, '__version__') else '未知'}")
        return True
    except ImportError as e:
        print(f"❌ MetaTrader5模块导入失败: {e}")
        print("💡 请确保已安装MetaTrader5: pip install MetaTrader5")
        return False
    except Exception as e:
        print(f"❌ MT5模块异常: {e}")
        return False

def test_mt5_initialize():
    """测试MT5初始化"""
    print("\n🔍 测试MT5初始化...")
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化
        if mt5.initialize():
            print("✅ MT5初始化成功")
            
            # 获取终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"📍 终端路径: {terminal_info.path}")
                print(f"📊 终端版本: {terminal_info.build}")
                print(f"🌐 连接状态: {'已连接' if terminal_info.connected else '未连接'}")
            
            # 关闭连接
            mt5.shutdown()
            return True
        else:
            error = mt5.last_error()
            print(f"❌ MT5初始化失败: {error}")
            print("💡 请确保MetaTrader5终端已安装并可以正常运行")
            return False
            
    except Exception as e:
        print(f"❌ MT5初始化异常: {e}")
        return False

def test_mt5_connection():
    """测试MT5连接"""
    print("\n🔍 测试MT5连接管理器...")
    try:
        from mt5.connection import MT5Connection
        
        connection = MT5Connection()
        print("✅ MT5连接管理器创建成功")
        
        # 测试连接检查
        is_connected = connection.check_connection()
        print(f"🔗 连接状态: {'已连接' if is_connected else '未连接'}")
        
        if not is_connected:
            print("💡 MT5未连接，这是正常的（需要配置账户信息）")
        
        return True
        
    except Exception as e:
        print(f"❌ MT5连接管理器测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试MT5配置...")
    try:
        from config import Config
        
        print(f"📋 MT5登录账号: {Config.MT5_LOGIN}")
        print(f"📋 MT5服务器: {Config.MT5_SERVER}")
        print(f"📋 MT5路径: {Config.MT5_PATH or '默认路径'}")
        
        if Config.MT5_LOGIN == "12345":
            print("⚠️ 检测到测试配置，请在.env文件中配置真实的MT5账户信息")
            print("📝 需要配置的项目:")
            print("   - MT5_LOGIN=您的MT5账户号")
            print("   - MT5_PASSWORD=您的MT5密码")
            print("   - MT5_SERVER=您的MT5服务器")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 MetaTrader5连接测试")
    print("=" * 60)
    
    tests = [
        ("MT5模块导入", test_mt5_import),
        ("MT5初始化", test_mt5_initialize),
        ("MT5连接管理器", test_mt5_connection),
        ("配置检查", test_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 MT5测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 MT5基础功能正常！")
        if Config.MT5_LOGIN == "12345":
            print("⚠️ 请配置真实的MT5账户信息以启用交易功能")
    else:
        print("⚠️ 部分MT5功能异常，请检查错误信息")
    
    print("\n💡 配置说明:")
    print("1. 确保MetaTrader5终端已安装")
    print("2. 在.env文件中配置MT5账户信息")
    print("3. 确保MT5终端可以正常登录")

if __name__ == '__main__':
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试交易执行通知功能
"""

import bark_notifier

def main():
    print("测试交易执行通知（系统接收到信号并执行成功的交易订单）...")
    
    # 测试交易执行通知（这是最重要的）
    test_trade_info = {
        'ticket': 999999,
        'symbol': 'ETHUSD',
        'operation': 'buy',
        'volume': 0.1,
        'price': 3500.0,
        'sl': 3400.0,
        'tp': 3600.0,
        'signal_id': 12345
    }
    
    result = bark_notifier.notify_trade_execution(test_trade_info)
    print(f'交易执行通知结果: {"成功" if result else "失败"}')
    
    # 确认这是最重要的通知
    print()
    print('💰 交易执行通知是最重要的通知类型！')
    print('📱 当系统接收到信号并成功执行交易时，会发送详细的订单信息到两个Bark设备')
    print('🔔 这确保您能及时了解所有交易活动')

if __name__ == "__main__":
    main()

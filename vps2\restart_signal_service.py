#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重启信号接收服务
"""

import psutil
import subprocess
import time
import requests

def restart_signal_service():
    """重启信号接收服务"""
    print('重启信号接收服务...')
    
    # 停止当前服务
    stopped = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'signal_receiver.py' in cmdline:
                    pid = proc.info['pid']
                    print(f'停止信号接收服务 (PID: {pid})...')
                    psutil.Process(pid).terminate()
                    time.sleep(2)
                    stopped = True
                    break
        except:
            pass
    
    if not stopped:
        print('没有找到运行中的信号接收服务')
    
    # 启动新服务
    print('启动信号接收服务...')
    try:
        process = subprocess.Popen(
            ['python', 'signal_receiver.py'],
            stdout=open('logs/signal_receiver.out.log', 'a'),
            stderr=open('logs/signal_receiver.err.log', 'a'),
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        print(f'✅ 信号接收服务已启动 (PID: {process.pid})')
        
        # 等待服务启动
        time.sleep(3)
        
        # 测试健康检查
        try:
            response = requests.get('http://localhost:9999/health', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print('✅ 健康检查通过')
                print(f'服务状态: {data.get("status")}')
                print(f'交易开关: {"开启" if data.get("trading_enabled") else "关闭"}')
                return True
            else:
                print(f'⚠️ 健康检查失败: {response.status_code}')
                return False
        except Exception as e:
            print(f'⚠️ 健康检查请求失败: {e}')
            return False
            
    except Exception as e:
        print(f'❌ 启动信号接收服务失败: {e}')
        return False

if __name__ == "__main__":
    restart_signal_service()

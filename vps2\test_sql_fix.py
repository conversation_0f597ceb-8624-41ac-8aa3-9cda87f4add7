#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试SQL语法修复
"""

import sqlite3

def test_sql_queries():
    """测试SQL查询语句"""
    print("=== 测试SQL查询语句 ===")
    
    try:
        # 连接到数据库
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 测试1: 基本where_clause
        print("\n1. 测试基本where_clause...")
        where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"
        
        sql1 = f'''
            SELECT timestamp, profit
            FROM orders
            {where_clause}
            ORDER BY timestamp ASC
        '''
        print(f"SQL1: {sql1}")
        
        try:
            c.execute(sql1)
            result1 = c.fetchall()
            print(f"   结果: 查询成功，返回 {len(result1)} 条记录")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试2: 带时间过滤的where_clause
        print("\n2. 测试带时间过滤的where_clause...")
        where_clause2 = "WHERE status = 'closed' AND signal_id IS NOT NULL AND DATE(timestamp) >= DATE('now', '-7 days')"
        
        sql2 = f'''
            SELECT timestamp, profit
            FROM orders
            {where_clause2}
            ORDER BY timestamp ASC
        '''
        print(f"SQL2: {sql2}")
        
        try:
            c.execute(sql2)
            result2 = c.fetchall()
            print(f"   结果: 查询成功，返回 {len(result2)} 条记录")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试3: 检查是否有问题的SQL
        print("\n3. 测试可能有问题的SQL...")
        
        # 模拟可能的错误情况
        test_cases = [
            "WHERE status = 'closed' AND signal_id IS NOT NULL AND",  # 末尾有AND
            "WHERE status = 'closed' AND signal_id IS NOT NULL AND AND",  # 重复AND
            "WHERE status = 'closed' AND signal_id IS NOT NULL AND DATE(timestamp) >= DATE('now', '-7 days') AND",  # 末尾有AND
        ]
        
        for i, bad_where in enumerate(test_cases):
            print(f"\n   测试错误情况 {i+1}: {bad_where}")
            bad_sql = f'''
                SELECT timestamp, profit
                FROM orders
                {bad_where}
                ORDER BY timestamp ASC
            '''
            
            try:
                c.execute(bad_sql)
                print(f"   意外成功!")
            except Exception as e:
                print(f"   预期错误: {e}")
        
        # 测试4: 检查表结构
        print("\n4. 检查orders表结构...")
        try:
            c.execute("PRAGMA table_info(orders)")
            columns = c.fetchall()
            print("   表结构:")
            for col in columns:
                print(f"     {col['name']}: {col['type']}")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试5: 检查是否有数据
        print("\n5. 检查orders表数据...")
        try:
            c.execute("SELECT COUNT(*) as count FROM orders")
            count_result = c.fetchone()
            print(f"   总记录数: {count_result['count']}")
            
            c.execute("SELECT COUNT(*) as count FROM orders WHERE status = 'closed'")
            closed_count = c.fetchone()
            print(f"   已平仓记录数: {closed_count['count']}")
            
            c.execute("SELECT COUNT(*) as count FROM orders WHERE signal_id IS NOT NULL")
            signal_count = c.fetchone()
            print(f"   有信号ID的记录数: {signal_count['count']}")
            
        except Exception as e:
            print(f"   错误: {e}")
        
        conn.close()
        print("\n✅ SQL测试完成")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    test_sql_queries()

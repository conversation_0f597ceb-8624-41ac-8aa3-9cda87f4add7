"""
交易策略执行逻辑模块
"""
from datetime import datetime
from trading.engine import trading_strategy_engine
from trading.config_manager import config_manager
from webhook.handler import webhook_handler
from utils.logger import logger

class TradingStrategy:
    """交易策略执行器"""
    
    def __init__(self):
        self.strategy_name = "TradingView综合信号策略"
        self.version = "1.0.0"
    
    def execute_strategy(self, alert_data):
        """
        执行完整的交易策略
        
        这是系统的核心策略执行函数，整合了：
        1. 警报接收和验证
        2. 信号标准化处理
        3. 交易执行决策
        4. 风险控制
        5. 持仓管理
        
        Args:
            alert_data: TradingView警报数据
            
        Returns:
            策略执行结果
        """
        try:
            logger.info(f"开始执行交易策略: {self.strategy_name}")
            
            # 1. 处理和验证警报
            alert_result = webhook_handler.process_alert(alert_data)
            if not alert_result["success"]:
                return {
                    "success": False,
                    "stage": "alert_processing",
                    "error": alert_result["error"]
                }
            
            processed_alert = alert_result["processed_data"]
            
            # 2. 执行交易逻辑
            trading_result = trading_strategy_engine.process_alert(processed_alert)
            
            # 3. 标记警报为已处理
            webhook_handler.mark_alert_processed(processed_alert["alert_id"])
            
            # 4. 构建完整结果
            strategy_result = {
                "success": trading_result["success"],
                "strategy_name": self.strategy_name,
                "alert_id": processed_alert["alert_id"],
                "symbol": processed_alert["standard_symbol"],
                "signal": processed_alert["standard_signal"],
                "timestamp": datetime.now().isoformat(),
                "trading_result": trading_result
            }
            
            if trading_result["success"]:
                logger.info(f"策略执行成功: {processed_alert['standard_symbol']} {processed_alert['standard_signal']}")
            else:
                logger.warning(f"策略执行失败: {trading_result.get('reason', 'Unknown')}")
            
            return strategy_result
            
        except Exception as e:
            logger.error(f"策略执行异常: {e}")
            return {
                "success": False,
                "stage": "strategy_execution",
                "error": str(e)
            }
    
    def get_strategy_status(self):
        """获取策略状态"""
        try:
            # 获取系统配置
            system_config = config_manager.get_system_config()
            portfolio_config = config_manager.get_portfolio_config()
            
            # 获取当前持仓
            from mt5.connection import mt5_connection
            positions = mt5_connection.get_positions()
            account_info = mt5_connection.get_account_info()
            
            # 计算统计信息
            total_positions = len(positions)
            total_pnl = sum(pos["profit"] for pos in positions) if positions else 0.0
            
            return {
                "strategy_name": self.strategy_name,
                "version": self.version,
                "status": "running" if trading_strategy_engine.is_running else "stopped",
                "emergency_stop": system_config.get("emergency_stop") == "True",
                "account_info": account_info,
                "portfolio": {
                    "total_positions": total_positions,
                    "total_pnl": total_pnl,
                    "global_sl_tp_enabled": portfolio_config.get("enabled", False),
                    "global_stop_loss": portfolio_config.get("global_stop_loss_usd", 0),
                    "global_take_profit": portfolio_config.get("global_take_profit_usd", 0)
                },
                "system_limits": {
                    "max_concurrent_trades": system_config.get("max_concurrent_trades", 5),
                    "trading_cooldown": system_config.get("trading_cooldown", 300),
                    "max_daily_loss": system_config.get("max_daily_loss_usd", 500),
                    "max_daily_profit": system_config.get("max_daily_profit_usd", 1000)
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取策略状态失败: {e}")
            return {
                "strategy_name": self.strategy_name,
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def start_strategy(self):
        """启动策略"""
        try:
            trading_strategy_engine.start_monitoring()
            logger.info(f"交易策略已启动: {self.strategy_name}")
            return {"success": True, "message": "策略启动成功"}
        except Exception as e:
            logger.error(f"启动策略失败: {e}")
            return {"success": False, "message": str(e)}
    
    def stop_strategy(self):
        """停止策略"""
        try:
            trading_strategy_engine.stop_monitoring()
            logger.info(f"交易策略已停止: {self.strategy_name}")
            return {"success": True, "message": "策略停止成功"}
        except Exception as e:
            logger.error(f"停止策略失败: {e}")
            return {"success": False, "message": str(e)}
    
    def emergency_stop(self):
        """紧急停止"""
        try:
            # 设置紧急停止标志
            config_manager.update_system_config("emergency_stop", "True")
            
            # 停止策略监控
            trading_strategy_engine.stop_monitoring()

            # 平仓所有订单
            trading_strategy_engine._close_all_positions("紧急停止")
            
            logger.critical("系统紧急停止已激活")
            return {"success": True, "message": "紧急停止执行成功"}
            
        except Exception as e:
            logger.error(f"紧急停止失败: {e}")
            return {"success": False, "message": str(e)}
    
    def resume_from_emergency(self):
        """从紧急停止恢复"""
        try:
            # 取消紧急停止标志
            config_manager.update_system_config("emergency_stop", "False")
            
            # 重新启动策略监控
            trading_strategy_engine.start_monitoring()
            
            logger.info("系统已从紧急停止状态恢复")
            return {"success": True, "message": "系统恢复成功"}
            
        except Exception as e:
            logger.error(f"系统恢复失败: {e}")
            return {"success": False, "message": str(e)}
    
    def get_performance_metrics(self, days=7):
        """获取策略性能指标"""
        try:
            from database.manager import db_manager
            
            # 获取交易统计
            trades_stats = db_manager.execute_query(
                """SELECT 
                    COUNT(*) as total_trades,
                    COUNT(CASE WHEN profit_usd > 0 THEN 1 END) as winning_trades,
                    COUNT(CASE WHEN profit_usd < 0 THEN 1 END) as losing_trades,
                    AVG(profit_usd) as avg_profit,
                    SUM(profit_usd) as total_profit,
                    MAX(profit_usd) as max_profit,
                    MIN(profit_usd) as min_profit
                   FROM trades 
                   WHERE close_time >= datetime('now', '-{} days')
                   AND status = 'CLOSED'""".format(days)
            )
            
            if trades_stats:
                stats = trades_stats[0]
                total_trades = stats[0] or 0
                winning_trades = stats[1] or 0
                losing_trades = stats[2] or 0
                
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
                
                return {
                    "period_days": days,
                    "total_trades": total_trades,
                    "winning_trades": winning_trades,
                    "losing_trades": losing_trades,
                    "win_rate_percent": round(win_rate, 2),
                    "average_profit": round(stats[3] or 0, 2),
                    "total_profit": round(stats[4] or 0, 2),
                    "max_profit": round(stats[5] or 0, 2),
                    "min_profit": round(stats[6] or 0, 2),
                    "calculated_at": datetime.now().isoformat()
                }
            else:
                return {
                    "period_days": days,
                    "total_trades": 0,
                    "winning_trades": 0,
                    "losing_trades": 0,
                    "win_rate_percent": 0,
                    "average_profit": 0,
                    "total_profit": 0,
                    "max_profit": 0,
                    "min_profit": 0,
                    "calculated_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {
                "error": str(e),
                "calculated_at": datetime.now().isoformat()
            }
    
    def validate_strategy_config(self):
        """验证策略配置"""
        try:
            issues = []
            
            # 检查MT5连接
            from mt5.connection import mt5_connection
            if not mt5_connection.check_connection():
                issues.append("MT5连接失败")
            
            # 检查交易对配置
            symbol_configs = config_manager.get_all_symbol_configs()
            enabled_symbols = [s for s, c in symbol_configs.items() if c.get("enabled", False)]
            
            if not enabled_symbols:
                issues.append("没有启用的交易对")
            
            # 检查系统配置
            system_config = config_manager.get_system_config()
            required_configs = ["trading_cooldown", "max_concurrent_trades"]
            
            for config_key in required_configs:
                if not system_config.get(config_key):
                    issues.append(f"缺少系统配置: {config_key}")
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "enabled_symbols": enabled_symbols,
                "checked_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"验证策略配置失败: {e}")
            return {
                "valid": False,
                "issues": [f"配置验证异常: {str(e)}"],
                "checked_at": datetime.now().isoformat()
            }

# 创建全局策略实例
trading_strategy = TradingStrategy()

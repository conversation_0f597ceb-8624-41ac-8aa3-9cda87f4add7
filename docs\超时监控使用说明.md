# 超时监控功能使用说明

## 📋 功能概述

超时监控功能用于自动平仓长时间未收到新警报的订单，防止订单在市场变化时无法及时调整。

## 🎯 设计原理

### 自动监控 vs 手动监控

1. **系统下单（TradingView警报触发）**
   - ✅ **自动添加超时监控**
   - 📡 收到新警报时自动重置超时时间
   - ⏰ 超时后自动平仓

2. **手动下单（MT5直接下单）**
   - ❌ **不自动添加监控**
   - 🔧 需要手动选择是否添加监控
   - 💡 避免误平仓重要的手动订单

## 🚀 使用方法

### 1. 查看监控状态

在**持仓管理**页面，每个订单都有：
- 📊 **平仓倒计时**列：显示剩余时间
- 🔘 **监控按钮**：添加/移除监控

### 2. 手动添加监控

对于手动下单的订单：

1. 进入**持仓管理**页面
2. 找到要监控的订单
3. 点击 🛡️ **添加监控**按钮
4. 输入超时时间（秒）
5. 确认添加

### 3. 移除监控

如果不想监控某个订单：

1. 找到正在监控的订单
2. 点击 🛡️❌ **移除监控**按钮
3. 确认移除

### 4. 监控状态说明

| 状态 | 显示 | 说明 |
|------|------|------|
| 未监控 | `未监控` | 订单没有超时监控 |
| 监控中 | `3:45` | 剩余3分45秒 |
| 即将超时 | `0:30` 橙色 | 剩余30秒，即将超时 |
| 已超时 | `⚠️ 已超时` 红色 | 订单已被自动平仓 |

## ⚙️ 监控控制

### 全局监控开关

在持仓页面顶部的**超时平仓监控**面板：
- 🟢 **启动监控**：开始监控所有已设置的订单
- 🔴 **停止监控**：暂停所有监控（不会删除设置）

### 监控统计

显示当前监控中的订单数量和系统状态。

## 🛡️ 安全机制

### 1. 手动订单保护
- 手动下单的订单默认**不会**被自动监控
- 避免误平仓重要的长期持仓
- 用户可以主动选择是否添加监控

### 2. 系统订单管理
- TradingView警报触发的订单自动添加监控
- 收到新警报时自动重置超时时间
- 确保策略订单能及时响应市场变化

### 3. 灵活控制
- 可以随时添加/移除监控
- 可以调整超时时间
- 可以暂停/恢复全局监控

## 📝 最佳实践

### 1. 系统下单
- 保持监控开启
- 使用合理的超时时间（建议3-5分钟）
- 确保TradingView警报正常工作

### 2. 手动下单
- 短期交易：建议添加监控
- 长期持仓：不建议添加监控
- 测试订单：可以添加较短的超时时间

### 3. 监控管理
- 定期检查监控状态
- 根据市场情况调整超时时间
- 重要时段保持监控开启

## ⚠️ 注意事项

1. **超时平仓是不可逆的**
   - 订单一旦被超时平仓就无法恢复
   - 请谨慎设置超时时间

2. **网络连接影响**
   - 确保系统网络连接稳定
   - TradingView警报延迟可能影响重置

3. **市场时段考虑**
   - 非交易时段可能不会收到警报
   - 周末和节假日需要特别注意

4. **监控资源**
   - 监控过多订单可能影响性能
   - 建议合理控制监控数量

## 🔧 故障排除

### 监控不工作
1. 检查全局监控是否开启
2. 确认订单是否已添加监控
3. 检查系统日志

### 误平仓问题
1. 检查超时时间设置是否合理
2. 确认TradingView警报是否正常
3. 考虑移除不需要监控的订单

### 警报重置失败
1. 检查webhook接收是否正常
2. 确认警报格式是否正确
3. 查看系统日志中的错误信息

## 📞 技术支持

如果遇到问题，请：
1. 查看系统日志
2. 检查配置设置
3. 联系技术支持

---

**记住：超时监控是为了保护您的交易，但手动订单的监控需要您主动管理！** 🛡️

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 信号详情</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: calc(100vh - 56px);
        }
        .sidebar a {
            color: #adb5bd;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
        }
        .sidebar a:hover, .sidebar a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar i {
            margin-right: 8px;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }
        .stats-card {
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-green {
            background-color: #28a745;
        }
        .status-red {
            background-color: #dc3545;
        }
        .status-yellow {
            background-color: #ffc107;
        }
        .detail-row {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">信号详情</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="{{ url_for('signals') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回信号列表
                        </a>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <!-- 信号基本信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">基本信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">信号ID</div>
                                    <div class="col-md-8">{{ signal['id'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">交易对</div>
                                    <div class="col-md-8">{{ signal['trading_pair'] }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">信号类型</div>
                                    <div class="col-md-8">
                                        {% if signal['signal_type'] == 'buy' %}
                                            <span class="badge bg-success">买入</span>
                                        {% else %}
                                            <span class="badge bg-danger">卖出</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">时间周期</div>
                                    <div class="col-md-8">{{ signal.get('interval', '-') }}</div>
                                </div>
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">接收时间</div>
                                    <div class="col-md-8">{{ signal.get('timestamp', '').replace('T', ' ').split('.')[0] if signal.get('timestamp') else '-' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 处理状态信息 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">处理状态</h5>
                            </div>
                            <div class="card-body">
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">处理状态</div>
                                    <div class="col-md-8">
                                        {% if signal.get('process_status') == 'success' %}
                                            <span class="badge bg-success">处理成功</span>
                                        {% elif signal.get('process_status') == 'failed' %}
                                            <span class="badge bg-danger">处理失败</span>
                                        {% elif signal.get('process_status') == 'processing' %}
                                            <span class="badge bg-info">处理中</span>
                                        {% else %}
                                            <span class="badge bg-warning">待处理</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if signal.get('process_time') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">处理时间</div>
                                    <div class="col-md-8">{{ signal.get('process_time', '').replace('T', ' ').split('.')[0] if signal.get('process_time') else '-' }}</div>
                                </div>
                                {% endif %}
                                {% if signal.get('success_details') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">成功详情</div>
                                    <div class="col-md-8 text-success">{{ signal.get('success_details') }}</div>
                                </div>
                                {% endif %}
                                {% if signal.get('failure_reason') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">失败原因</div>
                                    <div class="col-md-8 text-danger">{{ signal.get('failure_reason') }}</div>
                                </div>
                                {% endif %}
                                {% if signal.get('order_result') %}
                                <div class="row detail-row">
                                    <div class="col-md-4 detail-label">执行结果</div>
                                    <div class="col-md-8">
                                        {% if signal.get('order_result') == '成功' %}
                                            <span class="badge bg-success">{{ signal.get('order_result') }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ signal.get('order_result') }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格级别信息 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">价格级别信息</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">当前价格</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('close_price', 0)|float) if signal.get('close_price') else '-' }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">均值</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('mean', 0)|float) if signal.get('mean') else '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">R1</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('r1', 0)|float) if signal.get('r1') else '-' }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">S1</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('s1', 0)|float) if signal.get('s1') else '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">R2</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('r2', 0)|float) if signal.get('r2') else '-' }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">S2</div>
                                            <div class="col-6">{{ "%.5f"|format(signal.get('s2', 0)|float) if signal.get('s2') else '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">RSI信号</div>
                                            <div class="col-6">{{ signal.get('rsi_signal', '-') }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-6 detail-label">RSI值</div>
                                            <div class="col-6">{{ "%.2f"|format(signal.get('rsi_value', 0)|float) if signal.get('rsi_value') else '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单执行情况 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">订单执行情况</h5>
                            </div>
                            <div class="card-body">
                                {% if signal.get('order_ticket') or order %}
                                    {% if order %}
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">订单号</div>
                                            <div class="col-md-4">
                                                <strong>{{ order.get('ticket', signal.get('order_ticket', '-')) }}</strong>
                                            </div>
                                            <div class="col-md-2 detail-label">当前状态</div>
                                            <div class="col-md-4">
                                                {% if order.get('status') == 'open' %}
                                                    <span class="badge bg-success">持仓中</span>
                                                {% elif order.get('status') == 'closed' %}
                                                    <span class="badge bg-secondary">已平仓</span>
                                                {% elif order.get('status') == 'partially_closed' %}
                                                    <span class="badge bg-warning">部分平仓</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">未知</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">开仓价格</div>
                                            <div class="col-md-4">{{ "%.5f"|format(order.get('price', 0)|float) if order.get('price') else '-' }}</div>
                                            <div class="col-md-2 detail-label">交易量</div>
                                            <div class="col-md-4">{{ order.get('volume', '-') }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">止损价</div>
                                            <div class="col-md-4">{{ "%.5f"|format(order.get('sl', 0)|float) if order.get('sl') else '-' }}</div>
                                            <div class="col-md-2 detail-label">止盈价</div>
                                            <div class="col-md-4">{{ "%.5f"|format(order.get('tp', 0)|float) if order.get('tp') else '-' }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">信号S1</div>
                                            <div class="col-md-4">{{ "%.5f"|format(signal.get('s1', 0)|float) if signal.get('s1') else '-' }}</div>
                                            <div class="col-md-2 detail-label">信号R1</div>
                                            <div class="col-md-4">{{ "%.5f"|format(signal.get('r1', 0)|float) if signal.get('r1') else '-' }}</div>
                                        </div>
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">数据库盈亏</div>
                                            <div class="col-md-4 {% if order.get('profit', 0)|float > 0 %}text-success{% elif order.get('profit', 0)|float < 0 %}text-danger{% endif %}">
                                                {{ "%.2f"|format(order.get('profit', 0)|float) }}
                                            </div>
                                            <div class="col-md-2 detail-label">实时盈亏</div>
                                            <div class="col-md-4 {% if order.get('real_profit', 0)|float > 0 %}text-success{% elif order.get('real_profit', 0)|float < 0 %}text-danger{% endif %}">
                                                <strong>{{ "%.2f"|format(order.get('real_profit', 0)|float) }}</strong>
                                                {% if order.get('current_price') %}
                                                    <small class="text-muted">(当前价: {{ "%.5f"|format(order.get('current_price', 0)|float) }})</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% if order.get('status') == 'closed' %}
                                            <div class="row detail-row">
                                                <div class="col-md-2 detail-label">平仓价格</div>
                                                <div class="col-md-4">{{ "%.5f"|format(order.get('close_price', 0)|float) if order.get('close_price') else '-' }}</div>
                                                <div class="col-md-2 detail-label">平仓时间</div>
                                                <div class="col-md-4">{{ order.get('close_time', '').replace('T', ' ').split('.')[0] if order.get('close_time') else '-' }}</div>
                                            </div>
                                        {% endif %}
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <a href="{{ url_for('order_detail', order_id=order['id']) }}" class="btn btn-outline-primary">
                                                    <i class="bi bi-info-circle"></i> 查看订单详情
                                                </a>
                                                {% if order['status'] in ['open', 'partially_closed'] %}
                                                    <button class="btn btn-outline-danger close-position-btn"
                                                            data-ticket="{{ order['ticket'] }}"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#closePositionModal">
                                                        <i class="bi bi-x-circle"></i> 平仓
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="row detail-row">
                                            <div class="col-md-2 detail-label">订单号</div>
                                            <div class="col-md-4">{{ signal.get('order_ticket', '-') }}</div>
                                            <div class="col-md-2 detail-label">执行结果</div>
                                            <div class="col-md-4">
                                                {% if signal.get('order_result') == '成功' %}
                                                    <span class="badge bg-success">成功</span>
                                                {% else %}
                                                    <span class="badge bg-danger">{{ signal.get('order_result', '未知') }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <div class="alert alert-warning mb-0">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>此信号尚未产生订单或执行失败
                                    </div>
                                    {% if signal.get('process_status') != 'success' and signal.get('process_status') != 'failed' %}
                                        <div class="mt-3">
                                            <button class="btn btn-primary manual-process-btn" data-signal-id="{{ signal['id'] }}">
                                                <i class="bi bi-play-fill"></i> 手动执行
                                            </button>
                                        </div>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Webhook通知内容 -->
                {% if signal.get('webhook_content') %}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Webhook通知内容</h5>
                            </div>
                            <div class="card-body">
                                <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto; font-size: 0.85rem;">{{ signal.get('webhook_content') }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </main>
        </div>
    </div>

    <!-- 平仓确认弹窗 -->
    <div class="modal fade" id="closePositionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认平仓</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要平仓订单 <span id="closePositionTicket" class="fw-bold"></span> 吗？</p>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="partialCloseCheck">
                        <label class="form-check-label" for="partialCloseCheck">
                            部分平仓
                        </label>
                    </div>
                    <div id="partialVolumeGroup" class="mb-3" style="display: none;">
                        <label for="partialVolume" class="form-label">平仓量</label>
                        <input type="number" class="form-control" id="partialVolume" step="0.01" min="0.01">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmCloseBtn">确认平仓</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 平仓按钮点击事件
            $('.close-position-btn').click(function() {
                const ticket = $(this).data('ticket');
                $('#closePositionTicket').text(ticket);
            });

            // 部分平仓选项
            $('#partialCloseCheck').change(function() {
                if($(this).is(':checked')) {
                    $('#partialVolumeGroup').show();
                } else {
                    $('#partialVolumeGroup').hide();
                }
            });

            // 确认平仓事件
            $('#confirmCloseBtn').click(function() {
                const ticket = $('#closePositionTicket').text();
                let data = {
                    ticket: ticket
                };
                
                if($('#partialCloseCheck').is(':checked')) {
                    const volume = $('#partialVolume').val();
                    if(volume && volume > 0) {
                        data.volume = volume;
                    } else {
                        alert('请输入有效的平仓量');
                        return;
                    }
                }
                
                $.ajax({
                    url: '/close_position',
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        if(response.success) {
                            alert('平仓成功');
                            location.reload();
                        } else {
                            alert('平仓失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后重试');
                    },
                    complete: function() {
                        $('#closePositionModal').modal('hide');
                    }
                });
            });

            // 手动执行信号
            $('.manual-process-btn').click(function() {
                const signalId = $(this).data('signal-id');
                
                if(confirm('确定要手动执行此信号吗？')) {
                    $.ajax({
                        url: '/manual_process_signal',
                        type: 'POST',
                        data: {
                            signal_id: signalId
                        },
                        success: function(response) {
                            if(response.success) {
                                alert('信号执行成功');
                                location.reload();
                            } else {
                                alert('信号执行失败: ' + response.message);
                            }
                        },
                        error: function() {
                            alert('请求失败，请稍后重试');
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>

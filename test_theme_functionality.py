#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主题功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import requests

def test_ui_config_api():
    """测试UI配置API"""
    print("=== 测试UI配置API ===")
    
    base_url = "http://localhost:7000"
    
    # 测试获取UI配置
    try:
        response = requests.get(f"{base_url}/api/config/ui")
        print(f"GET /api/config/ui 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"UI配置数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if data.get('success'):
                ui_config = data.get('data', {})
                current_theme = ui_config.get('ui_theme', 'light')
                print(f"当前主题: {current_theme}")
                
                # 测试切换主题
                new_theme = 'dark' if current_theme == 'light' else 'light'
                print(f"尝试切换到: {new_theme}")
                
                # 更新主题
                ui_config['ui_theme'] = new_theme
                update_response = requests.put(
                    f"{base_url}/api/config/ui",
                    json=ui_config,
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"PUT /api/config/ui 状态码: {update_response.status_code}")
                if update_response.status_code == 200:
                    update_data = update_response.json()
                    print(f"更新结果: {json.dumps(update_data, ensure_ascii=False, indent=2)}")
                    print("✅ 主题切换API测试成功")
                else:
                    print(f"❌ 主题更新失败: {update_response.text}")
            else:
                print(f"❌ API返回失败: {data}")
        else:
            print(f"❌ API请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def check_ui_config_file():
    """检查UI配置文件"""
    print("\n=== 检查UI配置文件 ===")
    
    config_file = 'config/ui_config.json'
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"配置文件内容:")
            print(json.dumps(config, ensure_ascii=False, indent=2))
            
            theme = config.get('ui_theme', 'light')
            print(f"当前主题设置: {theme}")
            
            if theme in ['light', 'dark', 'auto']:
                print("✅ 主题设置有效")
            else:
                print(f"⚠️  未知的主题设置: {theme}")
                
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    else:
        print("❌ UI配置文件不存在")

def check_css_theme_support():
    """检查CSS主题支持"""
    print("\n=== 检查CSS主题支持 ===")
    
    css_file = 'static/css/style.css'
    
    if os.path.exists(css_file):
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # 检查深色主题相关的CSS
            dark_theme_indicators = [
                '[data-theme="dark"]',
                'background-color: #1a1a1a',
                'color: #e0e0e0'
            ]
            
            found_indicators = []
            for indicator in dark_theme_indicators:
                if indicator in css_content:
                    found_indicators.append(indicator)
            
            print(f"找到的深色主题CSS指示器: {len(found_indicators)}/{len(dark_theme_indicators)}")
            
            if len(found_indicators) == len(dark_theme_indicators):
                print("✅ CSS深色主题支持完整")
            else:
                print("⚠️  CSS深色主题支持不完整")
                missing = set(dark_theme_indicators) - set(found_indicators)
                print(f"缺失的指示器: {missing}")
                
        except Exception as e:
            print(f"❌ 读取CSS文件失败: {e}")
    else:
        print("❌ CSS文件不存在")

def check_js_theme_support():
    """检查JavaScript主题支持"""
    print("\n=== 检查JavaScript主题支持 ===")
    
    js_file = 'static/js/main.js'
    
    if os.path.exists(js_file):
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # 检查主题相关的JavaScript函数
            theme_functions = [
                'initializeTheme',
                'applyTheme',
                'saveThemePreference'
            ]
            
            found_functions = []
            for func in theme_functions:
                if func in js_content:
                    found_functions.append(func)
            
            print(f"找到的主题函数: {len(found_functions)}/{len(theme_functions)}")
            
            if len(found_functions) == len(theme_functions):
                print("✅ JavaScript主题支持完整")
            else:
                print("⚠️  JavaScript主题支持不完整")
                missing = set(theme_functions) - set(found_functions)
                print(f"缺失的函数: {missing}")
                
        except Exception as e:
            print(f"❌ 读取JavaScript文件失败: {e}")
    else:
        print("❌ JavaScript文件不存在")

if __name__ == "__main__":
    print("开始测试主题功能...\n")
    
    check_ui_config_file()
    check_css_theme_support()
    check_js_theme_support()
    test_ui_config_api()
    
    print("\n测试完成！")
    print("\n使用说明:")
    print("1. 在浏览器中打开系统界面")
    print("2. 进入配置管理页面")
    print("3. 在界面设置中选择不同的主题")
    print("4. 观察界面颜色变化")

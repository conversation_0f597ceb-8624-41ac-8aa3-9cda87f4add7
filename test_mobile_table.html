<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端表格滚动测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-section {
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .device-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">移动端表格滚动测试</h1>
        
        <div class="device-info">
            <strong>设备信息：</strong>
            <span id="deviceInfo"></span>
        </div>
        
        <!-- 测试1：首页持仓表格 -->
        <div class="test-section">
            <h3>测试1：首页持仓表格</h3>
            <div class="table-responsive">
                <table class="table table-hover" id="positionsTable">
                    <thead>
                        <tr>
                            <th>交易对</th>
                            <th>方向</th>
                            <th>手数</th>
                            <th>开仓价</th>
                            <th>当前价</th>
                            <th>盈亏</th>
                            <th>止盈/止损</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>EURUSD</td>
                            <td><span class="badge bg-success">做多</span></td>
                            <td>0.10</td>
                            <td>1.0850</td>
                            <td>1.0875</td>
                            <td class="text-success">+$25.00</td>
                            <td>TP: 1.0900<br>SL: 1.0800</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary btn-sm">修改</button>
                                    <button class="btn btn-danger btn-sm">平仓</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>GBPUSD</td>
                            <td><span class="badge bg-danger">做空</span></td>
                            <td>0.05</td>
                            <td>1.2650</td>
                            <td>1.2625</td>
                            <td class="text-success">+$12.50</td>
                            <td>TP: 1.2600<br>SL: 1.2700</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary btn-sm">修改</button>
                                    <button class="btn btn-danger btn-sm">平仓</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 测试2：持仓管理页面表格 -->
        <div class="test-section">
            <h3>测试2：持仓管理页面表格</h3>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>交易对</th>
                            <th>方向</th>
                            <th>手数</th>
                            <th>开仓价</th>
                            <th>当前价</th>
                            <th>浮动盈亏</th>
                            <th>止盈/止损</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="positionsDetailTableBody">
                        <tr>
                            <td>123456789</td>
                            <td>EURUSD</td>
                            <td><span class="badge bg-success">做多</span></td>
                            <td>0.10</td>
                            <td>1.0850</td>
                            <td>1.0875</td>
                            <td class="text-success">+$25.00</td>
                            <td>TP: 1.0900<br>SL: 1.0800</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-warning btn-sm">止盈止损</button>
                                    <button class="btn btn-danger btn-sm">平仓</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>987654321</td>
                            <td>GBPUSD</td>
                            <td><span class="badge bg-danger">做空</span></td>
                            <td>0.05</td>
                            <td>1.2650</td>
                            <td>1.2625</td>
                            <td class="text-success">+$12.50</td>
                            <td>TP: 1.2600<br>SL: 1.2700</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-warning btn-sm">止盈止损</button>
                                    <button class="btn btn-danger btn-sm">平仓</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 测试3：配置管理表格 -->
        <div class="test-section">
            <h3>测试3：配置管理表格</h3>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>交易对</th>
                            <th class="d-none d-md-table-cell">分类</th>
                            <th>状态</th>
                            <th class="d-none d-lg-table-cell">手数</th>
                            <th class="d-none d-md-table-cell">止损(USD)</th>
                            <th class="d-none d-md-table-cell">止盈(USD)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>EURUSD</td>
                            <td class="d-none d-md-table-cell">主要货币对</td>
                            <td><span class="badge bg-success">启用</span></td>
                            <td class="d-none d-lg-table-cell">0.10</td>
                            <td class="d-none d-md-table-cell">50</td>
                            <td class="d-none d-md-table-cell">100</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary btn-sm">编辑</button>
                                    <button class="btn btn-secondary btn-sm">禁用</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>GBPUSD</td>
                            <td class="d-none d-md-table-cell">主要货币对</td>
                            <td><span class="badge bg-success">启用</span></td>
                            <td class="d-none d-lg-table-cell">0.05</td>
                            <td class="d-none d-md-table-cell">30</td>
                            <td class="d-none d-md-table-cell">60</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary btn-sm">编辑</button>
                                    <button class="btn btn-secondary btn-sm">禁用</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults">
                <p>正在检测表格滚动功能...</p>
            </div>
        </div>
    </div>
    
    <script src="/static/js/main.js"></script>
    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const info = `
                屏幕宽度: ${window.innerWidth}px, 
                屏幕高度: ${window.innerHeight}px, 
                用户代理: ${navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备'}
            `;
            document.getElementById('deviceInfo').textContent = info;
        }
        
        // 测试表格滚动功能
        function testTableScroll() {
            const results = [];
            const tables = document.querySelectorAll('.table-responsive');
            
            tables.forEach((container, index) => {
                const table = container.querySelector('table');
                if (table) {
                    const containerWidth = container.clientWidth;
                    const tableWidth = table.scrollWidth;
                    const canScroll = tableWidth > containerWidth;
                    const hasOverflowX = getComputedStyle(container).overflowX === 'auto';
                    
                    results.push(`
                        <div class="alert ${canScroll && hasOverflowX ? 'alert-success' : 'alert-warning'}">
                            <strong>表格 ${index + 1}:</strong><br>
                            容器宽度: ${containerWidth}px<br>
                            表格宽度: ${tableWidth}px<br>
                            需要滚动: ${canScroll ? '是' : '否'}<br>
                            overflow-x: ${getComputedStyle(container).overflowX}<br>
                            状态: ${canScroll && hasOverflowX ? '✅ 可以滚动' : '❌ 无法滚动'}
                        </div>
                    `);
                }
            });
            
            document.getElementById('testResults').innerHTML = results.join('');
        }
        
        // 页面加载完成后执行测试
        window.addEventListener('load', () => {
            updateDeviceInfo();
            setTimeout(testTableScroll, 1000);
            setTimeout(testTableScroll, 3000);
        });
        
        // 窗口大小改变时重新测试
        window.addEventListener('resize', () => {
            updateDeviceInfo();
            setTimeout(testTableScroll, 500);
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Bark通知功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from notifications.bark import bark_manager
import json

def test_bark_notification():
    """测试Bark通知功能"""
    print("开始测试Bark通知功能...")
    
    # 测试警报转发通知
    test_alert_data = {
        "symbol": "BTCUSD",
        "signal": "BUY",
        "timeframe": "1H",
        "price": "65000",
        "signal_strength": "强",
        "timestamp": "2025-09-01T08:45:00Z",
        "alert_type": "综合信号",
        "source": "tradingview",
        "指标名称": "combined",
        "交易对": "BTCUSD",
        "周期": "1H",
        "价格": "65000",
        "事件": "综合做多信号",
        "信号": "combined_long",
        "MRC信号": "做多",
        "SRBR信号": "看多",
        "时间": "2025-09-01T08:45:00Z",
        "描述": "MRC和SRBR信号方向一致，确认做多"
    }
    
    print("测试数据:")
    print(json.dumps(test_alert_data, ensure_ascii=False, indent=2))
    
    # 测试警报转发
    print("\n1. 测试警报转发功能...")
    result = bark_manager.send_alert_forward(test_alert_data)
    print(f"警报转发结果: {result}")
    
    # 测试直接通知发送
    print("\n2. 测试直接通知发送...")
    direct_result = bark_manager.send_notification(
        "TradingView警报转发", 
        {
            "symbol": "BTCUSD",
            "signal": "BUY",
            "timeframe": "1H",
            "price": "65000",
            "strength": "强",
            "timestamp": "2025-09-01 16:45:00",
            "raw_data": str(test_alert_data)
        }
    )
    print(f"直接通知发送结果: {direct_result}")
    
    # 检查配置状态
    print("\n3. 检查配置状态...")
    print(f"Bark转发启用状态: {bark_manager._is_notification_type_enabled('TradingView警报转发')}")
    
    # 检查设备密钥
    print("\n4. 检查设备密钥...")
    device_keys = bark_manager.get_device_keys()
    print(f"设备密钥数量: {len(device_keys)}")
    for i, key in enumerate(device_keys, 1):
        print(f"设备{i}: {key[:10]}...")

if __name__ == "__main__":
    test_bark_notification()

@echo off
REM TradingView自动交易系统启动脚本
title TradingView自动交易系统

echo ========================================
echo TradingView自动交易系统
echo ========================================

REM 检查虚拟环境是否存在
if not exist venv (
    echo 错误: 虚拟环境不存在，请先运行 deploy\install.bat 进行安装
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist .env (
    echo 错误: 配置文件 .env 不存在，请先复制 .env.example 为 .env 并填入配置信息
    pause
    exit /b 1
)

echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

echo 正在启动系统...
echo.
echo 系统启动后可通过以下地址访问:
echo Web管理界面: http://localhost:5000
echo API文档: http://localhost:5000/api/system/health
echo.
echo 按 Ctrl+C 停止系统
echo.

REM 启动系统
python run.py

echo.
echo 系统已停止
pause

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView自动交易系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i>
                TradingView自动交易系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showPage('dashboard')" id="nav-dashboard">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('trades')" id="nav-trades">
                            <i class="bi bi-list-ul"></i> 交易记录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('positions')" id="nav-positions">
                            <i class="bi bi-pie-chart"></i> 持仓管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('config')" id="nav-config">
                            <i class="bi bi-gear"></i> 配置管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('alerts')" id="nav-alerts">
                            <i class="bi bi-bell"></i> 警报历史
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('logs')" id="nav-logs">
                            <i class="bi bi-file-text"></i> 系统日志
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text me-3">
                            <span id="systemStatus" class="status-indicator status-offline"></span>
                            <span id="systemStatusText">检查中...</span>
                        </span>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showPage('settings')">
                                <i class="bi bi-sliders"></i> 系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 警报区域 -->
        <div id="alertContainer"></div>

        <!-- 仪表板页面 -->
        <div id="dashboardPage" class="page-content">
            <!-- 系统状态卡片 -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="metric-value text-primary" id="accountBalance">$0.00</div>
                            <div class="metric-label">账户余额</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="metric-value text-success" id="todayProfit">$0.00</div>
                            <div class="metric-label">今日盈亏</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="metric-value text-info" id="openPositions">0</div>
                            <div class="metric-label">持仓数量</div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="metric-value text-warning" id="todayAlerts">0</div>
                            <div class="metric-label">今日警报</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-toggles"></i> 系统控制
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>策略控制</h6>
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-success btn-action" onclick="startStrategy()">
                                            <i class="bi bi-play-fill"></i> 启动策略
                                        </button>
                                        <button type="button" class="btn btn-warning btn-action" onclick="stopStrategy()">
                                            <i class="bi bi-pause-fill"></i> 停止策略
                                        </button>
                                        <button type="button" class="btn btn-danger btn-action" onclick="emergencyStop()">
                                            <i class="bi bi-stop-fill"></i> 紧急停止
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>系统操作</h6>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-info btn-action" onclick="refreshData()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-action" onclick="showConfigModal()">
                                            <i class="bi bi-gear"></i> 系统配置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时数据 -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i> 当前持仓
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="positionsTable">
                                    <thead>
                                        <tr>
                                            <th>交易对</th>
                                            <th>方向</th>
                                            <th>手数</th>
                                            <th>开仓价</th>
                                            <th>当前价</th>
                                            <th>盈亏</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="positionsTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">暂无持仓</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bell"></i> 最新警报
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="recentAlerts">
                                <div class="text-center text-muted">暂无警报</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他页面内容将通过JavaScript动态加载 -->
        <div id="tradesPage" class="page-content" style="display: none;"></div>
        <div id="positionsPage" class="page-content" style="display: none;"></div>
        <div id="configPage" class="page-content" style="display: none;"></div>
        <div id="alertsPage" class="page-content" style="display: none;"></div>
        <div id="logsPage" class="page-content" style="display: none;"></div>
        <div id="settingsPage" class="page-content" style="display: none;"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 认证检查 -->
    <script>
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('session_token') || sessionStorage.getItem('session_token');
            if (!token) {
                window.location.href = '/login';
                return;
            }

            // 验证token有效性
            fetch('/api/auth/validate', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                if (!response.ok) {
                    // token无效，清除并跳转到登录页
                    localStorage.removeItem('session_token');
                    sessionStorage.removeItem('session_token');
                    window.location.href = '/login';
                }
            }).catch(error => {
                console.error('认证验证失败:', error);
                window.location.href = '/login';
            });
        });
    </script>

    <!-- 自定义JS -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/config.js"></script>
    <script src="/static/js/pages.js"></script>
</body>
</html>

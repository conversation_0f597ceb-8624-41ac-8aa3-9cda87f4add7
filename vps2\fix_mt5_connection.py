#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5连接修复工具
"""
import os
import sys
import json
import logging
import time
import shutil
import subprocess

# 设置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s [%(levelname)s] %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger()

def print_separator(ch='=', length=50):
    print(ch * length)

def print_header(text):
    print_separator()
    print(text)
    print_separator()

def backup_config():
    """备份配置文件"""
    try:
        if os.path.exists('config.json'):
            backup_path = f'config.json.bak.{int(time.time())}'
            shutil.copy2('config.json', backup_path)
            logger.info(f"配置文件已备份至: {backup_path}")
            return True
    except Exception as e:
        logger.error(f"备份配置文件失败: {e}")
    return False

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}

def save_config(config):
    """保存配置文件"""
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)
        logger.info("配置文件已更新")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

def find_mt5_executable():
    """查找MT5可执行文件"""
    common_paths = [
        r"C:\Program Files\MetaTrader 5\terminal64.exe",
        r"C:\Program Files (x86)\MetaTrader 5\terminal64.exe",
        r"C:\Program Files\MetaTrader 5\terminal.exe",
        r"C:\Program Files (x86)\MetaTrader 5\terminal.exe"
    ]
    
    for path in common_paths:
        if os.path.isfile(path):
            logger.info(f"找到MT5可执行文件: {path}")
            return path
    
    try:
        # 尝试查找运行中的进程
        if sys.platform == 'win32':
            import subprocess
            result = subprocess.run(['tasklist', '/fi', 'imagename eq terminal64.exe', '/fo', 'csv'], 
                                   capture_output=True, text=True)
            if 'terminal64.exe' in result.stdout:
                logger.info("发现MT5正在运行")
                # 如果进程在运行，但找不到可执行文件，使用默认路径
                return r"C:\Program Files\MetaTrader 5\terminal64.exe"
    except:
        pass
    
    return None

def test_mt5_import():
    """测试导入MT5模块"""
    try:
        import MetaTrader5 as mt5
        logger.info("成功导入MetaTrader5模块")
        return True
    except ImportError:
        logger.error("导入MetaTrader5模块失败")
        logger.info("请安装MetaTrader5模块: pip install MetaTrader5")
        return False

def test_mt5_connection(config):
    """测试MT5连接"""
    try:
        import MetaTrader5 as mt5
        
        mt5_path = config.get('mt5_path', '')
        mt5_login = config.get('mt5_login', '')
        mt5_password = config.get('mt5_password', '')
        mt5_server = config.get('mt5_server', '')
        
        # 关闭之前的连接（如果有）
        mt5.shutdown()
        
        # 初始化MT5
        logger.info(f"尝试使用路径初始化MT5: {mt5_path}")
        if not mt5.initialize(path=mt5_path):
            logger.warning(f"初始化失败: {mt5.last_error()}")
            logger.info("尝试不带路径初始化...")
            if not mt5.initialize():
                logger.error(f"不带路径初始化也失败: {mt5.last_error()}")
                return False
        
        # 获取MT5信息
        terminal_info = mt5.terminal_info()
        if terminal_info is None:
            logger.error("无法获取MT5终端信息")
            return False
        
        logger.info(f"MT5连接成功: {terminal_info.name}")
        
        # 尝试登录
        try:
            login_id = int(mt5_login)
            logger.info(f"尝试登录MT5: {login_id}@{mt5_server}")
            login_result = mt5.login(login=login_id, password=mt5_password, server=mt5_server)
            if not login_result:
                logger.warning(f"登录失败: {mt5.last_error()}")
                
                # 尝试不带服务器名登录
                logger.info("尝试不带服务器名登录...")
                login_result = mt5.login(login=login_id, password=mt5_password)
                if not login_result:
                    logger.error(f"不带服务器名登录也失败: {mt5.last_error()}")
                    return False
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info:
                logger.info(f"登录成功! 账户: {account_info.login}, 服务器: {account_info.server}")
                return True
            else:
                logger.error(f"无法获取账户信息: {mt5.last_error()}")
                return False
        except Exception as e:
            logger.error(f"登录过程中出错: {e}")
            return False
    except Exception as e:
        logger.error(f"连接测试过程中出错: {e}")
        return False
    finally:
        try:
            mt5.shutdown()
        except:
            pass

def fix_mt5_config():
    """修复MT5配置"""
    print_header("MT5连接修复工具")
    
    # 测试MT5模块
    if not test_mt5_import():
        print("\n无法导入MetaTrader5模块，请先安装:")
        print("pip install MetaTrader5")
        return False
    
    # 备份配置
    backup_config()
    
    # 加载当前配置
    config = load_config()
    if not config:
        print("无法加载配置文件，请确保config.json存在且格式正确")
        return False
    
    print("\n当前配置:")
    print(f"MT5路径: {config.get('mt5_path', '未设置')}")
    print(f"MT5登录ID: {config.get('mt5_login', '未设置')}")
    print(f"MT5服务器: {config.get('mt5_server', '未设置')}")
    
    # 查找MT5可执行文件
    mt5_path = find_mt5_executable()
    if mt5_path:
        config['mt5_path'] = mt5_path
        print(f"\n已更新MT5路径: {mt5_path}")
    
    # 测试服务器名称
    print("\n正在测试当前设置...")
    if test_mt5_connection(config):
        print("\n✅ 连接测试成功！当前设置可以正常连接MT5。")
        save_config(config)
        return True
    
    print("\n❌ 连接测试失败，尝试不同的服务器名称...")
    
    # 尝试不同的服务器名称
    server_options = [
        "Tickmill-Demo", 
        "Tickmill-Live",
        "demo.mt5tickmill.com", 
        "mt5tickmill.com",
        "live.tickmill.com",
        "demo.tickmill.com",
        ""  # 空字符串表示不指定服务器
    ]
    
    original_server = config.get('mt5_server', '')
    for server in server_options:
        if server == original_server:
            continue
        
        print(f"\n尝试服务器名称: '{server}'")
        config['mt5_server'] = server
        if test_mt5_connection(config):
            print(f"\n✅ 使用服务器名称 '{server}' 连接成功!")
            save_config(config)
            return True
    
    print("\n⚠️ 所有服务器名称尝试均失败")
    print("\n请尝试以下步骤:")
    print("1. 确保MetaTrader 5正在运行且已登录")
    print("2. 检查账号和密码是否正确")
    print("3. 在MT5客户端中查看正确的服务器名称")
    print("4. 确保启用了自动交易功能")
    return False

if __name__ == "__main__":
    try:
        if fix_mt5_config():
            print("\n修复完成！请重启您的应用以应用更改。")
        else:
            print("\n修复未完成，请手动检查配置。")
    except Exception as e:
        print(f"\n修复过程中出错: {e}")
    
    input("\n按Enter键退出...")

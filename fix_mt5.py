#!/usr/bin/env python3
"""
MT5连接问题诊断和修复脚本
"""
import sys
import os
import subprocess
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_mt5_terminal():
    """检查MT5终端是否运行"""
    print("🔍 检查MT5终端进程...")
    try:
        # 检查MT5进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq terminal64.exe'], 
                              capture_output=True, text=True)
        
        if 'terminal64.exe' in result.stdout:
            print("✅ MT5终端正在运行")
            return True
        else:
            print("❌ MT5终端未运行")
            return False
    except Exception as e:
        print(f"❌ 检查MT5进程失败: {e}")
        return False

def start_mt5_terminal():
    """启动MT5终端"""
    print("🚀 尝试启动MT5终端...")
    try:
        from config import Config
        mt5_path = Config.MT5_PATH
        
        if mt5_path and os.path.exists(mt5_path):
            print(f"📍 MT5路径: {mt5_path}")
            subprocess.Popen([mt5_path])
            print("✅ MT5终端启动命令已发送")
            print("⏳ 等待MT5终端启动...")
            time.sleep(10)  # 等待10秒让MT5启动
            return True
        else:
            print("❌ MT5路径不存在或未配置")
            print("💡 请检查.env文件中的MT5_PATH配置")
            return False
    except Exception as e:
        print(f"❌ 启动MT5终端失败: {e}")
        return False

def test_mt5_connection_with_retry():
    """重试MT5连接"""
    print("🔄 测试MT5连接（带重试）...")
    try:
        import MetaTrader5 as mt5
        
        # 尝试多次连接
        for attempt in range(3):
            print(f"🔄 连接尝试 {attempt + 1}/3...")
            
            # 初始化MT5
            if mt5.initialize():
                print("✅ MT5初始化成功")
                
                # 获取终端信息
                terminal_info = mt5.terminal_info()
                if terminal_info:
                    print(f"📍 终端路径: {terminal_info.path}")
                    print(f"📊 终端版本: {terminal_info.build}")
                    print(f"🌐 连接状态: {'已连接' if terminal_info.connected else '未连接'}")
                    
                    # 如果未连接到服务器，尝试登录
                    if not terminal_info.connected:
                        print("🔑 尝试登录MT5账户...")
                        from config import Config
                        
                        login_result = mt5.login(
                            login=int(Config.MT5_LOGIN),
                            password=Config.MT5_PASSWORD,
                            server=Config.MT5_SERVER
                        )
                        
                        if login_result:
                            print("✅ MT5账户登录成功")
                            account_info = mt5.account_info()
                            if account_info:
                                print(f"💰 账户余额: ${account_info.balance:.2f}")
                                print(f"📊 账户杠杆: 1:{account_info.leverage}")
                                print(f"🏢 经纪商: {account_info.company}")
                        else:
                            error = mt5.last_error()
                            print(f"❌ MT5账户登录失败: {error}")
                    
                    mt5.shutdown()
                    return True
                else:
                    print("❌ 无法获取终端信息")
            else:
                error = mt5.last_error()
                print(f"❌ MT5初始化失败: {error}")
                
                if error[0] == -6:  # Authorization failed
                    print("💡 授权失败，可能需要手动登录MT5终端")
                elif error[0] == -1:  # Terminal not found
                    print("💡 未找到MT5终端，请检查安装路径")
            
            if attempt < 2:
                print("⏳ 等待5秒后重试...")
                time.sleep(5)
        
        print("❌ 所有连接尝试都失败了")
        return False
        
    except Exception as e:
        print(f"❌ MT5连接测试异常: {e}")
        return False

def show_mt5_troubleshooting():
    """显示MT5故障排除指南"""
    print("\n" + "="*60)
    print("🛠️ MT5连接故障排除指南")
    print("="*60)
    
    print("\n1️⃣ 检查MT5终端安装:")
    print("   - 确保MetaTrader5已正确安装")
    print("   - 检查终端路径是否正确")
    
    print("\n2️⃣ 手动登录MT5终端:")
    print("   - 打开MT5终端")
    print("   - 使用您的账户信息手动登录")
    print("   - 确保可以正常连接到服务器")
    
    print("\n3️⃣ 检查账户信息:")
    print("   - 验证MT5_LOGIN（账户号）是否正确")
    print("   - 验证MT5_PASSWORD（密码）是否正确")
    print("   - 验证MT5_SERVER（服务器）是否正确")
    
    print("\n4️⃣ 检查网络连接:")
    print("   - 确保网络连接正常")
    print("   - 检查防火墙设置")
    print("   - 确保MT5服务器可以访问")
    
    print("\n5️⃣ 重启和重试:")
    print("   - 关闭所有MT5终端")
    print("   - 重新启动MT5终端")
    print("   - 手动登录后再运行自动交易系统")

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 MT5连接问题诊断和修复")
    print("=" * 60)
    
    # 1. 检查MT5终端是否运行
    terminal_running = check_mt5_terminal()
    
    # 2. 如果未运行，尝试启动
    if not terminal_running:
        if start_mt5_terminal():
            terminal_running = check_mt5_terminal()
    
    # 3. 测试MT5连接
    if terminal_running:
        connection_success = test_mt5_connection_with_retry()
        
        if connection_success:
            print("\n🎉 MT5连接成功！")
            print("💡 现在可以重新启动交易系统")
        else:
            print("\n❌ MT5连接仍然失败")
            show_mt5_troubleshooting()
    else:
        print("\n❌ 无法启动MT5终端")
        show_mt5_troubleshooting()
    
    print("\n💡 建议操作:")
    print("1. 手动打开MT5终端并登录")
    print("2. 确保MT5终端保持运行状态")
    print("3. 重新启动交易系统")

if __name__ == '__main__':
    main()

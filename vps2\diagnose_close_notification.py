#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
诊断平仓通知问题
"""

import json
import sys
import os

def check_bark_notification_config():
    """检查Bark通知配置"""
    print("=== 检查Bark通知配置 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查基本Bark配置
        bark_url = config.get('bark_url', '')
        bark_device_key = config.get('bark_device_key', '')
        
        print(f"Bark URL: {bark_url}")
        print(f"Bark设备密钥: {'已配置' if bark_device_key else '未配置'}")
        
        # 检查通知类型配置
        bark_notifications = config.get('bark_notifications', {})
        trade_closed_enabled = bark_notifications.get('trade_closed', True)
        
        print(f"平仓通知配置: {'启用' if trade_closed_enabled else '禁用'}")
        
        if not trade_closed_enabled:
            print("❌ 问题发现：平仓通知被禁用了！")
            return False
        else:
            print("✅ 平仓通知配置正常")
            return True
            
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False

def test_bark_notification():
    """测试Bark通知功能"""
    print("\n=== 测试Bark通知功能 ===")
    
    try:
        # 导入通知模块
        import bark_notifier
        
        # 测试平仓通知
        test_trade_info = {
            'ticket': 99999999,
            'symbol': 'XAUUSD',
            'operation': 'buy',
            'volume': 0.1,
            'open_price': 1950.00,
            'close_price': 1955.00,
            'profit': 50.00,
            'points': 500
        }
        
        print("发送测试平仓通知...")
        result = bark_notifier.notify_trade_closed(test_trade_info)
        
        if result:
            print("✅ 测试平仓通知发送成功")
            return True
        else:
            print("❌ 测试平仓通知发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试通知失败: {e}")
        return False

def check_mt5_close_function():
    """检查MT5平仓函数"""
    print("\n=== 检查MT5平仓函数 ===")
    
    try:
        # 检查mt5_trader模块
        import mt5_trader
        
        # 检查close_position函数是否存在
        if hasattr(mt5_trader, 'close_position'):
            print("✅ close_position函数存在")
            
            # 读取函数源码检查是否有通知调用
            import inspect
            source = inspect.getsource(mt5_trader.close_position)
            
            if 'bark_notifier.notify_trade_closed' in source:
                print("✅ 平仓函数中包含通知调用")
                return True
            else:
                print("❌ 平仓函数中缺少通知调用")
                return False
        else:
            print("❌ close_position函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查MT5平仓函数失败: {e}")
        return False

def check_recent_logs():
    """检查最近的日志"""
    print("\n=== 检查最近的日志 ===")
    
    log_files = [
        'logs/mt5_trader.log',
        'logs/web_interface.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n检查 {log_file}...")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 查找最近的平仓相关日志
                close_logs = []
                bark_logs = []
                
                for line in lines[-1000:]:  # 检查最后1000行
                    if '平仓' in line or 'close_position' in line:
                        close_logs.append(line.strip())
                    if 'bark' in line.lower() or '通知' in line:
                        bark_logs.append(line.strip())
                
                if close_logs:
                    print(f"  找到 {len(close_logs)} 条平仓相关日志（最近5条）:")
                    for log in close_logs[-5:]:
                        print(f"    {log}")
                else:
                    print("  未找到平仓相关日志")
                
                if bark_logs:
                    print(f"  找到 {len(bark_logs)} 条通知相关日志（最近5条）:")
                    for log in bark_logs[-5:]:
                        print(f"    {log}")
                else:
                    print("  未找到通知相关日志")
                    
            except Exception as e:
                print(f"  读取日志失败: {e}")
        else:
            print(f"{log_file} 不存在")

def check_web_close_function():
    """检查Web界面平仓函数"""
    print("\n=== 检查Web界面平仓函数 ===")
    
    try:
        with open('web_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找close_position路由
        if '@app.route(\'/close_position\'' in content:
            print("✅ Web界面平仓路由存在")
            
            # 检查是否调用了mt5_trader.close_position
            if 'mt5_trader.close_position' in content:
                print("✅ Web界面调用了MT5平仓函数")
                return True
            else:
                print("❌ Web界面未调用MT5平仓函数")
                return False
        else:
            print("❌ Web界面平仓路由不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查Web界面失败: {e}")
        return False

def main():
    """主函数"""
    print("开始诊断平仓通知问题...")
    print("=" * 60)
    
    checks = [
        ("Bark通知配置检查", check_bark_notification_config),
        ("Bark通知功能测试", test_bark_notification),
        ("MT5平仓函数检查", check_mt5_close_function),
        ("Web界面平仓函数检查", check_web_close_function),
        ("日志检查", check_recent_logs),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            if check_name == "日志检查":
                check_func()  # 日志检查不返回布尔值
                results.append((check_name, True))
            else:
                result = check_func()
                results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("诊断总结:")
    
    success_count = 0
    for check_name, result in results:
        if check_name != "日志检查":
            status = "✅ 正常" if result else "❌ 异常"
            print(f"  {check_name}: {status}")
            if result:
                success_count += 1
    
    print(f"\n检查结果: {success_count}/{len(results)-1} 项正常")
    
    print("\n可能的问题原因:")
    print("1. 平仓通知配置被禁用")
    print("2. Bark服务器连接问题")
    print("3. 平仓函数执行异常")
    print("4. 通知发送失败但未记录错误")
    
    print("\n建议解决方案:")
    print("1. 确认平仓通知配置为启用状态")
    print("2. 测试Bark通知功能是否正常")
    print("3. 检查网络连接和Bark服务器状态")
    print("4. 查看详细的错误日志")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
TradingView自动交易系统启动脚本
"""
import os
import sys
import argparse
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, init_system
from utils.logger import logger

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TradingView自动交易系统')
    parser.add_argument('--host', default=os.getenv('FLASK_HOST', '0.0.0.0'), 
                       help='服务器地址')
    parser.add_argument('--port', type=int, default=int(os.getenv('FLASK_PORT', 5000)), 
                       help='服务器端口')
    parser.add_argument('--debug', action='store_true', 
                       default=os.getenv('FLASK_DEBUG', 'False').lower() == 'true',
                       help='启用调试模式')
    parser.add_argument('--env', default=os.getenv('FLASK_ENV', 'production'),
                       choices=['development', 'production', 'testing'],
                       help='运行环境')
    parser.add_argument('--no-init', action='store_true',
                       help='跳过系统初始化')
    
    args = parser.parse_args()
    
    try:
        logger.info("正在启动TradingView自动交易系统...")
        logger.info(f"运行环境: {args.env}")
        logger.info(f"服务器地址: {args.host}:{args.port}")
        logger.info(f"调试模式: {args.debug}")
        
        # 系统初始化
        if not args.no_init:
            init_system()
        
        # 创建Flask应用
        app = create_app(args.env)
        
        # 启动应用
        logger.info("系统启动完成，开始监听请求...")
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)
    finally:
        logger.info("系统已关闭")

if __name__ == '__main__':
    main()

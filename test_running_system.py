#!/usr/bin/env python3
"""
测试正在运行的系统
"""
import requests
import json
import time

def test_system():
    """测试系统各个端点"""
    base_url = "http://127.0.0.1:7000"
    
    print("🔍 测试TradingView自动交易系统...")
    print(f"📍 服务器地址: {base_url}")
    
    # 1. 测试主页
    print("\n1️⃣ 测试主页...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 主页可访问")
        else:
            print(f"⚠️ 主页状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 主页访问失败: {e}")
    
    # 2. 测试健康检查
    print("\n2️⃣ 测试健康检查API...")
    try:
        response = requests.get(f"{base_url}/api/system/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查API正常")
            print(f"📊 系统状态: {data.get('data', {}).get('status', 'unknown')}")
        else:
            print(f"⚠️ 健康检查状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
    
    # 3. 测试Webhook端点
    print("\n3️⃣ 测试Webhook端点...")
    try:
        test_alert = {
            "symbol": "XAUUSD",
            "signal_direction": "BUY",
            "timeframe": "1m",
            "price": 2000.0,
            "signal_strength": "强"
        }
        
        response = requests.post(
            f"{base_url}/webhook/tradingview",
            json=test_alert,
            timeout=10
        )
        
        if response.status_code in [200, 400]:
            print("✅ Webhook端点响应正常")
            try:
                data = response.json()
                print(f"📝 响应: {data.get('message', 'No message')}")
            except:
                print("📝 响应: 非JSON格式")
        else:
            print(f"⚠️ Webhook状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Webhook测试失败: {e}")
    
    # 4. 测试登录端点
    print("\n4️⃣ 测试登录端点...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 登录端点正常")
                token = data.get('session_token')
                
                # 5. 测试受保护的端点
                print("\n5️⃣ 测试受保护的端点...")
                headers = {'Authorization': f'Bearer {token}'}
                
                try:
                    response = requests.get(
                        f"{base_url}/api/config/symbols",
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            print("✅ 受保护端点正常")
                            symbols = data.get('data', {})
                            print(f"📊 配置的交易对数量: {len(symbols)}")
                        else:
                            print("⚠️ 受保护端点响应格式异常")
                    else:
                        print(f"⚠️ 受保护端点状态码: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 受保护端点测试失败: {e}")
            else:
                print(f"⚠️ 登录失败: {data.get('message', 'Unknown error')}")
        else:
            print(f"⚠️ 登录状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
    
    print("\n" + "="*60)
    print("🎉 系统测试完成！")
    print("💡 如果所有测试都通过，系统运行正常")
    print(f"🌐 Web管理界面: {base_url}")
    print("🔑 默认登录: admin / admin123")
    print("📡 Webhook地址: {}/webhook/tradingview".format(base_url))

if __name__ == '__main__':
    test_system()

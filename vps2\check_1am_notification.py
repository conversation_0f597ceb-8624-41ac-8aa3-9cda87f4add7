#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查1点通知问题
"""

import json
import pytz
import sqlite3
from datetime import datetime, timedelta

def check_current_config():
    """检查当前配置"""
    print('=== 当前配置检查 ===')
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        balance_monitoring = config.get('balance_monitoring', {})
        periodic_config = balance_monitoring.get('periodic_balance_notification', {})

        print(f'余额监测启用: {balance_monitoring.get("enabled", False)}')
        print(f'定时推送启用: {periodic_config.get("enabled", False)}')
        print(f'推送模式: {periodic_config.get("mode", "未设置")}')
        print(f'自定义时间点: {periodic_config.get("custom_times", [])}')
        print(f'时区设置: {periodic_config.get("timezone", "未设置")}')

        # 检查是否包含1点
        custom_times = periodic_config.get('custom_times', [])
        has_1am = '01:00' in custom_times
        print(f'\n=== 1点配置检查 ===')
        print(f'配置的时间点: {custom_times}')
        print(f'是否包含01:00: {has_1am}')
        
        return config, has_1am
        
    except Exception as e:
        print(f'读取配置失败: {e}')
        return None, False

def check_beijing_time():
    """检查北京时间"""
    print('\n=== 时间检查 ===')
    
    try:
        # 本地时间
        local_now = datetime.now()
        print(f'本地时间: {local_now.strftime("%Y-%m-%d %H:%M:%S")}')
        
        # 北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now_beijing = datetime.now(beijing_tz)
        print(f'北京时间: {now_beijing.strftime("%Y-%m-%d %H:%M:%S")}')
        print(f'当前小时: {now_beijing.hour}')
        
        # 检查是否接近1点
        if now_beijing.hour == 1:
            print('✅ 当前正好是1点时段')
        elif now_beijing.hour == 0 and now_beijing.minute >= 58:
            print('⏰ 即将到达1点')
        elif now_beijing.hour == 1 and now_beijing.minute <= 2:
            print('⏰ 刚过1点')
        else:
            print(f'❌ 当前不是1点时段（当前{now_beijing.hour}点）')
        
        return now_beijing
        
    except Exception as e:
        print(f'时间检查失败: {e}')
        return None

def check_balance_monitor_status():
    """检查余额监测服务状态"""
    print('\n=== 余额监测服务状态 ===')
    
    try:
        import balance_monitor
        
        # 检查服务是否运行
        status = balance_monitor.get_balance_monitoring_status()
        print(f'服务启用: {status.get("enabled", False)}')
        print(f'服务运行: {status.get("running", False)}')
        print(f'上次定时通知: {status.get("last_periodic_notification", "无")}')
        
        # 检查配置
        monitor_config = status.get("periodic_balance_notification", {})
        print(f'监测服务中的配置: {monitor_config}')
        
        return status
        
    except Exception as e:
        print(f'检查服务状态失败: {e}')
        return None

def check_notification_history():
    """检查通知历史"""
    print('\n=== 通知历史检查 ===')
    
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # 检查是否有balance_history表
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='balance_history'")
        if not c.fetchone():
            print('❌ balance_history表不存在')
            conn.close()
            return
        
        # 查询最近24小时的记录
        yesterday = datetime.now() - timedelta(hours=24)
        c.execute('''
            SELECT timestamp, change_type, notification_sent 
            FROM balance_history 
            WHERE timestamp >= ? 
            ORDER BY timestamp DESC
        ''', (yesterday.isoformat(),))
        
        records = c.fetchall()
        print(f'最近24小时的余额记录: {len(records)}条')
        
        for record in records[:5]:  # 显示最近5条
            timestamp = record['timestamp']
            change_type = record['change_type']
            notification_sent = record['notification_sent']
            print(f'  {timestamp} - {change_type} - 通知: {"是" if notification_sent else "否"}')
        
        conn.close()
        
    except Exception as e:
        print(f'检查通知历史失败: {e}')

def check_logs():
    """检查日志文件"""
    print('\n=== 日志检查 ===')
    
    log_files = [
        'logs/web_interface.log',
        'logs/mt5_trader.log'
    ]
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 查找最近的余额监测相关日志
            balance_logs = []
            for line in lines[-1000:]:  # 检查最后1000行
                if '余额' in line or 'balance' in line.lower() or '定时' in line:
                    balance_logs.append(line.strip())
            
            print(f'\n{log_file}中的余额相关日志（最近5条）:')
            for log in balance_logs[-5:]:
                print(f'  {log}')
                
        except Exception as e:
            print(f'读取{log_file}失败: {e}')

def test_notification_logic():
    """测试通知逻辑"""
    print('\n=== 测试通知逻辑 ===')
    
    try:
        import balance_monitor
        
        # 模拟1点时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        test_time = datetime.now(beijing_tz).replace(hour=1, minute=0, second=30)
        
        print(f'模拟时间: {test_time.strftime("%Y-%m-%d %H:%M:%S")}')
        
        # 检查配置
        balance_monitor.balance_monitor.load_config()
        config = balance_monitor.balance_monitor.config
        periodic_config = config.get('balance_monitoring', {}).get('periodic_balance_notification', {})
        custom_times = periodic_config.get('custom_times', [])
        
        print(f'配置的时间点: {custom_times}')
        
        # 测试是否应该推送
        should_push = False
        for time_str in custom_times:
            try:
                hour, minute = map(int, time_str.split(':'))
                target_time = test_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
                time_diff = abs((test_time - target_time).total_seconds())
                
                print(f'检查时间点 {time_str}:')
                print(f'  目标时间: {target_time.strftime("%H:%M:%S")}')
                print(f'  时间差: {time_diff}秒')
                print(f'  是否在2分钟内: {time_diff <= 120}')
                
                if time_diff <= 120:
                    should_push = True
                    print(f'  ✅ 应该在{time_str}推送')
                    break
            except:
                continue
        
        if not should_push:
            print('❌ 根据当前逻辑，1点不应该推送')
        
        return should_push
        
    except Exception as e:
        print(f'测试通知逻辑失败: {e}')
        return False

def manual_test_notification():
    """手动测试1点通知"""
    print('\n=== 手动测试1点通知 ===')
    
    try:
        import balance_monitor
        
        # 获取账户信息
        account_info = balance_monitor.balance_monitor.get_account_info()
        if not account_info:
            print('❌ 无法获取账户信息')
            return False
        
        print('✅ 账户信息获取成功')
        print(f'当前余额: ${account_info["balance"]:.2f}')
        
        # 手动发送定时通知
        print('发送测试通知...')
        balance_monitor.balance_monitor.send_periodic_balance_notification()
        print('✅ 测试通知已发送')
        
        return True
        
    except Exception as e:
        print(f'手动测试失败: {e}')
        return False

def main():
    """主函数"""
    print('1点通知问题诊断')
    print('=' * 60)
    
    # 检查配置
    config, has_1am = check_current_config()
    
    # 检查时间
    beijing_time = check_beijing_time()
    
    # 检查服务状态
    service_status = check_balance_monitor_status()
    
    # 检查通知历史
    check_notification_history()
    
    # 检查日志
    check_logs()
    
    # 测试通知逻辑
    should_push = test_notification_logic()
    
    # 总结问题
    print('\n' + '=' * 60)
    print('问题诊断总结:')
    
    issues = []
    
    if not has_1am:
        issues.append('❌ 配置中没有包含01:00时间点')
    else:
        print('✅ 配置中包含01:00时间点')
    
    if service_status and not service_status.get('running', False):
        issues.append('❌ 余额监测服务未运行')
    elif service_status and service_status.get('running', False):
        print('✅ 余额监测服务正在运行')
    
    if not should_push:
        issues.append('❌ 当前通知逻辑可能有问题')
    else:
        print('✅ 通知逻辑正常')
    
    if issues:
        print('\n发现的问题:')
        for issue in issues:
            print(f'  {issue}')
    else:
        print('\n✅ 配置和服务状态看起来正常')
    
    # 建议解决方案
    print('\n建议解决方案:')
    print('1. 确认Web界面中已选择01:00时间点')
    print('2. 检查余额监测服务是否正在运行')
    print('3. 手动测试通知功能')
    print('4. 查看详细日志了解具体错误')
    
    # 手动测试
    print('\n是否进行手动测试? (输入y确认)')
    # manual_test_notification()

if __name__ == "__main__":
    main()

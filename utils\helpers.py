"""
辅助工具函数
"""
import re
import json
from datetime import datetime, timedelta
from config import Config

def normalize_symbol(symbol):
    """
    标准化交易对符号
    
    Args:
        symbol: 原始交易对符号
    
    Returns:
        标准化的交易对符号
    """
    if not symbol:
        return symbol
    
    # 创建反向映射字典
    reverse_mapping = {}
    for standard_symbol, variants in Config.SYMBOL_MAPPING.items():
        for variant in variants:
            reverse_mapping[variant.upper()] = standard_symbol
    
    symbol_upper = symbol.upper().strip()
    return reverse_mapping.get(symbol_upper, symbol_upper)

def normalize_signal_direction(signal):
    """
    标准化信号方向（支持大小写）
    
    Args:
        signal: 原始信号方向
    
    Returns:
        标准化的信号方向 (BUY/SELL)
    """
    if not signal:
        return signal
    
    # 先去除空格，保持原始大小写进行匹配
    signal_clean = str(signal).strip()
    
    # 直接匹配（保持大小写）
    if signal_clean in Config.SIGNAL_DIRECTION_MAPPING:
        return Config.SIGNAL_DIRECTION_MAPPING[signal_clean]
    
    # 如果直接匹配失败，尝试大写匹配（兼容性处理）
    signal_upper = signal_clean.upper()
    return Config.SIGNAL_DIRECTION_MAPPING.get(signal_upper, signal_upper)

def validate_alert_data(alert_data):
    """
    验证警报数据的完整性和有效性

    Args:
        alert_data: 警报数据字典

    Returns:
        (is_valid, error_message)
    """
    # 检查交易对字段（支持多种字段名）
    symbol = alert_data.get("symbol") or alert_data.get("交易对")
    if not symbol:
        return False, "缺少必需字段: symbol/交易对"

    # 检查信号方向字段（支持多种字段名）
    signal = (
        alert_data.get("signal_direction") or
        alert_data.get("事件") or
        alert_data.get("信号") or
        alert_data.get("signal")
    )
    if not signal:
        return False, "缺少必需字段: signal_direction/事件/信号"

    # 检查时间周期字段（支持多种字段名）
    timeframe = alert_data.get("timeframe") or alert_data.get("周期")
    if not timeframe:
        return False, "缺少必需字段: timeframe/周期"

    # 验证交易对
    normalized_symbol = normalize_symbol(symbol)
    if normalized_symbol not in Config.SUPPORTED_SYMBOLS:
        return False, f"不支持的交易对: {symbol}"
    
    # 验证信号方向
    normalized_signal = normalize_signal_direction(signal)
    if normalized_signal not in ['BUY', 'SELL']:
        return False, f"无效的信号方向: {signal}"

    # 验证时间框架
    valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1']
    if timeframe not in valid_timeframes:
        return False, f"无效的时间框架: {timeframe}"
    
    return True, None

def format_currency(amount, currency='USD'):
    """
    格式化货币金额
    
    Args:
        amount: 金额
        currency: 货币符号
    
    Returns:
        格式化的货币字符串
    """
    if amount is None:
        return "N/A"
    
    try:
        amount = float(amount)
        if currency == 'USD':
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    except (ValueError, TypeError):
        return str(amount)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象或字符串
        format_str: 格式字符串
    
    Returns:
        格式化的日期时间字符串
    """
    if dt is None:
        return "N/A"
    
    try:
        if isinstance(dt, str):
            # 尝试解析ISO格式
            if 'T' in dt:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        
        return dt.strftime(format_str)
    except (ValueError, TypeError):
        return str(dt)

def calculate_duration(start_time, end_time=None):
    """
    计算时间间隔
    
    Args:
        start_time: 开始时间
        end_time: 结束时间（默认为当前时间）
    
    Returns:
        时间间隔字符串
    """
    if end_time is None:
        end_time = datetime.now()
    
    try:
        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        if isinstance(end_time, str):
            end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        duration = end_time - start_time
        
        days = duration.days
        hours, remainder = divmod(duration.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分钟"
        elif hours > 0:
            return f"{hours}小时 {minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟 {seconds}秒"
        else:
            return f"{seconds}秒"
    
    except (ValueError, TypeError):
        return "N/A"

def safe_float(value, default=0.0):
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
    
    Returns:
        浮点数
    """
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
    
    Returns:
        整数
    """
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def generate_alert_id():
    """
    生成唯一的警报ID
    
    Returns:
        警报ID字符串
    """
    import uuid
    return str(uuid.uuid4())

def is_market_hours():
    """
    检查是否在市场交易时间内
    
    Returns:
        布尔值
    """
    # 这里可以根据需要实现具体的市场时间检查逻辑
    # 目前返回True，表示24小时交易
    return True

def sanitize_filename(filename):
    """
    清理文件名，移除非法字符
    
    Args:
        filename: 原始文件名
    
    Returns:
        清理后的文件名
    """
    # 移除或替换非法字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除多余的空格和点
    filename = re.sub(r'\s+', '_', filename.strip())
    filename = filename.strip('.')
    
    return filename or 'unnamed'

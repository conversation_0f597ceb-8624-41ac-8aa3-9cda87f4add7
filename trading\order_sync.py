"""
MT5订单同步模块
用于同步MT5订单状态、盈亏和平仓信息
"""

import MetaTrader5 as mt5
import threading
import time
from datetime import datetime, timedelta
from database.manager import db_manager
from mt5.connection import mt5_connection
from utils.logger import logger


class OrderSyncManager:
    """MT5订单同步管理器"""
    
    def __init__(self, sync_interval=30):
        self.sync_interval = sync_interval  # 同步间隔（秒）
        self.syncing = False
        self.sync_thread = None
        self.lock = threading.Lock()
    
    def start_sync(self):
        """启动订单同步"""
        with self.lock:
            if self.syncing:
                logger.warning("订单同步已在运行")
                return False
            
            self.syncing = True
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
            logger.info("MT5订单同步已启动")
            return True
    
    def stop_sync(self):
        """停止订单同步"""
        with self.lock:
            if not self.syncing:
                logger.warning("订单同步未在运行")
                return False
            
            self.syncing = False
            if self.sync_thread and self.sync_thread.is_alive():
                self.sync_thread.join(timeout=10)
            
            logger.info("MT5订单同步已停止")
            return True
    
    def is_syncing(self):
        """检查是否正在同步"""
        return self.syncing and self.sync_thread and self.sync_thread.is_alive()
    
    def _sync_loop(self):
        """同步主循环"""
        while self.syncing:
            try:
                # 检查MT5连接
                if not mt5_connection.check_connection():
                    logger.debug("MT5未连接，跳过本次同步")
                    time.sleep(self.sync_interval)
                    continue
                
                # 同步开仓订单
                self._sync_open_orders()
                
                # 同步历史订单（检查平仓）
                self._sync_closed_orders()
                
            except Exception as e:
                logger.error(f"订单同步循环异常: {e}")
            
            # 等待下次同步
            time.sleep(self.sync_interval)
        
        logger.info("订单同步线程结束")
    
    def _sync_open_orders(self):
        """同步开仓订单状态"""
        try:
            # 获取数据库中的开仓订单
            open_trades = db_manager.execute_query(
                """SELECT id, mt5_ticket, symbol, direction, lot_size, open_price, 
                          stop_loss_usd, take_profit_usd, open_time
                   FROM trades 
                   WHERE status = 'OPEN' AND mt5_ticket IS NOT NULL"""
            )
            
            if not open_trades:
                return
            
            logger.debug(f"同步 {len(open_trades)} 个开仓订单")
            
            for trade in open_trades:
                trade_id, mt5_ticket, symbol, direction, lot_size, open_price, sl_usd, tp_usd, open_time = trade
                
                # 获取MT5中的持仓信息
                position = self._get_mt5_position(mt5_ticket)
                
                if position is None:
                    # 持仓不存在，可能已平仓，检查历史
                    self._check_closed_position(trade_id, mt5_ticket)
                else:
                    # 更新持仓信息
                    self._update_position_info(trade_id, position)
                    
        except Exception as e:
            logger.error(f"同步开仓订单失败: {e}")
    
    def _sync_closed_orders(self):
        """同步已平仓订单"""
        try:
            # 获取最近24小时的历史交易
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            
            # 获取MT5历史交易
            deals = mt5.history_deals_get(start_time, end_time)
            
            if deals is None:
                return
            
            logger.debug(f"检查 {len(deals)} 个历史交易")
            
            for deal in deals:
                if deal.entry == mt5.DEAL_ENTRY_OUT:  # 平仓交易
                    self._process_close_deal(deal)
                    
        except Exception as e:
            logger.error(f"同步历史订单失败: {e}")
    
    def _get_mt5_position(self, ticket):
        """获取MT5持仓信息"""
        try:
            positions = mt5.positions_get(ticket=ticket)
            if positions and len(positions) > 0:
                position = positions[0]
                return {
                    "ticket": position.ticket,
                    "symbol": position.symbol,
                    "type": "BUY" if position.type == mt5.POSITION_TYPE_BUY else "SELL",
                    "volume": position.volume,
                    "price_open": position.price_open,
                    "price_current": position.price_current,
                    "profit": position.profit,
                    "swap": position.swap,
                    "comment": position.comment,
                    "time": datetime.fromtimestamp(position.time)
                }
            return None
        except Exception as e:
            logger.error(f"获取MT5持仓信息失败: {e}")
            return None
    
    def _update_position_info(self, trade_id, position):
        """更新持仓信息"""
        try:
            current_profit = position["profit"] + position["swap"]
            
            db_manager.execute_query(
                """UPDATE trades 
                   SET profit_usd = ?, last_sync_time = ?, updated_at = ?
                   WHERE id = ?""",
                (current_profit, datetime.now(), datetime.now(), trade_id)
            )
            
        except Exception as e:
            logger.error(f"更新持仓信息失败: {e}")
    
    def _check_closed_position(self, trade_id, mt5_ticket):
        """检查已平仓的持仓"""
        try:
            # 查找平仓交易
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)  # 查找最近7天
            
            deals = mt5.history_deals_get(start_time, end_time)
            if deals is None:
                return
            
            for deal in deals:
                if (deal.position_id == mt5_ticket and 
                    deal.entry == mt5.DEAL_ENTRY_OUT):
                    
                    self._process_close_deal(deal, trade_id)
                    break
                    
        except Exception as e:
            logger.error(f"检查平仓持仓失败: {e}")
    
    def _process_close_deal(self, deal, trade_id=None):
        """处理平仓交易"""
        try:
            # 如果没有提供trade_id，从数据库查找
            if trade_id is None:
                result = db_manager.execute_query(
                    "SELECT id FROM trades WHERE mt5_ticket = ? AND status = 'OPEN'",
                    (deal.position_id,)
                )
                if not result:
                    return
                trade_id = result[0][0]
            
            # 确定平仓原因
            close_reason = self._determine_close_reason(deal)
            
            # 更新交易记录
            close_time = datetime.fromtimestamp(deal.time)
            profit = deal.profit + deal.swap
            
            db_manager.execute_query(
                """UPDATE trades 
                   SET status = 'CLOSED', 
                       close_time = ?, 
                       close_price = ?, 
                       profit_usd = ?, 
                       close_reason = ?,
                       last_sync_time = ?,
                       updated_at = ?
                   WHERE id = ?""",
                (close_time, deal.price, profit, close_reason, 
                 datetime.now(), datetime.now(), trade_id)
            )
            
            logger.info(f"订单#{deal.position_id}已平仓: {close_reason}, 盈亏: ${profit:.2f}")
            
        except Exception as e:
            logger.error(f"处理平仓交易失败: {e}")
    
    def _determine_close_reason(self, deal):
        """确定平仓原因"""
        try:
            comment = deal.comment.lower() if deal.comment else ""
            
            # 根据注释判断平仓原因
            if "sl" in comment or "stop loss" in comment or "止损" in comment:
                return "金额止损"
            elif "tp" in comment or "take profit" in comment or "止盈" in comment:
                return "金额止盈"
            elif "timeout" in comment or "超时" in comment:
                return "超时未收到新警报通知"
            elif "manual" in comment or "手动" in comment:
                return "手动平仓"
            else:
                return "系统平仓"
                
        except Exception as e:
            logger.error(f"确定平仓原因失败: {e}")
            return "未知原因"
    
    def sync_single_order(self, mt5_ticket):
        """同步单个订单"""
        try:
            if not mt5_connection.check_connection():
                return False
            
            # 检查是否为开仓订单
            position = self._get_mt5_position(mt5_ticket)
            if position:
                # 更新开仓订单信息
                result = db_manager.execute_query(
                    "SELECT id FROM trades WHERE mt5_ticket = ? AND status = 'OPEN'",
                    (mt5_ticket,)
                )
                if result:
                    self._update_position_info(result[0][0], position)
                    return True
            else:
                # 检查是否已平仓
                result = db_manager.execute_query(
                    "SELECT id FROM trades WHERE mt5_ticket = ? AND status = 'OPEN'",
                    (mt5_ticket,)
                )
                if result:
                    self._check_closed_position(result[0][0], mt5_ticket)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"同步单个订单失败: {e}")
            return False


# 创建全局订单同步管理器实例
order_sync_manager = OrderSyncManager()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MT5连接重置工具 - 强制重启MT5连接
"""

import MetaTrader5 as mt5
import sys
import os
import json
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    filename='logs/restart_mt5.log',
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """重启MT5连接"""
    try:
        logger.info("开始重置MT5连接...")
        
        # 关闭现有连接
        mt5.shutdown()
        logger.info("已关闭现有MT5连接")
        
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取MT5路径
        mt5_path = config.get('mt5_path', 'C:\\Program Files\\MetaTrader 5\\terminal64.exe')
        login = int(config.get('mt5_login', '0'))
        password = config.get('mt5_password', '')
        server = config.get('mt5_server', '')
        
        logger.info(f"MT5路径: {mt5_path}")
        logger.info(f"MT5账号: {login}")
        logger.info(f"MT5服务器: {server}")
        
        # 初始化连接
        init_result = mt5.initialize(path=mt5_path)
        if not init_result:
            error = mt5.last_error()
            logger.error(f"MT5初始化失败: {error}")
            return False
        
        logger.info("MT5初始化成功，准备登录账户")
        
        # 登录账户
        if not mt5.login(login=login, password=password, server=server):
            error = mt5.last_error()
            logger.error(f"MT5登录失败: {error}")
            return False
        
        # 检查账户信息确认登录成功
        account_info = mt5.account_info()
        if account_info:
            logger.info(f"成功登录到MT5账户: {account_info.login}, 账户名: {account_info.name}")
            logger.info(f"账户服务器: {account_info.server}, 货币: {account_info.currency}")
            logger.info(f"账户余额: {account_info.balance}, 净值: {account_info.equity}")
        else:
            logger.warning("登录成功但无法获取账户信息")
        
        # 检查是否启用了自动交易
        terminal_info = mt5.terminal_info()
        if terminal_info is not None and not terminal_info.trade_allowed:
            logger.warning("MT5自动交易功能未启用，请在MT5界面中启用自动交易")
        else:
            logger.info("MT5自动交易功能已启用")
        
        logger.info("MT5连接重置成功")
        return True
        
    except Exception as e:
        logger.error(f"MT5连接重置失败: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
